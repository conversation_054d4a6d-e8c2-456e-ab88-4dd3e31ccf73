---
description: 案件属性
globs: 
alwaysApply: false
---

以下是所有案件的类型和对应的排序
* 民事案件，类型代号：民
* 刑事案件，类型代号：刑
* 行政案件，类型代号：行
* 仲裁案件，类型代号：民
* 其他案件，类型代号：非诉

案件的编号格式，{}是需要根据实际数字替代的内容，顺序编号每年从1开始，每个类型都独立编号。
（{年份}）广承律{类型代号}代{顺序编号}号 
比如，2025年，的民事案件，顺序编号0301，那么案件完整编号如下：
（2025）广承律民代0301号 

一个案件需要在主任审批后，才会有案件编号。

案件会依次进入以下状态

* 创建
* 申请中
* 行政审批中
* 主任审批中
* 办理委托手续中
* 办案中
* 申请结案中
* 已通过主任审批结案
* 已归档

其中
行政审批需要行政人员来执行，用于审批“是否重大敏感事件”和“是否有利益冲突”，
主任审批需要主任来执行，第一次主任审批用于确定“是否可以开始代理该案件”，
第二次主任审批用于确定“是否可以结案”。

状态从“行政审批中”切换到“主任审批中”，需要经过行政人员审批，如果行政人员审批失败，案件会直接进入“状态不变”。
状态从“主任审批中”切换到“办理委托手续中”，需要经过主任审批，如果主任审批失败，案件会直接进入“已归档”。
状态从“申请结案中”切换到“已通过主任审批结案”，需要经过主任审批，如果主任审批失败，案件维持在“申请结案中”。

案件的


除了以上两个状态之外，案件所有人可以自行设置把案件从上一个状态变成下一个状态。
