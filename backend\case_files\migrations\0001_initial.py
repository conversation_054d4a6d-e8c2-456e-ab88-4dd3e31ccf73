# Generated by Django 5.0.3 on 2025-04-21 15:13

import case_files.models
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("cases", "0016_alter_legalentity_representative_name"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="CaseFileFolder",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255, verbose_name="文件夹名称")),
                (
                    "path",
                    models.CharField(
                        help_text="从根目录到当前文件夹的路径",
                        max_length=1000,
                        verbose_name="路径",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="folders",
                        to="cases.case",
                        verbose_name="所属案件",
                    ),
                ),
                (
                    "parent",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="children",
                        to="case_files.casefilefolder",
                        verbose_name="父文件夹",
                    ),
                ),
            ],
            options={
                "verbose_name": "案件文件夹",
                "verbose_name_plural": "案件文件夹",
                "unique_together": {("case", "parent", "name")},
            },
        ),
        migrations.CreateModel(
            name="CaseFile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        upload_to=case_files.models.case_file_path, verbose_name="文件"
                    ),
                ),
                (
                    "file_type",
                    models.CharField(
                        choices=[
                            ("doc", "Word文档(doc)"),
                            ("docx", "Word文档(docx)"),
                            ("xls", "Excel表格(xls)"),
                            ("xlsx", "Excel表格(xlsx)"),
                            ("pdf", "PDF文档"),
                            ("jpg", "JPG图片"),
                            ("jpeg", "JPEG图片"),
                            ("png", "PNG图片"),
                            ("other", "其他文件"),
                        ],
                        max_length=10,
                        verbose_name="文件类型",
                    ),
                ),
                (
                    "original_filename",
                    models.CharField(max_length=255, verbose_name="原始文件名"),
                ),
                (
                    "uploaded_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="上传时间"),
                ),
                (
                    "order",
                    models.IntegerField(
                        default=0, help_text="用于PDF合并时的顺序", verbose_name="排序"
                    ),
                ),
                (
                    "case",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="files",
                        to="cases.case",
                        verbose_name="所属案件",
                    ),
                ),
                (
                    "uploaded_by",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="uploaded_case_files",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="上传者",
                    ),
                ),
                (
                    "folder",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="files",
                        to="case_files.casefilefolder",
                        verbose_name="所属文件夹",
                    ),
                ),
            ],
            options={
                "verbose_name": "案件文件",
                "verbose_name_plural": "案件文件",
                "ordering": ["order", "uploaded_at"],
            },
        ),
    ]
