services:
  backend:
    build:
      context: "./${RUNTIME_PATH}"
      dockerfile: deploy/Dockerfile.backend
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/mediafiles
      - "./${RUNTIME_PATH}/fonts:/app/fonts"
    environment:
      - DJANGO_SETTINGS_MODULE=law_firm.settings_prod
    env_file:
      - ./.env

  frontend:
    build:
      context: "./${RUNTIME_PATH}"
      dockerfile: deploy/Dockerfile.frontend
    environment:
      - DJANGO_SETTINGS_MODULE=law_firm.settings_prod
    volumes:
      - static_volume:/var/www/staticfiles:ro
    ports:
      - 80:80
      - 443:443
    depends_on:
      - backend

volumes:
  postgres_data:
  static_volume:
  media_volume: