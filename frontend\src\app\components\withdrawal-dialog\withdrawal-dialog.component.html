<h2 mat-dialog-title>申请提款</h2>
<form [formGroup]="form" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>提款金额</mat-label>
        <input matInput formControlName="amount" type="number" step="0.01" min="0.01" required>
        <mat-error *ngIf="form.get('amount')?.hasError('required')">
          请输入提款金额
        </mat-error>
        <mat-error *ngIf="form.get('amount')?.hasError('min')">
          提款金额必须大于0
        </mat-error>
      </mat-form-field>
    </div>
    
    <div class="form-row">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>备注</mat-label>
        <textarea matInput formControlName="remarks" placeholder="请输入备注信息" rows="3"></textarea>
      </mat-form-field>
    </div>
  </mat-dialog-content>
  
  <mat-dialog-actions align="end">
    <button mat-button type="button" [mat-dialog-close]="null">取消</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid">提交申请</button>
  </mat-dialog-actions>
</form>
