import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { PricingService, RiskPricingResult } from '../../../services/pricing.service';

@Component({
  selector: 'app-risk-pricing',
  templateUrl: './risk-pricing.component.html',
  styleUrls: ['./risk-pricing.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatListModule
  ]
})
export class RiskPricingComponent {
  amount: number = 500000; // 默认标的额
  result: RiskPricingResult | null = null;
  
  constructor(private pricingService: PricingService) {
    this.calculateFee();
  }
  
  calculateFee(): void {
    this.result = this.pricingService.calculateRiskPricingFee(this.amount);
  }
  
  updateAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.amount = value > 0 ? value : 1;
    this.calculateFee();
  }
} 