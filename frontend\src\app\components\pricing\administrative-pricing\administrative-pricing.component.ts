import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { PricingService, CivilCasePricingResult } from '../../../services/pricing.service';

@Component({
  selector: 'app-administrative-pricing',
  templateUrl: './administrative-pricing.component.html',
  styleUrls: ['./administrative-pricing.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatRadioModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatListModule
  ]
})
export class AdministrativePricingComponent {
  amount: number = 100000; // 默认标的额
  involveProperty: boolean = true; // 默认涉及财产关系
  result: CivilCasePricingResult | null = null;
  
  constructor(private pricingService: PricingService) {
    this.calculateFee();
  }
  
  calculateFee(): void {
    this.result = this.pricingService.calculateAdministrativeCaseFee(this.amount, this.involveProperty);
  }
  
  updateAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.amount = value > 0 ? value : 0;
    this.calculateFee();
  }
  
  updateCaseType(involveProperty: boolean): void {
    this.involveProperty = involveProperty;
    this.calculateFee();
  }
} 