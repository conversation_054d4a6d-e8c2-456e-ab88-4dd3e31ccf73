<mat-toolbar color="primary">
  <a mat-button routerLink="/" class="brand-name">{{ firmInfoService.firmName }}</a>

  <div class="nav-links" *ngIf="authService.isLoggedIn()">
    <a mat-button routerLink="/parties" routerLinkActive="active">
      <mat-icon>people</mat-icon> 当事人管理
    </a>
    <a mat-button routerLink="/cases" routerLinkActive="active">
      <mat-icon>assignment</mat-icon> 案件管理
    </a>
    <a mat-button routerLink="/templates" routerLinkActive="active">
      <mat-icon>description</mat-icon> 文档模板
    </a>
    <a mat-button routerLink="/pricing" routerLinkActive="active">
      <mat-icon>calculate</mat-icon> 收费计算
    </a>
    <a mat-button routerLink="/court-fee" routerLinkActive="active">
      <mat-icon>account_balance</mat-icon> 诉讼费计算
    </a>
    <a mat-button routerLink="/finance" routerLinkActive="active">
      <mat-icon>account_balance_wallet</mat-icon> 财务管理
    </a>
    <a mat-button routerLink="/seal-usage" routerLinkActive="active">
      <mat-icon>verified</mat-icon> 公章使用登记
    </a>
    <a mat-button routerLink="/legacy-cases/import" routerLinkActive="active" *ngIf="authService.canAdminApproveCases()">
      <mat-icon>history</mat-icon> 历史案件导入
    </a>
  </div>

  <span class="spacer"></span>

  <ng-container *ngIf="authService.user$ | async as user">
    <button mat-button [matMenuTriggerFor]="userMenu" *ngIf="authService.isLoggedIn()">
      <mat-icon>account_circle</mat-icon>
      {{ getUserDisplayName(user) }}
    </button>
    <mat-menu #userMenu="matMenu">
      <button mat-menu-item [routerLink]="['/users/me']">
        <mat-icon>person</mat-icon>
        <span>我的资料</span>
      </button>
      <button mat-menu-item (click)="logout()">
        <mat-icon>exit_to_app</mat-icon>
        <span>退出登录</span>
      </button>
    </mat-menu>
  </ng-container>

  <a mat-button routerLink="/login" *ngIf="!authService.isLoggedIn()">
    <mat-icon>login</mat-icon> 登录
  </a>
</mat-toolbar>
