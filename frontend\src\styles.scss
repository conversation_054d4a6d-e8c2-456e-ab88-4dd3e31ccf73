/* You can add global styles to this file, and also import other style files */
@use "@angular/material" as mat;
@import "ng-zorro-antd/ng-zorro-antd.min.css";

@include mat.core();

html,
body {
  height: 100%;
}
body {
  margin: 0;
  font-family: <PERSON><PERSON>, "Helvetica Neue", sans-serif;
}

@include mat.strong-focus-indicators();

$my-primary: mat.define-palette(mat.$blue-palette, 500);
$my-accent: mat.define-palette(mat.$blue-grey-palette, A200, A100, A400);

$my-theme: mat.define-light-theme((
 color: (
   primary: $my-primary,
   accent: $my-accent,
 )
));

@include mat.all-component-themes($my-theme);
@include mat.strong-focus-indicators-theme($my-theme);
