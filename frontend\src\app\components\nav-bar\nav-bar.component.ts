import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';

import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatToolbarModule } from '@angular/material/toolbar';

import { AuthService } from '../../services/auth.service';
import { FirmInfoService } from '../../services/firm-info.service';
import { getUserDisplayName } from '../../utils/user.utils';

@Component({
  selector: 'app-nav-bar',
  templateUrl: './nav-bar.component.html',
  styleUrls: ['./nav-bar.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatToolbarModule,
    MatButtonModule,
    MatMenuModule,
    MatIconModule,
  ]
})
export class NavBarComponent {
  getUserDisplayName = getUserDisplayName;

  constructor(
    public authService: AuthService,
    public firmInfoService: FirmInfoService
  ) {}

  logout() {
    this.authService.logout();
  }
}
