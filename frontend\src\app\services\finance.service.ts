import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';
import { FinanceRecord, FinanceRecordCreate } from '../interfaces/finance.interface';
import { OfficeExpense, OfficeExpenseCreateDto } from '../interfaces/office-expense.interface';
import { User, Case } from '../interfaces/case.interface';

export interface LawyerBalance {
  lawyer_id: number;
  lawyer_name: string;
  balance: number;
  total_income: number;
  total_expense: number;
  income_count: number;
  expense_count: number;
  has_debt: boolean;
  debt_amount: number;
}

@Injectable({
  providedIn: 'root'
})
export class FinanceService {
  private apiUrl = `${environment.apiUrl}/finance`;

  constructor(private http: HttpClient) {}

  // ===== 财务收支记录相关方法 =====
  
  // 获取财务记录列表
  getFinanceRecords(params?: {
    lawyer_id?: number;
    account_type?: string;
    case_id?: number;
    start_date?: string;
    end_date?: string;
    keyword?: string;
  }): Observable<FinanceRecord[]> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null && value !== '') {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }
    
    return this.http.get<FinanceRecord[]>(`${this.apiUrl}/records/`, { params: httpParams });
  }

  // 获取单个财务记录
  getFinanceRecord(id: number): Observable<FinanceRecord> {
    return this.http.get<FinanceRecord>(`${this.apiUrl}/records/${id}/`);
  }

  // 创建财务记录
  createFinanceRecord(record: FinanceRecordCreate): Observable<FinanceRecord> {
    return this.http.post<FinanceRecord>(`${this.apiUrl}/records/`, record);
  }

  // 更新财务记录
  updateFinanceRecord(id: number, record: Partial<FinanceRecordCreate>): Observable<FinanceRecord> {
    return this.http.patch<FinanceRecord>(`${this.apiUrl}/records/${id}/`, record);
  }

  // 删除财务记录
  deleteFinanceRecord(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/records/${id}/`);
  }

  // 获取当前用户的财务余额
  getMyBalance(): Observable<LawyerBalance> {
    return this.http.get<LawyerBalance>(`${this.apiUrl}/records/my_balance/`);
  }

  // 获取所有律师的财务余额（仅财务人员可访问）
  getAllBalances(): Observable<LawyerBalance[]> {
    return this.http.get<LawyerBalance[]>(`${this.apiUrl}/records/all_balances/`);
  }

  // ===== 办公费用相关方法 =====
  
  // 获取办公费用列表
  getOfficeExpenses(params?: {
    keyword?: string;
    start_date?: string;
    end_date?: string;
    approval_status?: string;
    applicant_id?: number;
  }): Observable<OfficeExpense[]> {
    let httpParams = new HttpParams();
    
    if (params) {
      Object.keys(params).forEach(key => {
        const value = params[key as keyof typeof params];
        if (value !== undefined && value !== null && value !== '') {
          httpParams = httpParams.set(key, value.toString());
        }
      });
    }
    
    return this.http.get<OfficeExpense[]>(`${this.apiUrl}/expenses/`, { params: httpParams });
  }

  // 创建办公费用
  createOfficeExpense(expense: OfficeExpenseCreateDto): Observable<OfficeExpense> {
    return this.http.post<OfficeExpense>(`${this.apiUrl}/expenses/`, expense);
  }

  // 更新办公费用
  updateOfficeExpense(id: number, expense: Partial<OfficeExpenseCreateDto>): Observable<OfficeExpense> {
    return this.http.patch<OfficeExpense>(`${this.apiUrl}/expenses/${id}/`, expense);
  }

  // 审批办公费用
  approveOfficeExpense(id: number): Observable<OfficeExpense> {
    return this.http.post<OfficeExpense>(`${this.apiUrl}/expenses/${id}/approve/`, {});
  }

  // 打印办公费用
  printOfficeExpense(id: number): Observable<OfficeExpense[]> {
    return this.http.post<OfficeExpense[]>(`${this.apiUrl}/expenses/${id}/print/`, {});
  }

  // 删除办公费用
  deleteOfficeExpense(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/expenses/${id}/`);
  }

  // 打开打印窗口
  openPrintWindow(expenses: OfficeExpense[]): void {
    console.log('开始打印，费用记录数量：', expenses.length);
    
    // 检查是否有费用记录
    if (!expenses || expenses.length === 0) {
      console.error('没有费用记录可以打印');
      return;
    }
    
    // 创建打印内容
    const printContent = this.generatePrintContent(expenses);
    console.log('打印内容生成完成');
    
    // 打开新窗口进行打印
    const printWindow = window.open('', '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
    if (printWindow) {
      console.log('打印窗口打开成功');
      
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();
      
      // 等待内容加载完成后执行打印
      setTimeout(() => {
        console.log('准备执行打印');
        try {
          printWindow.print();
          console.log('打印命令已执行');
          
          // 打印完成后关闭窗口（给用户一些时间查看）
          setTimeout(() => {
            if (!printWindow.closed) {
              printWindow.close();
            }
          }, 1000);
        } catch (error) {
          console.error('打印执行失败：', error);
        }
      }, 500);
    } else {
      console.error('无法打开打印窗口，可能被浏览器阻止了弹窗');
      alert('无法打开打印窗口，请检查浏览器是否阻止了弹窗，或者手动允许此网站的弹窗。');
    }
  }

  // 生成打印内容
  private generatePrintContent(expenses: OfficeExpense[]): string {
    console.log('开始生成打印内容，费用记录：', expenses);
    
    const today = new Date().toLocaleDateString('zh-CN');
    
    let tableRows = '';
    expenses.forEach((expense, index) => {
      const expenseDate = new Date(expense.expense_date).toLocaleDateString('zh-CN');
      const createdAt = new Date(expense.created_at).toLocaleDateString('zh-CN');
      const applicantName = this.getUserDisplayName(expense.applicant);
      const approverName = expense.approver ? this.getUserDisplayName(expense.approver) : '';
      
      // 确保 amount 是数字类型（后端现在应该返回数字，但保留兼容性）
      const amount = typeof expense.amount === 'number' ? expense.amount : parseFloat(expense.amount) || 0;
      
      tableRows += `
        <tr>
          <td>${index + 1}</td>
          <td>${expenseDate}</td>
          <td>${applicantName}</td>
          <td>${expense.purpose}</td>
          <td>¥${amount.toFixed(2)}</td>
          <td>${expense.description || ''}</td>
          <td>${approverName}</td>
          <td>${createdAt}</td>
        </tr>
      `;
    });

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>办公费用审批表</title>
        <style>
          body { 
            font-family: "Microsoft YaHei", "SimSun", serif; 
            margin: 20px; 
            background: white;
          }
          .header { text-align: center; margin-bottom: 30px; }
          .title { font-size: 18px; font-weight: bold; margin-bottom: 10px; }
          .date { font-size: 14px; margin-bottom: 20px; }
          table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-bottom: 30px; 
            background: white;
          }
          th, td { 
            border: 1px solid #000; 
            padding: 8px; 
            text-align: center; 
            font-size: 12px; 
          }
          th { background-color: #f0f0f0; font-weight: bold; }
          .signature { 
            margin-top: 30px; 
            display: flex; 
            justify-content: space-between; 
          }
          .signature-item { text-align: center; }
          .test-button {
            margin: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
          }
          @media print {
            body { margin: 0; }
            .no-print { display: none; }
            .test-button { display: none; }
          }
        </style>
      </head>
      <body>
        <button class="test-button no-print" onclick="window.print()">点击此处打印</button>
        
        <div class="header">
          <div class="title">广东承诺律师事务所办公费用审批表</div>
          <div class="date">打印日期：${today}</div>
        </div>
        
        <table>
          <thead>
            <tr>
              <th>序号</th>
              <th>费用发生日期</th>
              <th>申请人</th>
              <th>用途</th>
              <th>金额</th>
              <th>说明</th>
              <th>审批人</th>
              <th>创建时间</th>
            </tr>
          </thead>
          <tbody>
            ${tableRows}
          </tbody>
        </table>
        
        <div class="signature">
          <div class="signature-item">
            <p>申请人签字：_____________</p>
            <p>日期：_____________</p>
          </div>
          <div class="signature-item">
            <p>审批人签字：_____________</p>
            <p>日期：_____________</p>
          </div>
        </div>
        
        <script>
          console.log('打印页面已加载，费用记录数：${expenses.length}');
          // 页面加载完成后自动聚焦
          window.focus();
          
          // 如果自动打印失败，显示提示
          setTimeout(function() {
            var printButton = document.querySelector('.test-button');
            if (printButton) {
              printButton.style.display = 'block';
              printButton.style.background = '#28a745';
              printButton.innerHTML = '自动打印可能失败，点击此处手动打印';
            }
          }, 2000);
        </script>
      </body>
      </html>
    `;
    
    console.log('HTML内容生成完成，内容长度：', htmlContent.length);
    return htmlContent;
  }

  // 获取用户显示名称的辅助方法
  private getUserDisplayName(user: User): string {
    if (!user) return '';
    const displayName = user.first_name && user.last_name ? 
      `${user.first_name} ${user.last_name}` : user.username;
    return displayName || user.username;
  }



  // 搜索案件（用于财务记录关联）
  searchCases(keyword: string): Observable<Case[]> {
    const params = new HttpParams().set('keyword', keyword);
    return this.http.get<Case[]>(`${this.apiUrl}/records/search_cases/`, { params });
  }

  // 获取年度财务报告
  getAnnualReport(year: number): Observable<any> {
    const params = new HttpParams().set('year', year.toString());
    return this.http.get<any>(`${this.apiUrl}/records/annual_report/`, { params });
  }
} 