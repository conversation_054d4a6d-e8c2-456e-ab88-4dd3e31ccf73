from django.db import models
from django.contrib.auth.models import User
from cases.models import Case
from django.utils import timezone
from django.db.models.signals import post_save
from django.dispatch import receiver

class WithdrawalRequest(models.Model):
    STATUS_CHOICES = (
        ('PENDING', '未审批'),
        ('APPROVED', '已批准'),
        ('REJECTED', '已拒绝'),
    )
    
    case = models.ForeignKey(
        Case,
        on_delete=models.CASCADE,
        related_name='withdrawal_requests',
        verbose_name='关联案件'
    )
    requester = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='withdrawal_requests',
        verbose_name='申请人'
    )
    amount = models.DecimalField(
        max_digits=12, 
        decimal_places=2,
        verbose_name='申请金额'
    )
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES,
        default='PENDING',
        verbose_name='状态'
    )
    approver = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_withdrawals',
        verbose_name='审批人'
    )
    remarks = models.TextField(
        blank=True,
        null=True,
        verbose_name='备注'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='申请时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '提款申请'
        verbose_name_plural = '提款申请'
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.requester.username} - {self.case.case_number} - ¥{self.amount}"

    def approve(self, approver):
        """批准提款申请并创建财务记录"""
        if self.status != 'PENDING':
            return False
            
        self.status = 'APPROVED'
        self.approver = approver
        self.save()
        return True
        
    def reject(self, approver):
        """拒绝提款申请"""
        if self.status != 'PENDING':
            return False
            
        self.status = 'REJECTED'
        self.approver = approver
        self.save()
        return True

@receiver(post_save, sender=WithdrawalRequest)
def create_finance_record_on_approval(sender, instance, **kwargs):
    """当提款申请被批准时，创建对应的财务记录"""
    if instance.status == 'APPROVED' and instance.approver:
        from finance.models import FinanceRecord
        
        # 检查是否已经为此申请创建了财务记录
        existing_record = FinanceRecord.objects.filter(
            withdrawal_request=instance
        ).exists()
        
        if not existing_record:
            FinanceRecord.objects.create(
                case=instance.case,
                amount=instance.amount,
                account_type='lawyer_withdrawal',
                description=f"{instance.requester.username}律师提款 - {instance.case.case_number}",
                payment_method='bank_transfer',
                payer=instance.requester,
                receiver=None,  # 律师收款，不记录接收方
                operator=instance.approver,
                withdrawal_request=instance,  # 关联提款申请
                remarks=instance.remarks
            )
