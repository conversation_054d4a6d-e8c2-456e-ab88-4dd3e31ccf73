from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import OfficeExpenseViewSet, PrintExpenseTemplateView, PrintWithdrawalTemplateView, FinanceRecordViewSet
from .withdrawal_views import WithdrawalRequestViewSet

router = DefaultRouter()
router.register(r'expenses', OfficeExpenseViewSet)
router.register(r'records', FinanceRecordViewSet)
router.register(r'withdrawals', WithdrawalRequestViewSet, basename='withdrawal')

urlpatterns = [
    path('', include(router.urls)),
    path('templates/finance/print_expense.html', PrintExpenseTemplateView.as_view(), name='print-expense-template'),
    path('templates/finance/print_withdrawal.html', PrintWithdrawalTemplateView.as_view(), name='print-withdrawal-template'),
] 