<div class="pricing-calculator">
  <h1>非诉讼服务收费计算器</h1>
  
  <mat-card class="calculator-card">
    <mat-card-content>
      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>服务类型</mat-label>
          <mat-select [value]="serviceType" (selectionChange)="updateServiceType($event.value)">
            <mat-option *ngFor="let type of serviceTypes" [value]="type.value">
              {{type.viewValue}}
            </mat-option>
          </mat-select>
          <mat-hint>请选择非诉讼服务的类型</mat-hint>
        </mat-form-field>
      </div>
      
      <div class="form-row" *ngIf="isFinancialType">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>涉及财产标的额（元）</mat-label>
          <input matInput type="number" min="0" [value]="amount" (input)="updateAmount($event)">
          <mat-hint>请输入涉及的财产标的额</mat-hint>
        </mat-form-field>
      </div>
      
      <div *ngIf="result" class="result-section">
        <h2>收费标准</h2>
        <mat-divider></mat-divider>
        
        <div class="fee-range">
          <div class="fee-item total-fee">
            <span class="fee-label">收费范围：</span>
            <span class="fee-value">{{ result.fee.min.toLocaleString() }}元 - {{ result.fee.max.toLocaleString() }}元</span>
          </div>
        </div>
        
        <h3>收费明细</h3>
        <mat-list>
          <mat-list-item *ngFor="let detail of result.details">
            {{ detail }}
          </mat-list-item>
        </mat-list>
      </div>
    </mat-card-content>
  </mat-card>
</div> 