"""
URL configuration for law_firm project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from rest_framework_simplejwt.views import TokenRefreshView
from .jwt import CustomTokenObtainPairView
from django.conf import settings
from django.conf.urls.static import static
from users.views import current_user
from .debug_views import debug_database_info

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('api/', include('cases.urls', namespace='api')),
    path('api/', include('users.urls')),
    path('api/feishu/', include('feishu.urls')),
    path('api/', include('templates_manager.urls')),
    path('api/case-files/', include('case_files.urls')),
    path('api/finance/', include('finance.urls')),
    path('api/users/me/', current_user, name='current-user'),
    path('api/debug/database/', debug_database_info, name='debug-database'),
]

# 添加媒体文件URL
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
