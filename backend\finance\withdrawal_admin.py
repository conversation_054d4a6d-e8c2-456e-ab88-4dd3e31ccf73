from django.contrib import admin
from .models import WithdrawalRequest, FinanceRecord

@admin.register(WithdrawalRequest)
class WithdrawalRequestAdmin(admin.ModelAdmin):
    list_display = ('requester', 'case', 'amount', 'status', 'approver', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('requester__username', 'case__case_number', 'remarks')
    raw_id_fields = ('case', 'requester', 'approver')
    date_hierarchy = 'created_at'
