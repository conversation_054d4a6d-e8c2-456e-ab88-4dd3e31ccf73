import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { RouterModule } from '@angular/router';
import { Subject, takeUntil, timer, switchMap } from 'rxjs';

import { FinanceService, LawyerBalance } from '../../../services/finance.service';
import { AuthService } from '../../../services/auth.service';

@Component({
  selector: 'app-debt-warning',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatIconModule,
    MatButtonModule,
    RouterModule
  ],
  template: `
    <div *ngIf="hasDebt && balance" class="debt-warning-container">
      <mat-card class="debt-warning-card">
        <div class="warning-content">
          <mat-icon class="warning-icon">warning</mat-icon>
          <div class="warning-text">
            <strong>财务提醒：</strong>
            您当前账户余额为负数，欠款金额为 
            <span class="debt-amount">¥{{ balance.debt_amount.toFixed(2) }}</span>
          </div>
          <div class="warning-actions">
            <button mat-stroked-button 
                    routerLink="/finance/records" 
                    class="view-details-btn">
              <mat-icon>visibility</mat-icon>
              查看详情
            </button>
            <button mat-icon-button 
                    (click)="dismissWarning()" 
                    class="dismiss-btn"
                    matTooltip="暂时关闭提醒">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>
      </mat-card>
    </div>
  `,
  styles: [`
    .debt-warning-container {
      width: 100%;
      padding: 16px;
      background: #fff;
      border-bottom: 1px solid #e0e0e0;
      animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
      from {
        transform: translateY(-100%);
        opacity: 0;
      }
      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    .debt-warning-card {
      background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
      border-left: 4px solid #f44336;
      box-shadow: 0 2px 8px rgba(244, 67, 54, 0.2);
      margin: 0 auto;
      max-width: 1200px;
    }

    .warning-content {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
    }

    .warning-icon {
      color: #f44336;
      font-size: 24px;
      width: 24px;
      height: 24px;
      flex-shrink: 0;
    }

    .warning-text {
      flex: 1;
      color: #d32f2f;
      font-weight: 500;
      line-height: 1.4;
    }

    .debt-amount {
      font-weight: 700;
      font-size: 1.1em;
      color: #b71c1c;
    }

    .warning-actions {
      display: flex;
      align-items: center;
      gap: 8px;
      flex-shrink: 0;
    }

    .view-details-btn {
      color: #d32f2f;
      border-color: #d32f2f;
      font-size: 0.9em;
      white-space: nowrap;
    }

    .view-details-btn:hover {
      background-color: rgba(211, 47, 47, 0.1);
    }

    .dismiss-btn {
      color: #f44336;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .debt-warning-container {
        padding: 12px;
      }

      .warning-content {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 12px;
      }

      .warning-text {
        text-align: center;
      }

      .warning-actions {
        justify-content: center;
        gap: 12px;
      }

      .view-details-btn {
        font-size: 0.85em;
        flex: 1;
        max-width: 120px;
      }
    }

    @media (max-width: 480px) {
      .warning-content {
        gap: 8px;
        padding: 10px;
      }

      .warning-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
      }

      .warning-text {
        font-size: 0.9em;
      }

      .debt-amount {
        font-size: 1em;
      }
    }
  `]
})
export class DebtWarningComponent implements OnInit, OnDestroy {
  balance: LawyerBalance | null = null;
  hasDebt = false;
  isDismissed = false;
  private destroy$ = new Subject<void>();

  constructor(
    private financeService: FinanceService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // 检查用户登录状态
    this.authService.user$.pipe(
      takeUntil(this.destroy$),
      switchMap(user => {
        if (user) {
          return this.financeService.getMyBalance();
        } else {
          return [];
        }
      })
    ).subscribe({
      next: (balance) => {
        this.balance = balance;
        this.hasDebt = balance.has_debt && !this.isDismissed;
      },
      error: (error) => {
        console.error('获取余额信息失败:', error);
      }
    });

    // 每30秒刷新一次余额信息
    timer(0, 30000).pipe(
      takeUntil(this.destroy$),
      switchMap(() => {
        if (!this.authService.isLoggedIn()) {
          return [];
        }
        return this.financeService.getMyBalance();
      })
    ).subscribe({
      next: (balance) => {
        this.balance = balance;
        // 如果之前关闭了警告，但债务金额发生变化，重新显示警告
        if (balance.has_debt && this.isDismissed) {
          const storedAmount = localStorage.getItem('dismissed_debt_amount');
          if (!storedAmount || parseFloat(storedAmount) !== balance.debt_amount) {
            this.isDismissed = false;
          }
        }
        this.hasDebt = balance.has_debt && !this.isDismissed;
      },
      error: (error) => {
        console.error('刷新余额信息失败:', error);
      }
    });

    // 检查是否之前关闭过警告
    const dismissed = localStorage.getItem('debt_warning_dismissed');
    if (dismissed === 'true') {
      this.isDismissed = true;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  dismissWarning(): void {
    this.isDismissed = true;
    this.hasDebt = false;
    
    // 保存关闭状态到本地存储
    localStorage.setItem('debt_warning_dismissed', 'true');
    if (this.balance) {
      localStorage.setItem('dismissed_debt_amount', this.balance.debt_amount.toString());
    }
  }
} 