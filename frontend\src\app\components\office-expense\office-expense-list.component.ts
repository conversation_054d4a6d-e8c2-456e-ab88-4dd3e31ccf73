import { Compo<PERSON>, OnInit } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { DatePipe, Ng<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>If, DecimalPipe } from '@angular/common';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatSortModule, Sort } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FinanceService } from '../../services/finance.service';
import { OfficeExpense } from '../../interfaces/office-expense.interface';
import { AuthService } from '../../services/auth.service';
import { getUserDisplayName } from '../../utils/user.utils';

// 定义User接口，与AuthService中使用的一致
interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  profile: {
    has_admin_approval_permission: boolean;
    has_director_approval_permission: boolean;
  };
  permissions: string[];
}

@Component({
  selector: 'app-office-expense-list',
  standalone: true,
  imports: [
    NgIf, NgFor, NgClass, DatePipe, DecimalPipe,
    ReactiveFormsModule,
    MatTableModule, MatPaginatorModule, MatSortModule,
    MatFormFieldModule, MatInputModule, MatButtonModule,
    MatIconModule, MatCardModule, MatDatepickerModule,
    MatNativeDateModule, MatSelectModule, MatDialogModule,
    MatTooltipModule, MatSnackBarModule
  ],
  providers: [DatePipe],
  template: `
    <div class="container">
      <div class="header">
        <h1>办公费用审批表</h1>
        <div>
          <button mat-raised-button color="primary" (click)="openExpenseDialog()">
            <mat-icon>add</mat-icon> 新增费用
          </button>
          <button mat-raised-button color="accent" (click)="testPrint()" style="margin-left: 10px;">
            <mat-icon>bug_report</mat-icon> 测试打印功能
          </button>
        </div>
      </div>

      <mat-card class="filter-section">
        <mat-card-content>
          <form [formGroup]="filterForm">
            <div class="filter-row">
              <mat-form-field appearance="outline">
                <mat-label>关键词</mat-label>
                <input matInput formControlName="keyword" placeholder="搜索用途或说明...">
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>开始日期</mat-label>
                <input matInput [matDatepicker]="startPicker" formControlName="startDate">
                <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
                <mat-datepicker #startPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>结束日期</mat-label>
                <input matInput [matDatepicker]="endPicker" formControlName="endDate">
                <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
                <mat-datepicker #endPicker></mat-datepicker>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>审批状态</mat-label>
                <mat-select formControlName="approvalStatus">
                  <mat-option [value]="''">全部</mat-option>
                  <mat-option value="approved">已审批</mat-option>
                  <mat-option value="pending">待审批</mat-option>
                </mat-select>
              </mat-form-field>
            </div>

            <div class="filter-actions">
              <button mat-button (click)="resetFilters()">重置</button>
              <button mat-raised-button color="primary" (click)="applyFilters()">搜索</button>
            </div>
          </form>
        </mat-card-content>
      </mat-card>

      <div class="expense-table-container">
        <table mat-table [dataSource]="expenses" matSort (matSortChange)="sortData($event)" class="expense-table">
          <!-- 费用发生日期 -->
          <ng-container matColumnDef="expenseDate">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 费用发生日期 </th>
            <td mat-cell *matCellDef="let expense"> {{ expense.expense_date | date:'yyyy-MM-dd' }} </td>
          </ng-container>

          <!-- 申请人 -->
          <ng-container matColumnDef="applicant">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 申请人 </th>
            <td mat-cell *matCellDef="let expense"> {{ getUserDisplayName(expense.applicant) }} </td>
          </ng-container>

          <!-- 用途 -->
          <ng-container matColumnDef="purpose">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 用途 </th>
            <td mat-cell *matCellDef="let expense"> {{ expense.purpose }} </td>
          </ng-container>

          <!-- 金额 -->
          <ng-container matColumnDef="amount">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 金额 </th>
            <td mat-cell *matCellDef="let expense"> {{ expense.amount | number:'1.2-2' }} </td>
          </ng-container>

          <!-- 说明 -->
          <ng-container matColumnDef="description">
            <th mat-header-cell *matHeaderCellDef> 说明 </th>
            <td mat-cell *matCellDef="let expense"> {{ expense.description }} </td>
          </ng-container>

          <!-- 审批人 -->
          <ng-container matColumnDef="approver">
            <th mat-header-cell *matHeaderCellDef> 审批人 </th>
            <td mat-cell *matCellDef="let expense">
              <!-- 已审批，显示审批人 -->
              <span *ngIf="expense.approver">{{ getUserDisplayName(expense.approver) }}</span>
              
              <!-- 未审批且有主任审批权限，显示审批按钮 -->
              <button 
                *ngIf="!expense.approver && authService.canDirectorApproveCases()"
                mat-raised-button 
                color="primary" 
                size="small"
                class="approve-button"
                (click)="approveExpense(expense); $event.stopPropagation()">
                <mat-icon class="small-icon">check_circle</mat-icon> 审批
              </button>
            </td>
          </ng-container>

          <!-- 创建时间 -->
          <ng-container matColumnDef="createdAt">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> 创建时间 </th>
            <td mat-cell *matCellDef="let expense"> {{ expense.created_at | date:'yyyy-MM-dd HH:mm' }} </td>
          </ng-container>

          <!-- 最后打印时间 -->
          <ng-container matColumnDef="lastPrintedAt">
            <th mat-header-cell *matHeaderCellDef> 最后打印时间 </th>
            <td mat-cell *matCellDef="let expense"> {{ expense.last_printed_at | date:'yyyy-MM-dd HH:mm' || '未打印' }} </td>
          </ng-container>

          <!-- 操作 -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef> 操作 </th>
            <td mat-cell *matCellDef="let expense">
              <!-- 打印按钮 -->
              <button 
                mat-icon-button 
                color="accent" 
                matTooltip="打印当前及之前的已审批记录，最多9条" 
                [disabled]="!expense.approver"
                (click)="printExpense(expense)">
                <mat-icon>print</mat-icon>
              </button>

              <!-- 删除按钮 -->
              <button 
                mat-icon-button 
                color="warn" 
                [matTooltip]="getDeleteTooltip(expense)"
                [disabled]="!canDeleteExpense(expense)"
                *ngIf="shouldShowDeleteButton(expense)"
                (click)="deleteExpense(expense)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <mat-paginator 
          [length]="totalItems" 
          [pageSize]="pageSize" 
          [pageSizeOptions]="[5, 10, 20, 50]"
          (page)="handlePageEvent($event)">
        </mat-paginator>
      </div>
    </div>
  `,
  styles: [`
    .container {
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .filter-section {
      margin-bottom: 20px;
    }
    .filter-row {
      display: flex;
      flex-wrap: wrap;
      gap: 16px;
    }
    .filter-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 8px;
    }
    .expense-table-container {
      overflow-x: auto;
    }
    .expense-table {
      width: 100%;
    }
    mat-form-field {
      flex: 1;
      min-width: 200px;
    }
    .approve-button {
      line-height: 28px;
      font-size: 12px;
      padding: 0 8px;
    }
    .small-icon {
      font-size: 16px;
      width: 16px;
      height: 16px;
      line-height: 16px;
      vertical-align: middle;
      margin-right: 4px;
    }
    .mat-icon-button:disabled {
      color: rgba(0, 0, 0, 0.26) !important;
    }
    .mat-icon-button:disabled .mat-icon {
      color: rgba(0, 0, 0, 0.26) !important;
    }
    @media (max-width: 768px) {
      .filter-row {
        flex-direction: column;
      }
      mat-form-field {
        width: 100%;
      }
    }
  `]
})
export class OfficeExpenseListComponent implements OnInit {
  expenses: OfficeExpense[] = [];
  displayedColumns: string[] = [
    'expenseDate', 'applicant', 'purpose', 'amount', 
    'description', 'approver', 'createdAt', 'lastPrintedAt', 'actions'
  ];
  filterForm = new FormGroup({
    keyword: new FormControl(''),
    startDate: new FormControl<Date | null>(null),
    endDate: new FormControl<Date | null>(null),
    approvalStatus: new FormControl('')
  });

  totalItems: number = 0;
  pageSize: number = 10;
  pageIndex: number = 0;
  currentUser: User | null = null;
  getUserDisplayName = getUserDisplayName;

  constructor(
    private financeService: FinanceService,
    public authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private datePipe: DatePipe
  ) {}

  ngOnInit(): void {
    this.loadExpenses();
    this.authService.user$.subscribe(user => {
      this.currentUser = user as User;
    });
  }

  loadExpenses(): void {
    const filters: any = {};
    
    const keyword = this.filterForm.get('keyword')?.value;
    if (keyword) {
      filters.keyword = keyword;
    }
    
    const startDate = this.filterForm.get('startDate')?.value;
    if (startDate) {
      filters.start_date = this.datePipe.transform(startDate, 'yyyy-MM-dd');
    }
    
    const endDate = this.filterForm.get('endDate')?.value;
    if (endDate) {
      filters.end_date = this.datePipe.transform(endDate, 'yyyy-MM-dd');
    }
    
    const approvalStatus = this.filterForm.get('approvalStatus')?.value;
    if (approvalStatus) {
      filters.approval_status = approvalStatus;
    }
    
    this.financeService.getOfficeExpenses(filters).subscribe({
      next: (data) => {
        this.expenses = data;
        this.totalItems = data.length;
      },
      error: (error) => {
        console.error('获取办公费用列表失败', error);
        this.snackBar.open('获取办公费用列表失败', '关闭', {
          duration: 3000
        });
      }
    });
  }

  applyFilters(): void {
    this.pageIndex = 0;
    this.loadExpenses();
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.loadExpenses();
  }

  async openExpenseDialog(expense?: OfficeExpense): Promise<void> {
    // 动态导入对话框组件，避免循环引用
    const { OfficeExpenseDialogComponent } = await import('./office-expense-dialog.component');
    
    const dialogRef = this.dialog.open(OfficeExpenseDialogComponent, {
      width: '600px',
      data: expense
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loadExpenses();
      }
    });
  }

  approveExpense(expense: OfficeExpense): void {
    if (!expense.id) return;
    
    this.financeService.approveOfficeExpense(expense.id).subscribe({
      next: () => {
        this.snackBar.open('费用审批成功', '关闭', {
          duration: 3000
        });
        this.loadExpenses();
      },
      error: (error) => {
        console.error('费用审批失败', error);
        this.snackBar.open('费用审批失败: ' + (error.error?.detail || '未知错误'), '关闭', {
          duration: 3000
        });
      }
    });
  }

  printExpense(expense: OfficeExpense): void {
    if (!expense.id) {
      console.error('费用ID为空');
      return;
    }
    
    // 检查费用是否已审批
    if (!expense.approver) {
      this.snackBar.open('只有已审批的费用才能打印', '关闭', {
        duration: 3000
      });
      return;
    }
    
    this.financeService.printOfficeExpense(expense.id).subscribe({
      next: (data) => {
        if (data && data.length > 0) {
          this.financeService.openPrintWindow(data);
          this.loadExpenses(); // 刷新列表，更新打印时间
        } else {
          this.snackBar.open('没有可打印的已审批费用记录', '关闭', {
            duration: 3000
          });
        }
      },
      error: (error) => {
        console.error('准备打印数据失败', error);
        this.snackBar.open('准备打印数据失败: ' + (error.error?.detail || '未知错误'), '关闭', {
          duration: 3000
        });
      }
    });
  }

  deleteExpense(expense: OfficeExpense): void {
    if (!expense.id) return;
    
    // 额外的安全检查：已审批的费用不能删除
    if (expense.approver) {
      this.snackBar.open('已审批的费用不能删除', '关闭', {
        duration: 3000
      });
      return;
    }
    
    // 检查是否是申请人
    if (!this.canDeleteExpense(expense)) {
      this.snackBar.open('您没有删除此费用的权限', '关闭', {
        duration: 3000
      });
      return;
    }
    
    if (confirm('确定要删除这条费用记录吗？')) {
      this.financeService.deleteOfficeExpense(expense.id).subscribe({
        next: () => {
          this.snackBar.open('费用记录删除成功', '关闭', {
            duration: 3000
          });
          this.loadExpenses();
        },
        error: (error) => {
          console.error('费用记录删除失败', error);
          this.snackBar.open('费用记录删除失败', '关闭', {
            duration: 3000
          });
        }
      });
    }
  }

  canDeleteExpense(expense: OfficeExpense): boolean {
    // 只有未审批的费用才能删除，且必须是申请人
    if (!this.currentUser || !expense.applicant) return false;
    
    // 必须是申请人
    if (this.currentUser.id !== expense.applicant.id) return false;
    
    // 必须是未审批状态
    return !expense.approver;
  }

  getDeleteTooltip(expense: OfficeExpense): string {
    if (!this.currentUser || !expense.applicant) return '无法删除';
    
    // 如果不是申请人
    if (this.currentUser.id !== expense.applicant.id) {
      return '只能删除自己申请的费用';
    }
    
    // 如果已审批
    if (expense.approver) {
      return '已审批的费用不能删除';
    }
    
    // 未审批且是申请人
    return '删除费用申请';
  }

  shouldShowDeleteButton(expense: OfficeExpense): boolean {
    // 只有申请人可以看到删除按钮（无论是否已审批）
    if (!this.currentUser || !expense.applicant) return false;
    return this.currentUser.id === expense.applicant.id;
  }

  handlePageEvent(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
  }

  sortData(sort: Sort): void {
    // 前端排序逻辑
  }

  // 测试打印功能
  testPrint(): void {
    console.log('测试打印功能');
    
    // 创建测试数据
    const testExpense: OfficeExpense = {
      id: 999,
      expense_date: new Date().toISOString().split('T')[0],
      applicant: {
        id: 1,
        username: 'testuser',
        first_name: '测试',
        last_name: '用户',
        email: '<EMAIL>'
      },
      purpose: '测试打印功能',
      amount: 100.00,
      description: '这是一个测试打印功能的记录',
      approver: {
        id: 2,
        username: 'admin',
        first_name: '管理',
        last_name: '员',
        email: '<EMAIL>'
      },
      created_at: new Date().toISOString(),
      last_printed_at: new Date().toISOString()
    };

    console.log('使用测试数据调用打印窗口');
    this.financeService.openPrintWindow([testExpense]);
  }
} 