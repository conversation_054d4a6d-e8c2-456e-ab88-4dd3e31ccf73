<div class="pricing-calculator">
  <h1>民事诉讼收费计算器</h1>
  
  <mat-card class="calculator-card">
    <mat-card-content>
      <div class="form-row">
        <mat-radio-group class="case-type-radio" [value]="isPropertyCase" (change)="updateCaseType($event.value)">
          <mat-radio-button [value]="true">涉及财产关系</mat-radio-button>
          <mat-radio-button [value]="false">不涉及财产关系</mat-radio-button>
        </mat-radio-group>
      </div>
      
      <div class="form-row" *ngIf="isPropertyCase">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>争议标的额（元）</mat-label>
          <input matInput type="number" min="0" [value]="amount" (input)="updateAmount($event)">
          <mat-hint>请输入案件的争议标的额</mat-hint>
        </mat-form-field>
      </div>
      
      <div *ngIf="result" class="result-section">
        <h2>收费标准</h2>
        <mat-divider></mat-divider>
        
        <div class="fee-range">
          <div class="fee-item" *ngIf="result.fixedFee">
            <span class="fee-label">固定收费范围：</span>
            <span class="fee-value">{{ result.fixedFee.min.toLocaleString() }}元 - {{ result.fixedFee.max.toLocaleString() }}元</span>
          </div>
          <div class="fee-item" *ngIf="result.percentageFee">
            <span class="fee-label">按比例收费：</span>
            <span class="fee-value">{{ result.percentageFee.min.toLocaleString() }}元 - {{ result.percentageFee.max.toLocaleString() }}元</span>
          </div>
          <div class="fee-item total-fee">
            <span class="fee-label">总计收费范围：</span>
            <span class="fee-value">{{ result.totalFee.min.toLocaleString() }}元 - {{ result.totalFee.max.toLocaleString() }}元</span>
          </div>
        </div>
        
        <h3>收费明细</h3>
        <mat-list>
          <mat-list-item *ngFor="let detail of result.details">
            {{ detail }}
          </mat-list-item>
        </mat-list>
      </div>
    </mat-card-content>
  </mat-card>
</div> 