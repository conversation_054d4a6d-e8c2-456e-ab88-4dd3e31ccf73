import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

export interface WithdrawalDialogData {
  caseId: number;
  amount: number;
}

@Component({
  selector: 'app-withdrawal-dialog',
  templateUrl: './withdrawal-dialog.component.html',
  styleUrls: ['./withdrawal-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatProgressSpinnerModule
  ]
})
export class WithdrawalDialogComponent {
  form: FormGroup;

  constructor(
    private fb: FormBuilder,
    public dialogRef: MatDialogRef<WithdrawalDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: WithdrawalDialogData
  ) {
    this.form = this.fb.group({
      amount: [data.amount, [Validators.required, Validators.min(0.01)]],
      remarks: ['']
    });
  }

  onSubmit(): void {
    if (this.form.valid) {
      const result = {
        case_id: this.data.caseId,
        amount: this.form.value.amount,
        remarks: this.form.value.remarks || undefined
      };
      this.dialogRef.close(result);
    }
  }
}
