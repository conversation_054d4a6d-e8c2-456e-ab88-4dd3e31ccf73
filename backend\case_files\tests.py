from django.test import TestCase, override_settings
import os
import tempfile
import zipfile
from io import BytesIO
from unittest.mock import patch, MagicMock

from django.urls import reverse
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile

from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from cases.models import Case
from .models import CaseFileFolder, CaseFile
from .utils import get_file_type, extract_zip, convert_to_pdf, merge_pdfs


class CaseFileModelTests(TestCase):
    """测试案件文件模型"""
    
    def setUp(self):
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )
        
        # 创建测试案件
        self.case = Case.objects.create(
            case_cause='测试案件',
            lawyer=self.user
        )
        
        # 创建测试文件夹
        self.folder = CaseFileFolder.objects.create(
            case=self.case,
            name='测试文件夹',
            parent=None
        )
    
    def test_folder_path_generation(self):
        """测试文件夹路径生成"""
        # 顶级文件夹路径应为空字符串
        self.assertEqual(self.folder.path, '')
        
        # 创建子文件夹
        child_folder = CaseFileFolder.objects.create(
            case=self.case,
            name='子文件夹',
            parent=self.folder
        )
        
        # 子文件夹路径应包括父文件夹名称
        self.assertEqual(child_folder.path, '测试文件夹')
        
        # 创建孙文件夹
        grandchild_folder = CaseFileFolder.objects.create(
            case=self.case,
            name='孙文件夹',
            parent=child_folder
        )
        
        # 孙文件夹路径应包括完整路径
        self.assertEqual(grandchild_folder.path, '测试文件夹/子文件夹')
    
    def test_case_file_creation(self):
        """测试案件文件创建"""
        # 创建测试文件
        test_file = SimpleUploadedFile(
            name='test.pdf',
            content=b'file content',
            content_type='application/pdf'
        )
        
        # 创建文件记录
        case_file = CaseFile.objects.create(
            case=self.case,
            folder=self.folder,
            file=test_file,
            file_type='pdf',
            original_filename='test.pdf',
            uploaded_by=self.user
        )
        
        # 验证文件记录
        self.assertEqual(case_file.case, self.case)
        self.assertEqual(case_file.folder, self.folder)
        self.assertEqual(case_file.file_type, 'pdf')
        self.assertEqual(case_file.original_filename, 'test.pdf')
        self.assertEqual(case_file.uploaded_by, self.user)


class CaseFileUtilsTests(TestCase):
    """测试案件文件工具函数"""
    
    def test_get_file_type(self):
        """测试获取文件类型"""
        self.assertEqual(get_file_type('test.doc'), 'doc')
        self.assertEqual(get_file_type('test.docx'), 'docx')
        self.assertEqual(get_file_type('test.xlsx'), 'xlsx')
        self.assertEqual(get_file_type('test.pdf'), 'pdf')
        self.assertEqual(get_file_type('test.jpg'), 'jpg')
        self.assertEqual(get_file_type('test.jpeg'), 'jpg')
        self.assertEqual(get_file_type('test.png'), 'png')
        self.assertEqual(get_file_type('test.zip'), 'zip')
        self.assertEqual(get_file_type('test.unknown'), 'other')
    
    @patch('case_files.utils.PdfReader')
    @patch('case_files.utils.register_fonts')
    def test_convert_to_pdf(self, mock_register_fonts, mock_pdf_reader):
        """测试文件转换为PDF"""
        # 模拟注册字体
        mock_register_fonts.return_value = True
        
        # 模拟PdfReader
        mock_pdf_reader_instance = MagicMock()
        mock_pdf_reader_instance.pages = [MagicMock(), MagicMock()]  # 模拟两页PDF
        mock_pdf_reader.return_value = mock_pdf_reader_instance
        
        # 创建测试用户和案件
        user = User.objects.create_user(username='testuser', password='testpassword')
        case = Case.objects.create(case_cause='测试案件', lawyer=user)
        
        # 测试PDF文件
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_pdf:
            tmp_pdf.write(b'%PDF-1.5\nTest PDF content')
            tmp_pdf_path = tmp_pdf.name
        
        pdf_file = SimpleUploadedFile(
            name='test.pdf',
            content=open(tmp_pdf_path, 'rb').read(),
            content_type='application/pdf'
        )
        
        # 创建CaseFile对象
        case_file = CaseFile.objects.create(
            case=case,
            file=pdf_file,
            file_type='pdf',
            original_filename='test.pdf',
            uploaded_by=user
        )
        
        # 测试转换函数
        with patch('case_files.utils.os.path.exists', return_value=True):
            with patch('case_files.utils.os.path.getsize', return_value=1000):
                result_path = convert_to_pdf(case_file)
                
        # 验证结果
        self.assertIsNotNone(result_path)
        
        # 清理临时文件
        if os.path.exists(tmp_pdf_path):
            os.unlink(tmp_pdf_path)
        if result_path and os.path.exists(result_path):
            os.unlink(result_path)


@override_settings(MEDIA_ROOT=tempfile.mkdtemp())
class CaseFileAPITests(APITestCase):
    """测试案件文件API"""
    
    def setUp(self):
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword',
            email='<EMAIL>'
        )
        
        # 创建API客户端并登录
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # 创建测试案件
        self.case = Case.objects.create(
            case_cause='测试案件',
            lawyer=self.user
        )
    
    def test_create_folder(self):
        """测试创建文件夹"""
        url = reverse('casefilefolder-list')
        data = {
            'case': self.case.id,
            'name': '测试文件夹',
            'parent': None
        }
        
        response = self.client.post(url, data, format='json')
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], '测试文件夹')
        
        # 验证数据库
        self.assertTrue(CaseFileFolder.objects.filter(
            case=self.case,
            name='测试文件夹',
            parent=None
        ).exists())
    
    def test_upload_file(self):
        """测试上传文件"""
        url = reverse('casefile-list')
        
        # 创建测试文件
        test_file = SimpleUploadedFile(
            name='test.pdf',
            content=b'%PDF-1.5\nTest PDF content',
            content_type='application/pdf'
        )
        
        data = {
            'case': self.case.id,
            'folder': '',
            'file': test_file
        }
        
        response = self.client.post(url, data, format='multipart')
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证数据库
        self.assertTrue(CaseFile.objects.filter(
            case=self.case,
            folder=None,
            original_filename='test.pdf'
        ).exists())
    
    @patch('case_files.views.extract_zip')
    def test_upload_zip_file(self, mock_extract_zip):
        """测试上传ZIP文件"""
        # 模拟extract_zip函数
        mock_extract_zip.return_value = None  # 模拟文件直接解压到根目录
        
        url = reverse('casefile-list')
        
        # 创建测试ZIP文件
        zip_buffer = BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w') as zip_file:
            zip_file.writestr('test.txt', 'Test content')
            zip_file.writestr('folder/nested.txt', 'Nested content')
        
        test_zip = SimpleUploadedFile(
            name='test.zip',
            content=zip_buffer.getvalue(),
            content_type='application/zip'
        )
        
        data = {
            'case': self.case.id,
            'folder': '',
            'file': test_zip
        }
        
        # 测试视图处理ZIP文件的上传
        response = self.client.post(url, data, format='multipart')
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # 验证extract_zip是否被调用
        mock_extract_zip.assert_called_once()
    
    def test_list_root_files(self):
        """测试列出根目录文件"""
        # 创建测试文件
        test_file = SimpleUploadedFile(
            name='test.pdf',
            content=b'Test content',
            content_type='application/pdf'
        )
        
        CaseFile.objects.create(
            case=self.case,
            folder=None,
            file=test_file,
            file_type='pdf',
            original_filename='test.pdf',
            uploaded_by=self.user
        )
        
        url = reverse('casefile-case-root-files')
        response = self.client.get(url, {'case': self.case.id})
        
        # 验证响应
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['original_filename'], 'test.pdf')


class ExtractZipTests(TestCase):
    """测试ZIP文件解压功能"""
    
    def setUp(self):
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )
        
        # 创建测试案件
        self.case = Case.objects.create(
            case_cause='测试案件',
            lawyer=self.user
        )
    
    @patch('case_files.utils.process_extracted_dir')
    def test_extract_zip(self, mock_process_extracted_dir):
        """测试解压ZIP文件"""
        # 模拟process_extracted_dir函数
        mock_process_extracted_dir.return_value = None
        
        # 创建测试ZIP文件
        with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as tmp_zip:
            with zipfile.ZipFile(tmp_zip, 'w') as zip_file:
                zip_file.writestr('test.txt', 'Test content')
                zip_file.writestr('folder/nested.txt', 'Nested content')
            zip_path = tmp_zip.name
        
        # 调用extract_zip函数
        result = extract_zip(zip_path, self.case, self.user)
        
        # 验证结果
        self.assertIsNone(result)  # 如果process_extracted_dir返回None，extract_zip也应返回None
        mock_process_extracted_dir.assert_called_once()
        
        # 清理临时文件
        if os.path.exists(zip_path):
            os.unlink(zip_path)


@override_settings(MEDIA_ROOT=tempfile.mkdtemp())
class MergePDFsTests(TestCase):
    """测试PDF合并功能"""
    
    def setUp(self):
        # 创建测试用户
        self.user = User.objects.create_user(
            username='testuser',
            password='testpassword'
        )
        
        # 创建测试案件
        self.case = Case.objects.create(
            case_cause='测试案件',
            lawyer=self.user
        )
        
        # 注册字体
        with patch('case_files.utils.register_fonts', return_value=True):
            pass
    
    @patch('case_files.utils.convert_to_pdf')
    @patch('case_files.utils.SimpleDocTemplate')
    def test_merge_pdfs(self, mock_simple_doc_template, mock_convert_to_pdf):
        """测试合并PDF文件"""
        # 模拟convert_to_pdf函数
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_pdf:
            tmp_pdf.write(b'%PDF-1.5\nTest PDF content')
            tmp_pdf_path = tmp_pdf.name
        mock_convert_to_pdf.return_value = tmp_pdf_path
        
        # 模拟SimpleDocTemplate
        mock_doc_instance = MagicMock()
        mock_simple_doc_template.return_value = mock_doc_instance
        
        # 创建测试PDF文件
        test_pdf = SimpleUploadedFile(
            name='test.pdf',
            content=b'%PDF-1.5\nTest PDF content',
            content_type='application/pdf'
        )
        
        # 创建CaseFile对象
        pdf_file = CaseFile.objects.create(
            case=self.case,
            file=test_pdf,
            file_type='pdf',
            original_filename='test.pdf',
            uploaded_by=self.user
        )
        
        # 调用merge_pdfs函数
        with patch('case_files.utils.PdfReader') as mock_pdf_reader:
            mock_pdf_reader_instance = MagicMock()
            mock_pdf_reader_instance.pages = [MagicMock()]  # 模拟一页PDF
            mock_pdf_reader.return_value = mock_pdf_reader_instance
            
            response = merge_pdfs([pdf_file], '测试合并文档')
        
        # 验证结果
        self.assertEqual(response['Content-Type'], 'application/pdf')
        
        # 检查Content-Disposition，但不检查具体编码后的值
        # 只需确保包含attachment和pdf后缀
        self.assertIn('attachment', response['Content-Disposition'])
        self.assertIn('.pdf', response['Content-Disposition'])
        
        # 清理临时文件
        if os.path.exists(tmp_pdf_path):
            os.unlink(tmp_pdf_path)

    @patch('case_files.utils.convert_to_pdf')
    @patch('case_files.utils.SimpleDocTemplate')
    def test_merge_pdfs_with_custom_start_page(self, mock_simple_doc_template, mock_convert_to_pdf):
        """测试合并PDF文件时使用自定义起始页码"""
        # 模拟convert_to_pdf函数
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_pdf:
            tmp_pdf.write(b'%PDF-1.5\nTest PDF content')
            tmp_pdf_path = tmp_pdf.name
        mock_convert_to_pdf.return_value = tmp_pdf_path
        
        # 模拟SimpleDocTemplate
        mock_doc_instance = MagicMock()
        mock_simple_doc_template.return_value = mock_doc_instance
        
        # 创建测试PDF文件
        test_pdf = SimpleUploadedFile(
            name='test.pdf',
            content=b'%PDF-1.5\nTest PDF content',
            content_type='application/pdf'
        )
        
        # 创建CaseFile对象
        pdf_file = CaseFile.objects.create(
            case=self.case,
            file=test_pdf,
            file_type='pdf',
            original_filename='test.pdf',
            uploaded_by=self.user
        )
        
        # 使用自定义起始页码调用merge_pdfs函数
        with patch('case_files.utils.PdfReader') as mock_pdf_reader:
            mock_pdf_reader_instance = MagicMock()
            mock_pdf_reader_instance.pages = [MagicMock()]  # 模拟一页PDF
            mock_pdf_reader.return_value = mock_pdf_reader_instance
            
            response = merge_pdfs([pdf_file], '自定义页码测试文档', start_page_number=5)
        
        # 验证结果
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertIn('attachment', response['Content-Disposition'])
        self.assertIn('.pdf', response['Content-Disposition'])
        
        # 验证SimpleDocTemplate.build被调用，且包含页码回调函数
        mock_doc_instance.build.assert_called_once()
        args, kwargs = mock_doc_instance.build.call_args
        self.assertIn('onFirstPage', kwargs)
        self.assertIn('onLaterPages', kwargs)
        
        # 清理临时文件
        if os.path.exists(tmp_pdf_path):
            os.unlink(tmp_pdf_path)
