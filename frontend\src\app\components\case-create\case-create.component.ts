import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatCardModule } from '@angular/material/card';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatRadioModule } from '@angular/material/radio';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CaseService } from '../../services/case.service';
import { environment } from '../../../environments/environment';
import { Observable, of } from 'rxjs';
import { map, startWith, debounceTime, switchMap, catchError } from 'rxjs/operators';
import { OnDestroy } from '@angular/core';
import { PartyType } from '../../interfaces/case.interface';
import { getPartyTypeDisplay } from '../../utils/party.utils';

// 当事人接口定义
interface CaseParty {
  id: number;
  name: string;
  entityType: 'natural' | 'legal';
  entityId: number;
  party_type: string;
  is_client: boolean;
  internal_number?: string;
  remarks?: string;
  info: string;
}

// 添加当事人对话框组件
@Component({
  selector: 'app-add-party-dialog',
  template: `
    <h2 mat-dialog-title>添加案件当事人</h2>
    <mat-dialog-content class="dialog-content">
      <!-- 搜索当事人 -->
      <mat-form-field appearance="outline" style="width: 100%;">
        <mat-label>搜索当事人</mat-label>
        <input type="text"
               matInput
               [(ngModel)]="searchTerm"
               (input)="searchParties()"
               (paste)="onPaste()"
               (keyup)="searchParties()"
               placeholder="输入姓名/单位名/身份证号/手机号进行模糊搜索">
        <mat-icon matSuffix>search</mat-icon>
      </mat-form-field>

      <!-- 加载指示器居中显示 -->
      <div *ngIf="isLoading" class="loading-spinner-container">
        <mat-progress-spinner diameter="40" mode="indeterminate"></mat-progress-spinner>
        <p>正在加载当事人数据...</p>
      </div>

      <!-- 搜索结果区域，固定高度 -->
      <div class="search-results-container" *ngIf="!isLoading">
        <!-- 搜索结果直接显示 -->
        <div class="search-results-list" *ngIf="searchTerm.length > 0">
          <div class="search-result-item"
               *ngFor="let party of filteredParties"
               (click)="selectPartyDirectly(party)">
            <span class="party-option">
              <span class="party-name">{{ party.name }}</span>
              <span class="party-type-badge" [ngClass]="party.type === 'natural' ? 'natural' : 'legal'">
                {{ party.type === 'natural' ? '自然人' : '单位' }}
              </span>
            </span>
            <small class="party-info">{{ party.info }}</small>
          </div>
          <div *ngIf="filteredParties.length === 0 && searchTerm.length > 0" class="no-results">
            <span>无搜索结果，请尝试其他关键词</span>
          </div>
        </div>
      </div>

      <!-- 已选择的当事人 -->
      <div *ngIf="selectedParty" class="selected-party">
        <h3>已选择当事人</h3>
        <div class="party-details">
          <p><strong>名称:</strong> {{ selectedParty.name }}</p>
          <p><strong>类型:</strong> {{ selectedParty.type === 'natural' ? '自然人' : '单位' }}</p>
          <p><strong>相关信息:</strong> {{ selectedParty.info || '无' }}</p>
        </div>
      </div>

      <!-- 当事人角色选择 -->
      <div *ngIf="selectedParty" class="party-role-section">
        <mat-form-field appearance="outline" style="width: 100%;">
          <mat-label>当事人角色</mat-label>
          <mat-select [(ngModel)]="partyType" required>
            <!-- 根据案件类型显示不同选项 -->
            <ng-container *ngIf="caseType === '民事案件' || caseType === '行政案件'">
              <mat-option [value]="PartyType.PLAINTIFF">原告</mat-option>
              <mat-option [value]="PartyType.DEFENDANT">被告</mat-option>
              <mat-option [value]="PartyType.THIRD_PARTY">第三人</mat-option>
            </ng-container>

            <ng-container *ngIf="caseType === '刑事案件'">
              <mat-option [value]="PartyType.PLAINTIFF">受害人</mat-option>
              <mat-option [value]="PartyType.SUSPECT">犯罪嫌疑人</mat-option>
              <mat-option [value]="PartyType.SUSPECT_FAMILY">嫌疑人家属</mat-option>
            </ng-container>

            <ng-container *ngIf="caseType === '其他案件'">
              <mat-option [value]="PartyType.PLAINTIFF">原告</mat-option>
              <mat-option [value]="PartyType.DEFENDANT">被告</mat-option>
              <mat-option [value]="PartyType.THIRD_PARTY">第三人</mat-option>
              <mat-option [value]="PartyType.NON_LITIGATION_CLIENT">非诉委托人</mat-option>
            </ng-container>
          </mat-select>
        </mat-form-field>

        <mat-checkbox [(ngModel)]="isClient" class="client-checkbox">
          设为本案委托人
        </mat-checkbox>

        <mat-form-field appearance="outline" style="width: 100%;">
          <mat-label>内部编号(选填)</mat-label>
          <input matInput [(ngModel)]="internalNumber" placeholder="内部编号，可不填">
        </mat-form-field>

        <mat-form-field appearance="outline" style="width: 100%;">
          <mat-label>备注(选填)</mat-label>
          <textarea matInput [(ngModel)]="remarks" rows="3" placeholder="关于当事人的备注信息"></textarea>
        </mat-form-field>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button [mat-dialog-close]="null">取消</button>
      <button mat-raised-button color="primary"
              [disabled]="!selectedParty || !partyType"
              [mat-dialog-close]="{
                id: selectedParty?.id,
                name: selectedParty?.name,
                entityType: selectedParty?.type,
                entityId: selectedParty?.id,
                party_type: partyType,
                is_client: isClient,
                internal_number: internalNumber,
                remarks: remarks,
                info: selectedParty?.info
              }">
        添加
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .dialog-content {
      height: 500px;
      overflow-y: auto;
      overflow-x: hidden;
      padding-bottom: 16px;
      background-color: #ffffff;
      position: relative;
      z-index: 1;
    }

    .loading-spinner-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 100px;
      margin: 20px 0;
      background-color: #ffffff;
      border: 1px solid #ddd;
      border-radius: 4px;
      position: relative;
      z-index: 996;
    }

    .search-results-container {
      min-height: 100px;
      max-height: 200px;
      margin-bottom: 16px;
      position: relative;
      z-index: 999;
    }

    .search-results-list {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: #ffffff;
      position: relative;
      z-index: 1000;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .search-result-item {
      padding: 8px 16px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      background-color: #ffffff;
      transition: background-color 0.2s ease;
    }

    .search-result-item:hover {
      background-color: #f0f0f0;
    }

    .search-result-item:last-child {
      border-bottom: none;
    }

    .no-results {
      padding: 16px;
      text-align: center;
      color: #666;
      font-style: italic;
      background-color: #ffffff;
      border: 1px solid #ddd;
      border-radius: 4px;
    }

    .selected-party {
      margin: 20px 0;
      padding: 10px;
      border: 1px solid #e0e0e0;
      border-radius: 4px;
      background-color: #f8f9fa;
      position: relative;
      z-index: 997;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .party-details p {
      margin: 5px 0;
    }

    .party-role-section {
      margin-top: 16px;
    }

    .client-checkbox {
      display: block;
      margin: 15px 0;
    }

    .party-option {
      display: flex;
      align-items: center;
    }

    .party-name {
      font-weight: 500;
    }

    .party-type-badge {
      margin-left: 8px;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.8em;
    }

    .party-type-badge.natural {
      background-color: #2196f3;
      color: white;
    }

    .party-type-badge.legal {
      background-color: #ff9800;
      color: white;
    }

    .party-info {
      display: block;
      font-size: 0.8em;
      color: rgba(0,0,0,0.6);
    }

    /* 历史案件搜索结果样式 */
    .legacy-results {
      margin-top: 20px;
      border-top: 1px solid #ddd;
      padding-top: 10px;
      background-color: #ffffff;
      position: relative;
      z-index: 998;
    }

    .legacy-header {
      margin-bottom: 10px;
      background-color: #ffffff;
      padding: 4px 0;
    }

    .legacy-result-item {
      padding: 8px 16px;
      border-bottom: 1px solid #eee;
      background-color: #ffffff;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .legacy-result-item:hover {
      background-color: #f0f0f0;
    }

    .legacy-badge {
      margin-left: 8px;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.8em;
      background-color: #673ab7;
      color: white;
    }
  `],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatAutocompleteModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    FormsModule,
    MatProgressSpinnerModule
  ]
})
export class AddPartyDialogComponent implements OnInit, OnDestroy {
  searchTerm: string = '';
  filteredParties: any[] = [];
  selectedParty: any = null;
  partyType: string = PartyType.PLAINTIFF;
  isClient: boolean = false;
  internalNumber: string = '';
  remarks: string = '';
  isLoading: boolean = false;
  caseType: string = '民事案件';

  private searchTimeout: any; // 用于防抖

  // 在模板中使用枚举和工具函数
  PartyType = PartyType;
  getPartyTypeDisplay = getPartyTypeDisplay;

  constructor(
    private http: HttpClient
  ) {}

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  searchParties() {
    // 清除之前的定时器
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    if (!this.searchTerm || this.searchTerm.trim().length === 0) {
      this.filteredParties = [];
      return;
    }

    // 缩短防抖延迟时间，提高响应速度
    this.searchTimeout = setTimeout(() => {
      this.performSearch();
    }, 200);
  }

  // 添加粘贴事件处理，确保粘贴后能立即搜索
  onPaste() {
    // 使用 setTimeout 确保粘贴的内容已经更新到 ngModel
    setTimeout(() => {
      this.performSearchImmediately();
    }, 50);
  }

  // 立即执行搜索，跳过防抖机制
  performSearchImmediately() {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    if (!this.searchTerm || this.searchTerm.trim().length === 0) {
      this.filteredParties = [];
      return;
    }

    console.log('立即搜索 - 搜索词：', this.searchTerm.trim());
    this.performSearch();
  }

  private performSearch() {
    this.isLoading = true;
    // 对搜索词进行更好的处理：去除首尾空格，但保留中间的空格
    const searchTerm = this.searchTerm.trim();
    const lowerSearchTerm = searchTerm.toLowerCase();

    // 如果搜索词为空，直接返回
    if (!searchTerm) {
      this.filteredParties = [];
      this.isLoading = false;
      return;
    }

    // 添加调试信息
    console.log('执行搜索 - 搜索词：', searchTerm, '长度：', searchTerm.length);

    this.http.get<any[]>(`${environment.apiUrl}/natural-persons/`).pipe(
      switchMap(naturalPersons => {
        // 改进过滤逻辑：处理姓名中可能的空格和特殊字符
        const filteredNaturalPersons = naturalPersons.filter(person => {
          if (!person.name) return false;

          const personName = person.name.trim().toLowerCase();
          const personIdNum = person.id_number ? person.id_number.trim().toLowerCase() : '';
          const personPhone = person.phone_number ? person.phone_number.trim() : '';

          // 检查是否匹配：
          // 1. 精确匹配
          // 2. 包含匹配
          // 3. 去除空格后的匹配（处理输入时可能的空格问题）
          const nameExactMatch = personName === lowerSearchTerm;
          const nameContainsMatch = personName.includes(lowerSearchTerm);
          const nameNoSpaceMatch = personName.replace(/\s+/g, '') === lowerSearchTerm.replace(/\s+/g, '');

          const idExactMatch = personIdNum && personIdNum === lowerSearchTerm;
          const idContainsMatch = personIdNum && personIdNum.includes(lowerSearchTerm);

          const phoneMatch = personPhone && personPhone.includes(searchTerm);

          return nameExactMatch || nameContainsMatch || nameNoSpaceMatch ||
                 idExactMatch || idContainsMatch || phoneMatch;
        });

        console.log('自然人搜索结果数量：', filteredNaturalPersons.length);

        const naturalResults = filteredNaturalPersons.map(person => ({
          id: person.id,
          name: person.name,
          type: 'natural' as const,
          info: person.id_number || person.phone_number || ''
        }));

        return this.http.get<any[]>(`${environment.apiUrl}/legal-entities/`).pipe(
          map(legalEntities => {
            // 改进单位过滤逻辑
            const filteredLegalEntities = legalEntities.filter(entity => {
              if (!entity.name) return false;

              const entityName = entity.name.trim().toLowerCase();
              const repName = entity.representative_name ? entity.representative_name.trim().toLowerCase() : '';

              // 检查是否匹配：
              // 1. 精确匹配
              // 2. 包含匹配
              // 3. 去除空格后的匹配
              const nameExactMatch = entityName === lowerSearchTerm;
              const nameContainsMatch = entityName.includes(lowerSearchTerm);
              const nameNoSpaceMatch = entityName.replace(/\s+/g, '') === lowerSearchTerm.replace(/\s+/g, '');

              const repExactMatch = repName && repName === lowerSearchTerm;
              const repContainsMatch = repName && repName.includes(lowerSearchTerm);

              return nameExactMatch || nameContainsMatch || nameNoSpaceMatch ||
                     repExactMatch || repContainsMatch;
            });

            console.log('单位搜索结果数量：', filteredLegalEntities.length);

            const legalResults = filteredLegalEntities.map(entity => ({
              id: entity.id,
              name: entity.name,
              type: 'legal' as const,
              info: `代表人: ${entity.representative_name || '未知'}`
            }));

            // 合并结果并按匹配度排序
            const allResults = [...naturalResults, ...legalResults];

            // 排序：精确匹配优先，然后按名称长度排序
            allResults.sort((a, b) => {
              const aExact = a.name.toLowerCase() === lowerSearchTerm;
              const bExact = b.name.toLowerCase() === lowerSearchTerm;

              if (aExact && !bExact) return -1;
              if (!aExact && bExact) return 1;

              return a.name.length - b.name.length;
            });

            return allResults;
          })
        );
      }),
      catchError(error => {
        console.error('搜索当事人出错：', error);
        return of([]);
      })
    ).subscribe({
      next: (results) => {
        console.log('最终搜索结果：', results);
        this.filteredParties = results;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('搜索请求失败：', error);
        this.filteredParties = [];
        this.isLoading = false;
      }
    });
  }

  // 直接从列表选择当事人
  selectPartyDirectly(party: any) {
    this.selectedParty = party;
    // 选择当事人后清空搜索结果
    this.searchTerm = '';
    this.filteredParties = [];

    // 清除搜索定时器
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }
}

@Component({
  selector: 'app-case-create',
  templateUrl: './case-create.component.html',
  styleUrls: ['./case-create.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatCardModule,
    MatSnackBarModule,
    MatIconModule,
    MatAutocompleteModule,
    MatRadioModule,
    MatCheckboxModule,
    MatDialogModule
  ]
})
export class CaseCreateComponent implements OnInit {
  caseForm!: FormGroup;
  isSubmitting = false;
  parties: CaseParty[] = []; // 存储添加的所有当事人
  customProperties: { name: string; value: string }[] = [];

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private caseService: CaseService,
    private http: HttpClient,
    private router: Router,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    // 创建表单，移除对方当事人和委托人相关字段
    this.caseForm = this.fb.group({
      // 基本信息
      case_cause: ['', [Validators.required, Validators.maxLength(200)]],
      case_type: ['民事案件', [Validators.required]], // 默认为民事案件
      is_sensitive: [false], // 是否为重大敏感案件
      is_risk_agency: [false], // 是否风险代理

      // 案件信息
      accepting_authority: [''],
      contract_number: [''],
      court_case_number: [''],
      litigation_request: [''],
      litigation_amount: [''],
      agreed_attorney_fee: [''],
      agency_stage: [''],
      case_summary: ['', [Validators.required]],
      comments: [''],
      custom_property_name: [''],
      custom_property_value: ['']
    });
  }

  // 根据案件类型获取对应的当事人类型显示文本
  getPartyTypeDisplayForCase(partyType: string): string {
    const caseType = this.caseForm.get('case_type')?.value;
    return getPartyTypeDisplay(partyType, caseType);
  }

  // 当案件类型变更时调用
  onCaseTypeChanged(): void {
    // 如果已经添加了当事人，提示用户检查当事人角色
    if (this.parties.length > 0) {
      this.snackBar.open('案件类型已变更，请检查当事人角色是否需要调整', '了解', { duration: 5000 });
    }
  }

  // 打开添加当事人对话框
  openAddPartyDialog(): void {
    const dialogRef = this.dialog.open(AddPartyDialogComponent, {
      width: '600px',
      maxHeight: '80vh',
      disableClose: false,
      autoFocus: true,
      data: { caseType: this.caseForm.get('case_type')?.value }
    });

    // 设置案件类型
    const instance = dialogRef.componentInstance as AddPartyDialogComponent;
    instance.caseType = this.caseForm.get('case_type')?.value;

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // 添加当事人到列表
        this.parties.push(result);
      }
    });
  }

  // 移除当事人
  removeParty(index: number): void {
    this.parties.splice(index, 1);
  }

  // 添加自定义属性
  addCustomProperty(): void {
    const name = this.caseForm.get('custom_property_name')?.value;
    const value = this.caseForm.get('custom_property_value')?.value;

    if (name && value) {
      this.customProperties.push({ name, value });
      // 清空输入框
      this.caseForm.patchValue({
        custom_property_name: '',
        custom_property_value: ''
      });
    }
  }

  // 删除自定义属性
  removeCustomProperty(index: number): void {
    this.customProperties.splice(index, 1);
  }

  // 计算对方当事人的值
  private getOpposingParties(): string {
    const caseType = this.caseForm.get('case_type')?.value;

    // 只有民事案件、行政案件和其他案件才有对方当事人
    if (!['民事案件', '行政案件', '其他案件'].includes(caseType)) {
      return '';
    }

    // 确定我方角色（根据委托人角色）
    const clientParties = this.parties.filter(party => party.is_client);

    // 如果没有委托人，返回空字符串
    if (clientParties.length === 0) {
      return '';
    }

    // 获取我方当事人类型（假设所有委托人角色相同）
    const clientType = clientParties[0].party_type;

    let opposingParties: string[] = [];

    if (clientType === PartyType.PLAINTIFF) {
      // 如果我方是原告，对方是被告和第三人
      opposingParties = this.parties
        .filter(party => party.party_type === PartyType.DEFENDANT || party.party_type === PartyType.THIRD_PARTY)
        .map(party => party.name);
    } else if (clientType === PartyType.DEFENDANT || clientType === PartyType.THIRD_PARTY) {
      // 如果我方是被告或第三人，对方是原告
      opposingParties = this.parties
        .filter(party => party.party_type === PartyType.PLAINTIFF)
        .map(party => party.name);
    }

    // 用顿号连接对方当事人名称
    return opposingParties.join('、');
  }

  onSubmit(): void {
    if (this.caseForm.invalid || this.parties.length === 0) {
      if (this.parties.length === 0) {
        this.snackBar.open('请至少添加一个当事人', '关闭', { duration: 3000 });
      }
      return;
    }

    this.isSubmitting = true;
    const formValue = this.caseForm.value;

    // 转换当事人数据结构，以匹配后端API期望的格式
    const convertedParties = this.parties.map(party => {
      // 根据entityType确定应该设置natural_person_id还是legal_entity_id
      return {
        party_type: party.party_type,
        natural_person_id: party.entityType === 'natural' ? party.entityId : null,
        legal_entity_id: party.entityType === 'legal' ? party.entityId : null,
        is_client: party.is_client,
        internal_number: party.internal_number || '',
        remarks: party.remarks || ''
      };
    });

    // 构建包含content对象的数据结构
    const caseData: any = {
      case_cause: formValue.case_cause,
      is_sensitive: formValue.is_sensitive, // 添加敏感案件标记
      is_risk_agency: formValue.is_risk_agency, // 添加风险代理标记
      agreed_lawyer_fee: formValue.agreed_attorney_fee ? parseFloat(formValue.agreed_attorney_fee) : null, // 存储在正确字段
      content: {
        '案件类型': formValue.case_type,
        '受理机关': formValue.accepting_authority,
        '委托合同编号': formValue.contract_number,
        '法院案件编号': formValue.court_case_number,
        '诉讼请求': formValue.litigation_request,
        '诉讼标的额': formValue.litigation_amount,
        '代理阶段': formValue.agency_stage,
        '案情摘要': formValue.case_summary,
        '备注': formValue.comments,
        '对方当事人': this.getOpposingParties() // 添加新的"对方当事人"字段
      },
      parties: convertedParties // 使用转换后的当事人数据
    };

    // 添加自定义属性到content中
    if (this.customProperties.length > 0) {
      this.customProperties.forEach(prop => {
        caseData.content[prop.name] = prop.value;
      });
    }

    // 提交案件
    this.caseService.createCase(caseData).subscribe({
      next: (response) => {
        this.isSubmitting = false;
        // 显示成功消息
        this.snackBar.open('案件创建成功', '关闭', { duration: 3000 });
        // 导航到案件详情页
        this.router.navigate(['/cases', response.id]);
      },
      error: (error) => {
        this.isSubmitting = false;
        // 显示错误消息
        this.snackBar.open('案件创建失败', '关闭', { duration: 3000 });
        console.error('创建案件出错:', error);
      }
    });
  }

  onCancel(): void {
    this.router.navigate(['/cases']);
  }
}
