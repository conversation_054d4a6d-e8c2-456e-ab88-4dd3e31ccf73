<div class="case-detail-container">
  <button mat-button [routerLink]="['/cases']" class="back-button">
    <mat-icon>arrow_back</mat-icon> 返回案件列表
  </button>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <mat-card *ngIf="case && !isLoading && !error">
    <mat-card-header>
      <div class="header-content">
        <div class="title-section">
          <mat-card-title>{{ case.case_cause }}</mat-card-title>
          <mat-card-subtitle>案件编号: {{ case.case_number }}</mat-card-subtitle>
        </div>
        <div class="action-buttons">
          <button mat-raised-button color="primary" [routerLink]="['/cases', case.id, 'export']">
            <mat-icon>description</mat-icon>
            导出文档
          </button>
          <button mat-raised-button color="warn" 
                  *ngIf="isOwner && canDeleteCase()"
                  (click)="deleteCase()"
                  class="delete-button">
            <mat-icon>delete</mat-icon>
            删除案件
          </button>
        </div>
      </div>
    </mat-card-header>

    <mat-card-content>
      <!-- 标签页 -->
      <mat-tab-group>
        <!-- 第一个标签页：案件信息 -->
        <mat-tab label="案件信息">
          <div class="tab-content">
            <div class="info-section">
              <h3>基本信息</h3>
              <div class="status-section">
                <span class="status-badge" [ngClass]="case.status.toLowerCase()">
                  {{ getStatusText(case.status) }}
                </span>
                <button mat-raised-button color="primary"
                        *ngIf="isOwner && canMoveToNextStatus() && getNextStatus(case.status)"
                        (click)="moveToNextStatus()">
                  <mat-icon>arrow_forward</mat-icon>
                  移至"{{ getStatusText(getNextStatus(case.status)) }}"
                </button>
                <span class="payment-status" [class.paid]="case.is_paid">
                  <mat-icon>{{ case.is_paid ? 'paid' : 'payment' }}</mat-icon>
                  {{ case.is_paid ? '已缴费' : '未缴费' }}
                </span>
                <!-- 行政人员标记缴费按钮 -->
                <button mat-raised-button color="accent"
                        *ngIf="canMarkAsPaid()"
                        (click)="markAsPaid()"
                        [disabled]="isMarkingPaid"
                        class="mark-paid-button">
                  <mat-icon>account_balance</mat-icon>
                  {{ isMarkingPaid ? '处理中...' : '标记为已缴费' }}
                </button>
              </div>
              <div class="sensitive-case-section">
                <mat-checkbox
                  [checked]="case.is_sensitive"
                  [disabled]="!canEditSensitiveStatus()"
                  (change)="toggleSensitiveStatus($event)">
                  重大敏感案件
                </mat-checkbox>
                <mat-icon
                  *ngIf="!canEditSensitiveStatus()"
                  matTooltip="主任审批后不可修改"
                  class="info-icon">info</mat-icon>
              </div>
              <div class="risk-agency-section">
                <mat-checkbox
                  [checked]="case.is_risk_agency"
                  [disabled]="!canEditRiskAgencyStatus()"
                  (change)="toggleRiskAgencyStatus($event)">
                  是否风险代理
                </mat-checkbox>
                <mat-icon
                  *ngIf="!canEditRiskAgencyStatus()"
                  matTooltip="主任审批后不可修改"
                  class="info-icon">info</mat-icon>
              </div>
              <p><strong>案件编号:</strong> {{ case.case_number }}</p>
              <p><strong>案由:</strong> {{ case.case_cause }}</p>
              <p><strong>承办律师:</strong> {{ getUserDisplayName(case.lawyer) }}</p>
              <p><strong>创建时间:</strong> {{ case.created_at | date:'yyyy年MM月dd日' }}</p>
              <p><strong>更新时间:</strong> {{ case.updated_at | date:'yyyy年MM月dd日' }}</p>
            </div>

            <!-- 协作律师管理区域 -->
            <div class="info-section">
              <h3>协作律师</h3>
              <div class="collaborating-lawyers-section">

                <!-- 协作律师列表 -->
                <div class="lawyer-list" *ngIf="case.collaborating_lawyers && case.collaborating_lawyers.length > 0">
                  <div *ngFor="let lawyer of case.collaborating_lawyers" class="lawyer-item">
                    <div class="lawyer-info">
                      <span class="lawyer-name">{{ getUserDisplayName(lawyer) }}</span>
                    </div>
                    <button mat-icon-button color="warn" *ngIf="isOwner" (click)="removeCollaborator(lawyer.id)" aria-label="移除律师">
                      <mat-icon>delete</mat-icon>
                    </button>
                  </div>
                </div>

                <!-- 详细协作律师信息 -->
                <div class="lawyer-collaborations" *ngIf="collaborations && collaborations.length > 0">
                  <h4>协作详情</h4>
                  <div *ngFor="let collab of collaborations" class="collaboration-item">
                    <div class="lawyer-info">
                      <span class="lawyer-name">{{ getUserDisplayName(collab.lawyer) }}</span>
                      <span class="joined-at">加入时间: {{ collab.joined_at | date:'yyyy年MM月dd日' }}</span>
                      <div class="fee-share-section">
                        <span class="fee-share-label">费用分成比例:</span>
                        <span class="fee-share-value">{{ (collab.fee_share_ratio * 100).toFixed(2) }}%</span>
                        <button mat-icon-button color="primary" *ngIf="isOwner" (click)="editFeeShareRatio(collab)"
                                matTooltip="编辑协作详情" class="edit-fee-button">
                          <mat-icon>edit</mat-icon>
                        </button>
                      </div>
                      <p *ngIf="collab.remarks" class="remarks">{{ collab.remarks }}</p>
                    </div>
                  </div>
                </div>

                <!-- 无协作律师时显示 -->
                <div *ngIf="(!case.collaborating_lawyers || case.collaborating_lawyers.length === 0) && (!collaborations || collaborations.length === 0)" class="no-lawyers">
                  <p>暂无协作律师</p>
                </div>

                <!-- 添加协作律师 -->
                <div class="add-lawyer-section" *ngIf="isOwner">
                  <button mat-raised-button color="primary" (click)="openAddLawyerDialog()">
                    <mat-icon>person_add</mat-icon>
                    添加协作律师
                  </button>
                </div>
              </div>
            </div>

            <!-- 案件费用 -->
            <div class="info-section">
              <h3>案件费用</h3>
              <div class="case-fees-section">

                <!-- 商定律师费显示 -->
                <div class="agreed-fee-section">
                  <div class="content-item">
                    <strong>商定律师费：</strong>
                    <span *ngIf="case.agreed_lawyer_fee">{{ case.agreed_lawyer_fee | number:'1.2-2' }} 元</span>
                    <span *ngIf="!case.agreed_lawyer_fee && case.content && case.content['商定律师费']">{{ case.content['商定律师费'] }}</span>
                    <span *ngIf="!case.agreed_lawyer_fee && !(case.content && case.content['商定律师费'])">未设定</span>
                    <!-- 编辑商定律师费按钮 -->
                    <button mat-icon-button color="primary" *ngIf="isOwner" (click)="editAgreedLawyerFee()"
                            matTooltip="编辑商定律师费" class="edit-fee-button">
                      <mat-icon>edit</mat-icon>
                    </button>
                  </div>

                  <!-- 事务所提成计算（当有商定律师费时显示） -->
                  <div *ngIf="case.agreed_lawyer_fee || (case.content && case.content['商定律师费'])" class="fee-distribution">
                    <div class="fee-distribution-item">
                      <span class="fee-label">事务所提成 (12%):</span>
                      <span class="fee-amount" *ngIf="case.agreed_lawyer_fee">{{ (case.agreed_lawyer_fee * 0.12) | number:'1.2-2' }} 元</span>
                      <span class="fee-amount" *ngIf="!case.agreed_lawyer_fee && case.content && case.content['商定律师费']">{{ calculateFeeDistribution(case.content['商定律师费'])?.firmAmount }} 元</span>
                    </div>
                    <div class="fee-distribution-item">
                      <span class="fee-label">律师所得:</span>
                      <span class="fee-amount" *ngIf="case.agreed_lawyer_fee">{{ (case.agreed_lawyer_fee * 0.88) | number:'1.2-2' }} 元</span>
                      <span class="fee-amount" *ngIf="!case.agreed_lawyer_fee && case.content && case.content['商定律师费']">{{ calculateFeeDistribution(case.content['商定律师费'])?.lawyerAmount }} 元</span>
                    </div>
                  </div>
                </div>

                <!-- 费用分成详情 -->
                <div class="main-lawyer-share-section">
                  <h4>费用分成详情</h4>
                  <div class="fee-share-summary">
                    <!-- 主办律师分成详情 -->
                    <div class="main-lawyer-share">
                      <span class="share-label">承办律师 ({{ getUserDisplayName(case.lawyer) }}):</span>
                      <div class="share-values">
                        <span class="share-value main-lawyer"
                              [class.warning]="!isFeeShareValid()">{{ getMainLawyerFeeSharePercentage() }}%</span>
                        <span class="share-amount main-lawyer" *ngIf="getTotalLawyerFee() > 0">
                          ({{ getMainLawyerFeeAmount() | number:'1.2-2' }} 元)
                        </span>
                        <!-- 主办律师提款按钮 -->
                        <ng-container *ngIf="isCurrentUserMainLawyer()">
                          <!-- 有剩余可提取金额且无待审批申请：显示申请提款按钮 -->
                          <button *ngIf="!hasMainLawyerPendingWithdrawal && getTotalLawyerFee() > 0 && hasMainLawyerAvailableAmount()"
                                  mat-button color="primary"
                                  style="margin-left: 8px; font-size: 12px; padding: 4px 8px; min-width: auto; height: 28px;"
                                  (click)="openWithdrawalDialog(getMainLawyerFeeAmount())">
                            <mat-icon style="font-size: 16px; width: 16px; height: 16px; margin-right: 4px;">account_balance_wallet</mat-icon>申请提款
                          </button>
                          <!-- 有待审批申请：显示提款申请中 -->
                          <span *ngIf="hasMainLawyerPendingWithdrawal"
                                style="display: inline-flex; align-items: center; margin-left: 8px; color: #ff9800; font-size: 12px;">
                            <mat-icon style="font-size: 16px; width: 16px; height: 16px; margin-right: 4px;">pending</mat-icon>提款申请中
                          </span>
                          <!-- 没有剩余可提取金额：显示已提全部款项 -->
                          <span *ngIf="!hasMainLawyerPendingWithdrawal && getTotalLawyerFee() > 0 && !hasMainLawyerAvailableAmount()"
                                style="display: inline-flex; align-items: center; margin-left: 8px; color: #28a745; font-size: 12px;">
                            <mat-icon style="font-size: 16px; width: 16px; height: 16px; margin-right: 4px;">check_circle</mat-icon>已提全部款项
                          </span>
                        </ng-container>
                      </div>
                    </div>

                    <!-- 各个协作律师的具体分成比例和金额 -->
                    <div class="individual-collaborators" *ngIf="collaborations && collaborations.length > 0">
                      <div *ngFor="let collab of collaborations" class="individual-collaborator-share">
                        <div class="collaborator-info">
                          <span class="share-label">协作律师 ({{ getUserDisplayName(collab.lawyer) }}):</span>
                          <div class="share-values">
                            <span class="share-value collaborator-individual">{{ (collab.fee_share_ratio * 100).toFixed(2) }}%</span>
                            <span class="share-amount collaborator-individual" *ngIf="getTotalLawyerFee() > 0">
                              ({{ getCollaboratorFeeAmount(collab.fee_share_ratio) | number:'1.2-2' }} 元)
                            </span>
                            <!-- 协作律师提款按钮 -->
                            <ng-container *ngIf="isCurrentUserCollaborator(collab.lawyer.id)">
                              <!-- 有剩余可提取金额且无待审批申请：显示申请提款按钮 -->
                              <button *ngIf="!getCollaboratorPendingWithdrawalStatus(collab.lawyer.id) && getTotalLawyerFee() > 0 && hasCollaboratorAvailableAmount(collab.lawyer.id)"
                                      mat-button color="primary"
                                      style="margin-left: 8px; font-size: 12px; padding: 4px 8px; min-width: auto; height: 28px;"
                                      (click)="openWithdrawalDialog(getCollaboratorFeeAmount(collab.fee_share_ratio))">
                                <mat-icon style="font-size: 16px; width: 16px; height: 16px; margin-right: 4px;">account_balance_wallet</mat-icon>申请提款
                              </button>
                              <!-- 有待审批申请：显示提款申请中 -->
                              <span *ngIf="getCollaboratorPendingWithdrawalStatus(collab.lawyer.id)"
                                    style="display: inline-flex; align-items: center; margin-left: 8px; color: #ff9800; font-size: 12px;">
                                <mat-icon style="font-size: 16px; width: 16px; height: 16px; margin-right: 4px;">pending</mat-icon>提款申请中
                              </span>
                              <!-- 没有剩余可提取金额：显示已提全部款项 -->
                              <span *ngIf="!getCollaboratorPendingWithdrawalStatus(collab.lawyer.id) && getTotalLawyerFee() > 0 && !hasCollaboratorAvailableAmount(collab.lawyer.id)"
                                    style="display: inline-flex; align-items: center; margin-left: 8px; color: #28a745; font-size: 12px;">
                                <mat-icon style="font-size: 16px; width: 16px; height: 16px; margin-right: 4px;">check_circle</mat-icon>已提全部款项
                              </span>
                            </ng-container>
                          </div>
                        </div>
                        <button mat-icon-button color="primary" *ngIf="isOwner" (click)="editFeeShareRatio(collab)"
                                matTooltip="编辑协作详情" class="mini-edit-button">
                          <mat-icon>edit</mat-icon>
                        </button>
                      </div>
                    </div>

                    <!-- 协作律师总计 -->
                    <div class="collaborator-total-share" *ngIf="collaborations && collaborations.length > 0">
                      <span class="share-label">协作律师总计:</span>
                      <div class="share-values">
                        <span class="share-value collaborator"
                              [class.warning]="!isFeeShareValid()">{{ getTotalCollaboratorFeeSharePercentage() }}%</span>
                        <span class="share-amount collaborator" *ngIf="getTotalLawyerFee() > 0">
                          ({{ getTotalCollaboratorFeeAmount() | number:'1.2-2' }} 元)
                        </span>
                      </div>
                    </div>

                    <!-- 合计为律师所得部分 -->
                    <div class="total-fee-summary" *ngIf="getTotalLawyerFee() > 0">
                      <span class="share-label">律师所得合计:</span>
                      <div class="share-values">
                        <span class="share-value total">100%</span>
                        <span class="share-amount total">({{ getTotalLawyerFee() | number:'1.2-2' }} 元)</span>
                      </div>
                    </div>

                    <!-- 分成比例警告 -->
                    <div class="fee-share-warning" *ngIf="!isFeeShareValid()">
                      <mat-icon>warning</mat-icon>
                      <span>注意：分成比例总和超过100%，请调整协作律师的分成比例</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="info-section">
              <h3>当事人信息</h3>

              <!-- 关联当事人信息 -->
              <div class="related-client-section" *ngIf="case.natural_person_id || case.legal_entity_id">
                <h4>委托当事人</h4>

                <div *ngIf="clientLoading" class="loading-spinner">
                  <mat-spinner diameter="24"></mat-spinner>
                  <span>加载当事人信息...</span>
                </div>

                <div *ngIf="clientError" class="error-message">
                  {{ clientError }}
                </div>

                <div *ngIf="naturalPerson || legalEntity" class="client-details">
                  <div class="client-header">
                    <span class="client-name">{{ getClientName() }}</span>
                    <span class="client-type-badge"
                         [ngClass]="naturalPerson ? 'natural' : 'legal'">
                      {{ getClientType() }}
                    </span>
                  </div>

                  <div class="client-info">
                    <p *ngFor="let info of getClientInfo()">{{ info }}</p>
                  </div>
                </div>

                <div *ngIf="!clientLoading && !clientError && !naturalPerson && !legalEntity" class="no-client">
                  <p>未找到关联当事人</p>
                </div>
              </div>

              <!-- 当事人列表 -->
              <div *ngIf="formattedParties.length > 0" class="party-list-section">
                @if(case.natural_person_id || case.legal_entity_id) {<h4>案件当事人列表</h4>}
                @else {<h4>当事人列表</h4>}

                @for (party of formattedParties; track $index) {
                <div class="party-info">
                  <div class="party-header">
                    <p>
                      <strong>{{ party.party_type_display }}:</strong>
                      {{ party.natural_person?.name || party.legal_entity?.name || '未知名称' }}

                      @if(party.internal_number) {<span class="internal-number">(内部编号: {{ party.internal_number }})</span>}
                      @if(party.is_client) { <span class="client-badge">本案委托人</span>}
                    </p>

                    @if((isOwner || isCollaborator) && party && party.id && case) { <!-- 添加编辑和删除按钮 -->
                    <div class="party-actions">
                      <button mat-icon-button color="primary" (click)="openEditPartyDialog(party)" class="edit-party-button" matTooltip="编辑当事人">
                        <mat-icon>edit</mat-icon>
                      </button>
                      <button mat-icon-button color="warn" (click)="deleteParty(party)" class="delete-party-button" matTooltip="删除当事人">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                    }
                  </div>

                  @if(party.natural_person) {
                  <div>
                    @if (party.natural_person.id_number) {<p> <strong>身份证号:</strong> {{ party.natural_person.id_number }} </p>}
                    @if (party.natural_person.phone_number) {<p> <strong>联系电话:</strong> {{ party.natural_person.phone_number }} </p>}
                  </div>
                  }

                  @if(party.legal_entity) {
                  <div>
                    @if (party.legal_entity.representative_name) {<p> <strong>法定代表人:</strong> {{ party.legal_entity.representative_name }} </p>}
                    @if (party.legal_entity.representative_id_number) {<p> <strong>代表人身份证号:</strong> {{ party.legal_entity.representative_id_number }} </p>}
                    @if (party.legal_entity.representative_phone_number) {<p> <strong>代表人联系电话:</strong> {{ party.legal_entity.representative_phone_number }} </p>}
                  </div>
                  }

                  @if(party.remarks) {<div class="party-remarks"><p><strong>备注:</strong> {{ party.remarks }}</p></div>}

                  <!-- 历史案件记录折叠区域 -->
                  @if(getPartyHistoryCount(getPartyIdentifier(party)) > 0) {
                  <div class="party-history-section">
                    <div class="party-history-header" (click)="togglePartyHistory(getPartyIdentifier(party))">
                      <mat-icon>{{ isPartyHistoryExpanded(getPartyIdentifier(party)) ? 'expand_more' : 'chevron_right' }}</mat-icon>
                      <span>历史案件记录 ({{ getPartyHistoryCount(getPartyIdentifier(party)) }})</span>
                    </div>

                    <!-- 历史记录详情 -->
                    @if(isPartyHistoryExpanded(getPartyIdentifier(party))) {
                    <div class="party-history-content">
                      @if(isHistoryLoading(getPartyIdentifier(party))) {
                      <div class="loading-spinner-small">
                        <mat-spinner diameter="20"></mat-spinner>
                        <span>加载历史记录...</span>
                      </div>
                      }

                      @for(historyItem of getPartyHistory(getPartyIdentifier(party)); track $index) {
                      <div class="party-history-item">
                        <div class="history-item-header">
                          <span class="history-item-type">{{ historyItem.source }}</span>
                          <span class="history-item-date">{{ historyItem.filing_date | date:'yyyy-MM-dd' }}</span>
                        </div>
                        <p><strong>合同编号:</strong> {{ historyItem.contract_number }}</p>
                        <p><strong>案件类型:</strong> {{ historyItem.case_type }}</p>
                        <p><strong>承办律师:</strong> {{ historyItem.lawyer }}</p>
                      </div>
                      }
                    </div>
                    }
                  </div>
                  }
                </div>
                }

                @if(isOwner || isCollaborator) { <!-- 添加当事人按钮 -->
                <button mat-raised-button color="primary" (click)="openAddPartyDialog()" class="add-party-button">
                  <mat-icon>person_add</mat-icon>
                  添加当事人
                </button>
                }
              </div>

              <!-- 当没有任何当事人信息时显示 -->
              <div *ngIf="(!case.natural_person_id && !case.legal_entity_id) && formattedParties.length === 0" class="no-parties">
                <p>未添加任何当事人信息</p>

                <!-- 添加当事人按钮（当没有当事人时也显示） -->
                <button mat-raised-button color="primary" (click)="openAddPartyDialog()" *ngIf="isOwner || isCollaborator">
                  <mat-icon>person_add</mat-icon>
                  添加当事人
                </button>
              </div>
            </div>

            <div class="info-section">
              <h3>案件详情</h3>
              <div class="content-grid">
                <div class="content-item">
                  <strong>对方当事人：</strong>
                  <span>{{ case.content && case.content['对方当事人'] }}</span>
                </div>
                <div class="content-item">
                  <strong>受理机关：</strong>
                  <span>{{ case.content && case.content['受理机关'] }}</span>
                </div>
                <div class="content-item">
                  <strong>委托合同编号：</strong>
                  <span>{{ case.content && case.content['委托合同编号'] }}</span>
                </div>
                <div class="content-item">
                  <strong>法院案件编号：</strong>
                  <span>{{ case.content && case.content['法院案件编号'] }}</span>
                </div>
                <div class="content-item">
                  <strong>诉讼请求：</strong>
                  <span>{{ case.content && case.content['诉讼请求'] }}</span>
                </div>
                <div class="content-item">
                  <strong>诉讼标的额：</strong>
                  <span>{{ case.content && case.content['诉讼标的额'] }}</span>
                </div>
                <div class="content-item">
                  <strong>代理阶段：</strong>
                  <span>{{ case.content && case.content['代理阶段'] }}</span>
                </div>
                <!-- 自定义属性 -->
                <div class="content-item" *ngFor="let prop of getCustomProperties()">
                  <strong>{{ prop.name }}：</strong>
                  <span>{{ prop.value }}</span>
                </div>
              </div>

              <div class="long-content-section">
                <div class="content-item">
                  <strong>案情摘要：</strong>
                  <div class="content-text pre-formatted">{{ case.content && case.content['案情摘要'] }}</div>
                </div>
                <div class="content-item">
                  <strong>备注：</strong>
                  <div class="content-text pre-formatted">{{ case.content && case.content['备注'] }}</div>
                </div>
              </div>
            </div>

            <div class="info-section" *ngIf="case.approvals && case.approvals.length > 0">
              <h3>审批历史</h3>
              <div class="approval-history">
                <mat-card *ngFor="let approval of case.approvals" class="approval-record">
                  <mat-card-content>
                    <div class="approval-header">
                      <span class="approver">审批人：{{ getUserDisplayName(approval.approver) }}</span>
                      <span class="approval-time">{{ approval.created_at | date:'yyyy年MM月dd日 HH:mm:ss' }}</span>
                    </div>
                    <div class="approval-status">
                      审批阶段：{{ getStatusText(approval.status_when_approved) }}
                    </div>
                    <div class="approval-action" [ngClass]="approval.action">
                      {{ approval.action_display }}
                    </div>
                    <div class="approval-comment">
                      {{ approval.comment }}
                    </div>
                  </mat-card-content>
                </mat-card>
              </div>
            </div>
          </div>
        </mat-tab>

        <!-- 第二个标签页：文档管理 -->
        <mat-tab label="文档管理">
          <div class="tab-content">
            <div class="info-section">
              <app-case-files [caseId]="case.id"></app-case-files>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </mat-card-content>
  </mat-card>

  <div *ngIf="(canApproveCases$ | async) && showApprovalSection() && case && !isLoading && !error" class="approval-section">
    <mat-card>
      <mat-card-header>
        <mat-card-title>
          {{case.status === 'ADMIN_REVIEWING' ? '行政审批' : '主任审批'}}
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>审批意见</mat-label>
          <textarea matInput [(ngModel)]="approvalComment"
                    placeholder="请输入审批意见"
                    rows="4"
                    required></textarea>
        </mat-form-field>
        <div class="approval-buttons">
          <button mat-raised-button color="primary" (click)="approveCase('approve')">
            审批通过
          </button>
          <button mat-raised-button color="warn" (click)="approveCase('reject')">
            审批不通过
          </button>
          <button mat-raised-button (click)="approveCase('revise')">
            需要修改
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
