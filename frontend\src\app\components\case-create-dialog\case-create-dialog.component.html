<h2 mat-dialog-title>创建新案件</h2>

<form [formGroup]="caseForm" (ngSubmit)="onSubmit()">
  <div mat-dialog-content class="dialog-content">
    <div class="form-section">
      <h3>基本信息</h3>
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>案由</mat-label>
        <input matInput formControlName="case_cause" placeholder="请输入案由">
        <mat-error *ngIf="caseForm.get('case_cause')?.hasError('required')">
          案由为必填项
        </mat-error>
        <mat-error *ngIf="caseForm.get('case_cause')?.hasError('maxlength')">
          案由不能超过200个字符
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>案件类型</mat-label>
        <mat-select formControlName="case_type" required>
          <mat-option value="民事案件">民事案件</mat-option>
          <mat-option value="刑事案件">刑事案件</mat-option>
          <mat-option value="行政案件">行政案件</mat-option>
          <mat-option value="其他案件">其他案件</mat-option>
        </mat-select>
        <mat-error *ngIf="caseForm.get('case_type')?.hasError('required')">
          请选择案件类型
        </mat-error>
      </mat-form-field>

      <div class="checkbox-field">
        <mat-checkbox formControlName="is_sensitive">
          重大敏感案件
        </mat-checkbox>
        <span class="helper-text">标记为重大敏感案件后，主任审批后将无法修改此状态</span>
      </div>

      <div class="checkbox-field">
        <mat-checkbox formControlName="is_risk_agency">
          是否风险代理
        </mat-checkbox>
        <span class="helper-text">标记为风险代理案件后，主任审批后将无法修改此状态</span>
      </div>
    </div>

    <div class="form-section">
      <h3>当事人信息</h3>
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>委托人</mat-label>
        <input matInput formControlName="client" placeholder="请输入委托人姓名">
        <mat-error *ngIf="caseForm.get('client')?.hasError('required')">
          委托人为必填项
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>联系电话</mat-label>
        <input matInput formControlName="phone" placeholder="请输入联系电话">
        <mat-error *ngIf="caseForm.get('phone')?.hasError('required')">
          联系电话为必填项
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>对方当事人</mat-label>
        <input matInput formControlName="opposing_party" placeholder="请输入对方当事人">
      </mat-form-field>
    </div>

    <div class="form-section">
      <h3>案件信息</h3>
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>受理机关</mat-label>
        <input matInput formControlName="accepting_authority" placeholder="请输入受理机关">
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>委托合同编号</mat-label>
        <input matInput formControlName="contract_number" placeholder="请输入委托合同编号">
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>法院案件编号</mat-label>
        <input matInput formControlName="court_case_number" placeholder="请输入法院案件编号">
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>案情摘要</mat-label>
        <textarea matInput formControlName="case_summary" rows="4" placeholder="请输入案情摘要"></textarea>
        <mat-error *ngIf="caseForm.get('case_summary')?.hasError('required')">
          案情摘要为必填项
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>备注</mat-label>
        <textarea matInput formControlName="comments" rows="2" placeholder="请输入备注"></textarea>
      </mat-form-field>
    </div>
  </div>

  <div mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">取消</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="caseForm.invalid">保存</button>
  </div>
</form>
