export interface WithdrawalRequest {
  id: number;
  case: {
    id: number;
    case_number: string;
    case_cause: string;
    lawyer: number;
  };
  requester: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  amount: number;
  status: string;
  status_display: string;
  approver?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
  } | null;
  deduction_tax: number;
  other_deductions?: string;
  remarks?: string;
  final_amount: number;
  created_at: string;
  updated_at: string;
  can_approve: boolean;
  can_delete: boolean;
}

export interface WithdrawalRequestCreate {
  case_id: number;
  amount: number;
  remarks?: string;
}

export interface WithdrawalRequestStatus {
  case_id: number;
  has_pending_request: boolean;
}

export interface WithdrawalApprovalData {
  deduction_tax: number;
  other_deductions?: string;
  remarks?: string;
}
