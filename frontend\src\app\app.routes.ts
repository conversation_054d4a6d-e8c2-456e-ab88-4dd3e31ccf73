import { Routes } from '@angular/router';
import { LoginComponent } from './components/login/login.component';
import { HomeComponent } from './components/home/<USER>';
import { CaseListComponent } from './components/case-list/case-list.component';
import { CaseDetailComponent } from './components/case-detail/case-detail.component';
import { UserProfileComponent } from './components/user-profile/user-profile.component';
import { TemplateListComponent } from './components/template-list/template-list.component';
import { TemplateDetailComponent } from './components/template-detail/template-detail.component';
import { AuthGuard } from './guards/auth.guard';
import { PartyManagementComponent } from './components/party-management/party-management.component';
import { CaseCreateComponent } from './components/case-create/case-create.component';
import { CaseExportComponent } from './components/case-export/case-export.component';
import { PricingComponent } from './components/pricing/pricing.component';
import { LegacyCaseImportComponent } from './features/legacy-case-import/legacy-case-import.component';
import { CourtFeeCalculatorComponent } from './components/court-fee-calculator/court-fee-calculator.component';
import { OfficeExpenseListComponent } from './components/office-expense/office-expense-list.component';
import { SealUsageListComponent } from './components/seal-usage/seal-usage-list.component';
import { FinanceRecordsComponent } from './components/finance-records/finance-records.component';
import { FinanceManagementComponent } from './components/finance-management/finance-management.component';

export const routes: Routes = [
  { path: '', redirectTo: 'cases', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  {
    path: 'cases',
    component: CaseListComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'cases/create',
    component: CaseCreateComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'cases/:id',
    component: CaseDetailComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'cases/:id/export',
    component: CaseExportComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'legacy-cases/import',
    component: LegacyCaseImportComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'users/me',
    component: UserProfileComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'templates',
    component: TemplateListComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'templates/:id',
    component: TemplateDetailComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'parties',
    component: PartyManagementComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'pricing',
    component: PricingComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'court-fee',
    component: CourtFeeCalculatorComponent,
    canActivate: [AuthGuard]
  },
  {
    path: 'finance',
    component: FinanceManagementComponent,
    canActivate: [AuthGuard]
  },
  // Redirect old routes to new unified finance management
  {
    path: 'finance/expenses',
    redirectTo: '/finance',
    pathMatch: 'full'
  },
  {
    path: 'finance/records',
    redirectTo: '/finance',
    pathMatch: 'full'
  },
  {
    path: 'seal-usage',
    component: SealUsageListComponent,
    canActivate: [AuthGuard]
  },
  { path: '**', redirectTo: 'cases' }
];
