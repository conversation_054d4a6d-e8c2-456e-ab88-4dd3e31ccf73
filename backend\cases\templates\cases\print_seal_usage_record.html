<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公章使用登记表 - {{ year }}年</title>
    <style>
        @page {
            size: A4;
            margin: 20mm 15mm;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .no-print {
                display: none;
            }
            
            /* 打印时使用人、审批人、盖章经办人列留空 */
            .print-empty {
                visibility: hidden;
            }
        }
        
        body {
            font-family: "SimSun", "宋体", serif;
            font-size: 12px;
            line-height: 1.2;
            margin: 0;
            padding: 15px;
            background-color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 5px 0;
        }
        
        .header h2 {
            font-size: 14px;
            font-weight: bold;
            margin: 0 0 10px 0;
        }
        
        .header .year {
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .print-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
            margin-bottom: 20px;
            font-size: 11px;
        }
        
        .print-table th,
        .print-table td {
            border: 1px solid #000;
            padding: 4px 2px;
            text-align: center;
            vertical-align: middle;
            word-wrap: break-word;
            font-size: 11px;
        }
        
        .print-table th {
            background-color: white;
            font-weight: bold;
            height: 30px;
        }
        
        .print-table td {
            min-height: 25px;
            height: 25px;
        }
        
        /* 列宽设置 */
        .col-sequence { width: 8%; }
        .col-date { width: 10%; }
        .col-matter { width: 15%; }
        .col-doc-name { width: 12%; }
        .col-quantity { width: 6%; }
        .col-seal-type { width: 8%; }
        .col-recipient { width: 12%; }
        .col-user { width: 8%; }
        .col-approver { width: 8%; }
        .col-operator { width: 8%; }
        .col-remarks { width: 5%; }
        
        /* 突出显示当前记录 */
        .current-record {
            background-color: #f0f0f0;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
        }
        
        .print-date {
            position: fixed;
            bottom: 10px;
            right: 10px;
            font-size: 10px;
            color: #666;
        }
        
        .text-left {
            text-align: left !important;
            padding-left: 4px !important;
        }
    </style>
</head>
<body>
    {% if error %}
    <div class="error-message" style="color: red; text-align: center; font-size: 16px; margin: 50px 0;">
        <h2>错误</h2>
        <p>{{ error }}</p>
    </div>
    {% else %}
    <div class="header">
        <h1>{{ firm_name|default:"广东承境律师事务所" }}</h1>
        <h2>{{ year }}年度公章使用登记表</h2>
        <div class="year">{{ year }}年度</div>
    </div>
    
    <table class="print-table">
        <thead>
            <tr>
                <th class="col-sequence">序号</th>
                <th class="col-date">日期</th>
                <th class="col-matter">事由</th>
                <th colspan="3">盖章</th>
                <th class="col-recipient">受送单位</th>
                <th class="col-user">使用人</th>
                <th class="col-approver">审批人</th>
                <th class="col-operator">盖章经办人</th>
                <th class="col-remarks">备注</th>
            </tr>
            <tr>
                <th></th>
                <th></th>
                <th></th>
                <th class="col-doc-name">文件名称</th>
                <th class="col-quantity">数量</th>
                <th class="col-seal-type">用章种类</th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            {% for record in records %}
            <tr {% if record.id == current_record.id %}class="current-record"{% endif %}>
                <td class="col-sequence">（{{ record.year }}）公章使用{{ record.sequence_number }}号</td>
                <td class="col-date">{{ record.date|date:"m-d" }}</td>
                <td class="col-matter text-left">{{ record.matter }}</td>
                <td class="col-doc-name text-left">{{ record.document_name }}</td>
                <td class="col-quantity">{{ record.quantity }}</td>
                <td class="col-seal-type">{{ record.seal_type_display|default:record.seal_type }}</td>
                <td class="col-recipient text-left">{{ record.recipient_unit }}</td>
                <td class="col-user text-left">{{ record.user_name|default:"-" }}</td>
                <td class="col-approver"></td>
                <td class="col-operator"></td>
                <td class="col-remarks text-left">{{ record.remarks|default:"-" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11">暂无记录</td>
            </tr>
            {% endfor %}
            
            {% comment %}
            填充空行到9行
            {% endcomment %}
            {% for i in empty_rows %}
                <tr>
                    <td class="col-sequence">&nbsp;</td>
                    <td class="col-date">&nbsp;</td>
                    <td class="col-matter">&nbsp;</td>
                    <td class="col-doc-name">&nbsp;</td>
                    <td class="col-quantity">&nbsp;</td>
                    <td class="col-seal-type">&nbsp;</td>
                    <td class="col-recipient">&nbsp;</td>
                    <td class="col-user">&nbsp;</td>
                    <td class="col-approver">&nbsp;</td>
                    <td class="col-operator">&nbsp;</td>
                    <td class="col-remarks">&nbsp;</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}
    
    <div class="no-print" style="margin-top: 20px; text-align: center;">
        <button onclick="window.print()" style="padding: 8px 16px; margin-right: 10px; background-color: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">打印</button>
        <button onclick="window.close()" style="padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
    </div>
    
    <div class="print-date no-print">
        打印时间: <span id="printDate"></span>
    </div>
    
    <script>
        // 设置打印时间
        document.getElementById('printDate').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html> 