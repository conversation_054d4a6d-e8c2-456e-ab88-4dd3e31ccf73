<div class="case-files-container">
  <!-- 顶部导航栏 -->
  <div class="case-files-header">
    <div class="header-content">
      <h2>案件文档管理</h2>
      <div class="path-navigation">
        <button mat-icon-button (click)="goBack()" [disabled]="isLoading" class="nav-button">
          <mat-icon>arrow_back</mat-icon>
        </button>
        <div class="breadcrumb">
          <span [class.active]="!currentFolder" (click)="loadRootFolders()">根目录</span>
          <span *ngFor="let folder of parentFolders; let i = index" class="path-separator">/</span>
          <span *ngFor="let folder of parentFolders; let i = index" 
                [class.active]="i === parentFolders.length - 1"
                (click)="navigateToFolder(folder)">
            {{ folder.name }}
            <span *ngIf="i < parentFolders.length - 1" class="path-separator">/</span>
          </span>
          <span *ngIf="currentFolder" class="path-separator">/</span>
          <span *ngIf="currentFolder" class="active">{{ currentFolder.name }}</span>
        </div>
      </div>
    </div>
  </div>

  <div class="tool-bar">
    <!-- 搜索框 -->
    <div class="search-container">
      <mat-form-field appearance="outline" class="search-field">
        <mat-icon matPrefix>search</mat-icon>
        <input matInput placeholder="搜索文件..." [(ngModel)]="searchTerm" (input)="onSearchInput()">
        <button mat-icon-button matSuffix *ngIf="searchTerm" (click)="clearSearch()">
          <mat-icon>close</mat-icon>
        </button>
      </mat-form-field>
    </div>

    <!-- 文件上传部分 -->
    <div class="upload-container">
      <input type="file" id="file-upload" #fileInput (change)="onFileSelected($event)" style="display: none;">
      <button mat-flat-button color="primary" (click)="fileInput.click()" [disabled]="isLoading" class="upload-button">
        <mat-icon>attach_file</mat-icon> 选择文件
      </button>
      <button mat-flat-button color="accent" (click)="uploadFile()" [disabled]="!fileToUpload || isLoading" class="upload-button">
        <mat-icon>cloud_upload</mat-icon> 上传
      </button>
    </div>
  </div>

  <!-- 选择的文件预览 -->
  <div class="file-preview-container" *ngIf="fileToUpload">
    <div class="file-preview-card">
      <div class="file-preview-content">
        <mat-icon class="file-icon">insert_drive_file</mat-icon>
        <div class="file-info">
          <span class="file-name" [title]="fileToUpload.name">{{ fileToUpload.name }}</span>
          <span class="file-size">{{ formatFileSize(fileToUpload.size) }}</span>
        </div>
      </div>
      <button mat-icon-button color="warn" (click)="fileToUpload = null">
        <mat-icon>close</mat-icon>
      </button>
    </div>
  </div>

  <!-- 加载指示器 -->
  <div *ngIf="isLoading" class="loading-indicator">
    <mat-spinner diameter="30"></mat-spinner>
    <span>加载中...</span>
  </div>

  <!-- 主内容区 -->
  <div class="content-container" [class.with-selection]="selectedFiles.length > 0">
    <!-- 文件列表视图 -->
    <div class="files-view-container">
      <div class="view-header">
        <div class="view-tabs">
          <button mat-button [ngClass]="{'active-tab': viewMode === 'list'}" (click)="switchViewMode('list')">
            <mat-icon>view_list</mat-icon> 列表视图
          </button>
          <button mat-button [ngClass]="{'active-tab': viewMode === 'grid'}" (click)="switchViewMode('grid')">
            <mat-icon>grid_view</mat-icon> 网格视图
          </button>
        </div>
        <div class="view-actions">
          <!-- <button mat-button color="primary" (click)="showCreateFolderDialog()" *ngIf="!searchTerm">
            <mat-icon>create_new_folder</mat-icon> 新建文件夹
          </button> -->
        </div>
      </div>

      <!-- 无内容提示 -->
      <div *ngIf="(filteredFiles.length === 0 && filteredFolders.length === 0) && !isLoading" class="empty-content">
        <mat-icon>folder_open</mat-icon>
        <p *ngIf="searchTerm">没有找到匹配的文件或文件夹</p>
        <p *ngIf="!searchTerm">当前文件夹为空</p>
        <button mat-flat-button color="primary" (click)="fileInput.click()" *ngIf="!searchTerm">
          <mat-icon>upload_file</mat-icon> 上传文件
        </button>
      </div>

      <!-- 列表视图 -->
      <div class="list-view" *ngIf="viewMode === 'list' && (filteredFiles.length > 0 || filteredFolders.length > 0)">
        <div class="list-header">
          <div class="select-all">
            <mat-checkbox 
              [checked]="areAllFilesSelected()" 
              [indeterminate]="areSomeFilesSelected()" 
              (change)="toggleAllFiles($event)">
            </mat-checkbox>
          </div>
          <div class="file-name-header">名称</div>
          <div class="file-type-header">类型</div>
          <div class="file-size-header">大小</div>
          <div class="file-actions-header">操作</div>
        </div>

        <!-- 文件夹项 -->
        <div class="list-item folder-item" *ngFor="let folder of filteredFolders" (click)="openFolder(folder)">
          <div class="select-cell"></div>
          <div class="name-cell">
            <mat-icon class="folder-icon">folder</mat-icon>
            <span class="item-name">{{ folder.name }}</span>
          </div>
          <div class="type-cell">文件夹</div>
          <div class="size-cell">-</div>
          <div class="actions-cell" (click)="$event.stopPropagation()">
            <button mat-icon-button color="warn" (click)="deleteFolder(folder)" matTooltip="删除文件夹">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>

        <!-- 文件项 -->
        <div class="list-item file-item" *ngFor="let file of filteredFiles">
          <div class="select-cell">
            <mat-checkbox 
              [checked]="isFileSelected(file)" 
              (change)="toggleFileSelection(file)"
              (click)="$event.stopPropagation()">
            </mat-checkbox>
          </div>
          <div class="name-cell" (click)="viewFile(file)">
            <mat-icon [ngClass]="file.file_type">
              {{
                file.file_type === 'pdf' ? 'picture_as_pdf' :
                (file.file_type === 'doc' || file.file_type === 'docx') ? 'description' :
                (file.file_type === 'xls' || file.file_type === 'xlsx') ? 'table_chart' :
                (file.file_type === 'jpg' || file.file_type === 'jpeg' || file.file_type === 'png') ? 'image' : 'insert_drive_file'
              }}
            </mat-icon>
            <span class="item-name">{{ file.original_filename }}</span>
          </div>
          <div class="type-cell">{{ getFileTypeDisplay(file.file_type) }}</div>
          <div class="size-cell">{{ formatFileSize() }}</div>
          <div class="actions-cell">
            <button mat-icon-button color="primary" (click)="downloadFile(file)" matTooltip="下载文件">
              <mat-icon>download</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteFile(file)" matTooltip="删除文件">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <!-- 网格视图 -->
      <div class="grid-view" *ngIf="viewMode === 'grid' && (filteredFiles.length > 0 || filteredFolders.length > 0)">
        <!-- 文件夹项 -->
        <div class="grid-item folder-item" *ngFor="let folder of filteredFolders" (click)="openFolder(folder)">
          <div class="item-icon">
            <mat-icon class="folder-icon">folder</mat-icon>
          </div>
          <div class="item-details">
            <div class="item-name">{{ folder.name }}</div>
            <div class="item-type">文件夹</div>
          </div>
          <div class="item-actions" (click)="$event.stopPropagation()">
            <button mat-icon-button color="warn" (click)="deleteFolder(folder)" matTooltip="删除文件夹">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>

        <!-- 文件项 -->
        <div class="grid-item file-item" *ngFor="let file of filteredFiles">
          <div class="item-checkbox">
            <mat-checkbox 
              [checked]="isFileSelected(file)" 
              (change)="toggleFileSelection(file)"
              (click)="$event.stopPropagation()">
            </mat-checkbox>
          </div>
          <div class="item-icon" (click)="viewFile(file)">
            <mat-icon [ngClass]="file.file_type">
              {{
                file.file_type === 'pdf' ? 'picture_as_pdf' :
                (file.file_type === 'doc' || file.file_type === 'docx') ? 'description' :
                (file.file_type === 'xls' || file.file_type === 'xlsx') ? 'table_chart' :
                (file.file_type === 'jpg' || file.file_type === 'jpeg' || file.file_type === 'png') ? 'image' : 'insert_drive_file'
              }}
            </mat-icon>
          </div>
          <div class="item-details" (click)="viewFile(file)">
            <div class="item-name">{{ file.original_filename }}</div>
            <div class="item-meta">{{ getFileTypeDisplay(file.file_type) }}</div>
          </div>
          <div class="item-actions" (click)="$event.stopPropagation()">
            <button mat-icon-button color="primary" (click)="downloadFile(file)" matTooltip="下载文件">
              <mat-icon>download</mat-icon>
            </button>
            <button mat-icon-button color="warn" (click)="deleteFile(file)" matTooltip="删除文件">
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 选中文件处理面板 -->
    <div class="selection-panel" *ngIf="selectedFiles.length > 0">
      <div class="panel-header">
        <h3>已选择 {{ selectedFiles.length }} 个文件</h3>
        <button mat-icon-button (click)="clearSelectedFiles()" matTooltip="清除选择">
          <mat-icon>close</mat-icon>
        </button>
      </div>

      <div class="panel-section">
        <h4>文件列表</h4>
        <div cdkDropList class="selected-files-list" (cdkDropListDropped)="onDrop($event)">
          <div class="selected-file-item" *ngFor="let file of selectedFiles" cdkDrag>
            <mat-icon cdkDragHandle class="drag-handle">drag_indicator</mat-icon>
            <mat-icon [ngClass]="file.file_type" class="file-type-icon">
              {{
                file.file_type === 'pdf' ? 'picture_as_pdf' :
                (file.file_type === 'doc' || file.file_type === 'docx') ? 'description' :
                (file.file_type === 'xls' || file.file_type === 'xlsx') ? 'table_chart' :
                (file.file_type === 'jpg' || file.file_type === 'jpeg' || file.file_type === 'png') ? 'image' : 'insert_drive_file'
              }}
            </mat-icon>
            <span class="file-name">{{ file.original_filename }}</span>
            <button mat-icon-button (click)="toggleFileSelection(file)">
              <mat-icon>close</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <div class="panel-section">
        <h4>文件操作</h4>
        <div class="merge-actions">
          <form [formGroup]="mergeForm" (ngSubmit)="mergeToPdf()">
            <mat-form-field appearance="outline" class="output-name-field">
              <mat-label>输出文件名</mat-label>
              <input matInput formControlName="output_filename" placeholder="输入合并后的PDF文件名">
              <mat-error *ngIf="mergeForm.get('output_filename')?.hasError('required')">
                文件名不能为空
              </mat-error>
            </mat-form-field>
            <mat-form-field appearance="outline" class="page-number-field">
              <mat-label>起始页码</mat-label>
              <input matInput type="number" formControlName="start_page_number" placeholder="默认从1开始" min="1">
              <mat-error *ngIf="mergeForm.get('start_page_number')?.hasError('required')">
                起始页码不能为空
              </mat-error>
              <mat-error *ngIf="mergeForm.get('start_page_number')?.hasError('min')">
                起始页码必须大于等于1
              </mat-error>
              <mat-hint>设置PDF页码的起始编号</mat-hint>
            </mat-form-field>
            <div class="action-buttons">
              <button mat-flat-button color="primary" type="submit" [disabled]="mergeForm.invalid || isLoading || selectedFiles.length === 0">
                <mat-icon>merge_type</mat-icon> 合并成PDF
              </button>
              <button mat-flat-button type="button" color="accent" [disabled]="selectedFiles.length === 0" (click)="downloadSelected()">
                <mat-icon>download</mat-icon> 批量下载
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
