import { User } from './case.interface';

export interface OfficeExpense {
  id: number;
  expense_date: string;
  applicant: User;
  purpose: string;
  amount: number;
  description?: string;
  approver?: User;
  created_at: string;
  last_printed_at?: string;
  can_approve?: boolean;
}

export interface OfficeExpenseCreateDto {
  expense_date: string;
  purpose: string;
  amount: number;
  description?: string;
} 