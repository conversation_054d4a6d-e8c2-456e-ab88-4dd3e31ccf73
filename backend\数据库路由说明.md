# 数据库路由功能说明

## 概述

本系统实现了基于访问域名的自动数据库路由功能。根据访问的域名，系统会自动选择相应的数据库：

- **firm2开头的域名**（如 `firm2.example.com`）→ 使用 `firm2` 数据库
- **其他域名** → 使用 `default` 数据库

## 实现原理

### 1. 数据库路由器 (`law_firm/database_router.py`)
- 使用线程本地存储来维护每个请求的数据库选择
- 提供 `get_current_db_alias()` 和 `set_current_db_alias()` 函数
- 实现Django数据库路由器接口

### 2. 中间件 (`law_firm/middleware.py`)  
- 拦截每个HTTP请求
- 根据请求的域名设置当前线程的数据库别名
- 在响应头中添加 `X-Database-Used` 用于调试

### 3. 配置 (`law_firm/settings.py`)
- 添加了 `DomainDatabaseMiddleware` 到中间件列表
- 配置了 `DATABASE_ROUTERS` 指向数据库路由器

## 使用方法

### 1. 自动路由（推荐）
当用户通过不同域名访问系统时，会自动选择对应的数据库：

```python
# 所有ORM操作都会自动使用正确的数据库
users = User.objects.all()
persons = NaturalPerson.objects.filter(name='张三')
```

### 2. 手动指定数据库
如果需要明确指定数据库：

```python
# 明确使用特定数据库
users = User.objects.using('firm2').all()
persons = NaturalPerson.objects.using('default').filter(name='张三')
```

### 3. 利益冲突检索
利益冲突检索功能会搜索所有配置的数据库，确保不遗漏任何相关记录。

## 调试工具

### 1. 调试API
访问 `/api/debug/database/` 查看当前数据库状态：

```json
{
  "domain": "firm2.example.com",
  "current_db_from_router": "firm2", 
  "current_db_from_request": "firm2",
  "databases_config": ["default", "firm2"],
  "connection_status": {
    "default": {"status": "connected", "database_name": "lawcase_db"},
    "firm2": {"status": "connected", "database_name": "lawcase_db2"}
  },
  "middleware_working": true
}
```

### 2. 管理命令
使用管理命令测试数据库路由功能：

```bash
# 测试默认数据库
python manage.py test_db_routing --database=default

# 测试firm2数据库  
python manage.py test_db_routing --database=firm2
```

## 数据库配置

系统支持的数据库：

- **default**: 主数据库 (`lawcase_db`)
- **firm2**: 分所数据库 (`lawcase_db2`)

## 注意事项

### 1. 跨数据库关系
- 不能在不同数据库的模型之间建立外键关系
- 每个数据库应该包含完整的数据结构

### 2. 用户认证
- 用户认证信息存储在各自的数据库中
- 不同域名的用户账户是独立的

### 3. 信号处理器
- 用户信号处理器已适配数据库路由功能
- 会在正确的数据库中创建UserProfile

### 4. 迁移
- 需要对每个数据库分别执行迁移：
```bash
python manage.py migrate
python manage.py migrate --database=firm2
```

## 域名配置示例

### 开发环境
- `localhost:4200` → default数据库
- `firm2.localhost:4200` → firm2数据库

### 生产环境  
- `main.law-firm.com` → default数据库
- `firm2.law-firm.com` → firm2数据库

## 故障排除

1. **中间件不工作**：检查 `X-Database-Used` 响应头
2. **路由器不工作**：使用调试API检查状态
3. **数据库连接问题**：检查数据库配置和权限
4. **测试功能**：使用管理命令验证功能 