.pricing-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.pricing-title {
  margin-bottom: 24px;
  color: #333;
  text-align: center;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

.pricing-card {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
}

.pricing-icon {
  font-size: 32px;
  height: 32px;
  width: 32px;
  margin-right: 16px;
  color: #3f51b5;
}

mat-card-content {
  flex-grow: 1;
}

mat-card-header {
  margin-bottom: 16px;
}

.tab-icon {
  margin-right: 8px;
}

.tab-label {
  margin-right: 8px;
}

.tab-content {
  padding: 24px 16px;
  
  h2 {
    margin-bottom: 24px;
    color: #333;
    text-align: center;
  }
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.case-type-radio {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.result-section {
  margin-top: 24px;
  
  h3 {
    margin: 16px 0;
    color: #333;
  }
}

.fee-range {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 24px 0;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
}

.total-fee {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #ccc;
  
  .fee-label, .fee-value {
    font-size: 1.1em;
    font-weight: 700;
  }
  
  .fee-value {
    color: #f44336;
  }
}

.fee-label {
  font-weight: 500;
  color: #555;
}

.fee-value {
  font-weight: 700;
  color: #3f51b5;
  font-size: 1.1em;
}

.risk-notice {
  margin: 24px 0;
  padding: 16px;
  border: 1px solid #ffccbc;
  background-color: #fff8f6;
  border-radius: 4px;
  
  p {
    margin-top: 0;
    color: #d84315;
    font-weight: 500;
  }
  
  ul {
    margin-bottom: 0;
    padding-left: 24px;
    
    li {
      margin-bottom: 4px;
      color: #555;
    }
  }
} 