"""
中间件 - 根据请求域名设置上下文和数据库
"""
import threading

# 线程本地存储，用于存储当前事务所别名
_thread_locals = threading.local()

def set_current_firm(firm_alias: str):
    """设置当前请求的律师事务所别名"""
    _thread_locals.firm_alias = firm_alias
    
def get_current_firm():
    """获取当前请求的律师事务所别名"""
    return getattr(_thread_locals, 'firm_alias', 'default')
class DomainDatabaseMiddleware:
    """
    根据请求域名设置数据库中间件
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 获取请求的主机名，优先使用X-Forwarded-Host
        host = request.META.get('HTTP_X_FORWARDED_HOST') or request.get_host()
        host = host.lower()
        
        # 根据域名决定使用哪个数据库
        if 'firm2' in host:
            firm_alias = 'firm2'
        else:
            firm_alias = 'default'
        
        set_current_firm(firm_alias)

        # 为了调试，可以在请求头中添加数据库信息
        request.firm_alias = firm_alias
        
        response = self.get_response(request)
        
        # 在响应头中添加数据库信息（用于调试）
        response['X-Database-Used'] = firm_alias
        response['X-Request-Host'] = host  # 调试用
        
        return response 