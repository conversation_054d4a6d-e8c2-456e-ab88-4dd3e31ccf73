# Generated by Django 5.0.3 on 2025-06-22 17:30

from django.db import migrations


def populate_letter_numbers(apps, schema_editor):
    """为现有记录生成letter_number"""
    # 使用原始SQL来避免模型字段依赖问题
    with schema_editor.connection.cursor() as cursor:
        # 为每个年份和函件类型的组合分配letter_number
        cursor.execute("""
            WITH numbered_records AS (
                SELECT id, 
                       ROW_NUMBER() OVER (PARTITION BY year, letter_type ORDER BY created_at) as rn
                FROM cases_letterrecord
                WHERE letter_number IS NULL
            )
            UPDATE cases_letterrecord 
            SET letter_number = numbered_records.rn
            FROM numbered_records
            WHERE cases_letterrecord.id = numbered_records.id;
        """)


def reverse_populate_letter_numbers(apps, schema_editor):
    """逆转操作：清空letter_number"""
    with schema_editor.connection.cursor() as cursor:
        cursor.execute("UPDATE cases_letterrecord SET letter_number = NULL;")


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0037_add_letter_number_field'),
    ]

    operations = [
        migrations.RunPython(
            populate_letter_numbers,
            reverse_populate_letter_numbers,
        ),
    ]
