<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>代理费退费申请单打印</title>
    <style>
        body {
            font-family: Sim<PERSON><PERSON>, <PERSON>l, sans-serif;
            margin: 20px;
            font-size: 14px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .form-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .form-table td {
            border: 1px solid #000;
            padding: 8px;
            vertical-align: middle;
        }
        .form-table .label-cell {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
            width: 150px;
        }
        .form-table .value-cell {
            text-align: left;
            padding-left: 10px;
        }
        .form-table .approval-cell {
            width: 80px;
            text-align: center;
            font-weight: bold;
        }
        .form-table .signature-cell {
            height: 120px;
            vertical-align: top;
        }
        .form-table .text-wrap {
            word-wrap: break-word;
            word-break: break-all;
            white-space: pre-wrap;
        }
        .signature-area {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature-item {
            text-align: center;
            width: 200px;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            height: 40px;
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 12px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 15px;
            }
            button {
                display: none;
            }
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">代理费退费申请单</div>
    </div>
    
    <table class="form-table">
        <tr>
            <td class="label-cell" colspan="2">申请人</td>
            <td class="value-cell" id="requester-name"></td>
            <td class="label-cell">申请日期</td>
            <td class="value-cell" id="application-date"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">案件信息</td>
            <td class="value-cell" id="case-info"></td>
            <td class="approval-cell" rowspan="6">领导审批</td>
            <td class="signature-cell" rowspan="6"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">申请退费金额</td>
            <td class="value-cell" id="withdrawal-amount"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">应扣税费</td>
            <td class="value-cell" id="deduction-tax"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">其他应扣项目</td>
            <td class="value-cell text-wrap" id="other-deductions"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">备注说明</td>
            <td class="value-cell text-wrap" id="remarks"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">审批状态</td>
            <td class="value-cell" id="approval-status"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">实退合计</td>
            <td class="value-cell" id="final-amount"></td>
            <td class="approval-cell">签名</td>
            <td class="signature-cell"></td>
        </tr>
        <tr>
            <td class="label-cell" colspan="2">金额大写</td>
            <td class="value-cell" id="amount-chinese" colspan="3"></td>
        </tr>
    </table>
    
    <div class="signature-area">
        <div class="signature-item">
            <div class="signature-line"></div>
            <div>申请人签名</div>
        </div>
        <div class="signature-item">
            <div class="signature-line"></div>
            <div>审批人签名</div>
        </div>
        <div class="signature-item">
            <div class="signature-line"></div>
            <div>财务签名</div>
        </div>
    </div>
    
    <div class="footer">
        <p>打印日期: <span id="print-date"></span></p>
    </div>
    
    <div class="no-print" style="margin-top: 20px; text-align: center;">
        <button onclick="window.print()" style="padding: 8px 16px; margin-right: 10px; background-color: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">打印</button>
        <button onclick="window.close()" style="padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
    </div>
    
    <script>
        // 从URL参数获取数据
        const urlParams = new URLSearchParams(window.location.search);
        const withdrawalData = JSON.parse(decodeURIComponent(urlParams.get('data')));
        
        // 用户显示名称辅助函数
        function getUserDisplayName(user) {
            if (!user) return '未知用户';
            if (user.last_name || user.first_name) {
                return `${user.last_name || ''}${user.first_name || ''}`.trim();
            }
            return user.username || `用户ID: ${user.id}`;
        }
        
        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return `${date.getFullYear()}年${(date.getMonth() + 1).toString().padStart(2, '0')}月${date.getDate().toString().padStart(2, '0')}日`;
        }
        
        // 数字转中文大写金额
        function numberToChinese(num) {
            const chineseNum = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            const chineseUnit = ['', '拾', '佰', '仟', '万', '拾', '佰', '仟', '亿'];
            const chineseMoney = ['角', '分'];
            
            let result = '';
            const numStr = num.toFixed(2);
            const [integerPart, decimalPart] = numStr.split('.');
            
            // 处理整数部分
            if (parseInt(integerPart) === 0) {
                result = '零圆';
            } else {
                const integerArr = integerPart.split('').reverse();
                for (let i = 0; i < integerArr.length; i++) {
                    const n = parseInt(integerArr[i]);
                    if (n !== 0) {
                        result = chineseNum[n] + chineseUnit[i] + result;
                    } else if (result && !result.startsWith('零')) {
                        result = '零' + result;
                    }
                }
                result += '圆';
            }
            
            // 处理小数部分
            if (decimalPart) {
                const jiao = parseInt(decimalPart[0]);
                const fen = parseInt(decimalPart[1]);
                
                if (jiao !== 0) {
                    result += chineseNum[jiao] + '角';
                }
                if (fen !== 0) {
                    result += chineseNum[fen] + '分';
                } else if (jiao === 0 && fen === 0) {
                    result += '整';
                }
            } else {
                result += '整';
            }
            
            return result;
        }
        
        // 填充数据
        if (withdrawalData) {
            // 申请人
            document.getElementById('requester-name').textContent = getUserDisplayName(withdrawalData.requester);
            
            // 申请日期
            document.getElementById('application-date').textContent = formatDate(withdrawalData.created_at);
            
            // 案件信息
            const caseNumber = withdrawalData.case.case_number || '';
            const caseCause = withdrawalData.case.case_cause || '';
            const caseInfo = caseNumber ? `${caseNumber} - ${caseCause}` : caseCause;
            document.getElementById('case-info').textContent = caseInfo;
            
            // 申请金额
            document.getElementById('withdrawal-amount').textContent = `¥${parseFloat(withdrawalData.amount).toFixed(2)}`;
            
            // 应扣税费
            document.getElementById('deduction-tax').textContent = `¥${parseFloat(withdrawalData.deduction_tax || 0).toFixed(2)}`;
            
            // 其他应扣项目
            document.getElementById('other-deductions').textContent = withdrawalData.other_deductions || '无';
            
            // 备注
            document.getElementById('remarks').textContent = withdrawalData.remarks || '无';
            
            // 审批状态
            let statusText = withdrawalData.status_display;
            if (withdrawalData.approver) {
                statusText += ` (审批人: ${getUserDisplayName(withdrawalData.approver)})`;
            }
            document.getElementById('approval-status').textContent = statusText;
            
            // 实退合计（申请金额 - 应扣税费）
            const finalAmount = parseFloat(withdrawalData.final_amount || withdrawalData.amount);
            document.getElementById('final-amount').textContent = `¥${finalAmount.toFixed(2)}`;
            
            // 金额大写（基于实退合计）
            document.getElementById('amount-chinese').textContent = numberToChinese(finalAmount);
        }
        
        // 设置打印日期
        const now = new Date();
        document.getElementById('print-date').textContent = formatDate(now.toISOString());
        
        // 自动打印功能
        window.onload = function() {
            // 延迟一秒后自动弹出打印对话框
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html> 