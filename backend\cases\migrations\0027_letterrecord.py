# Generated by Django 5.0.3 on 2025-05-24 02:12

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0026_change_user_operator_to_charfield'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LetterRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(help_text='记录年份', verbose_name='年份')),
                ('sequence_number', models.IntegerField(help_text='当年序号', verbose_name='序号')),
                ('date', models.DateField(verbose_name='日期')),
                ('case_number', models.CharField(blank=True, max_length=100, null=True, verbose_name='对应案件号')),
                ('client_name', models.CharField(max_length=200, verbose_name='委托人/当事人')),
                ('quantity', models.IntegerField(default=1, verbose_name='数量')),
                ('recipient_unit', models.CharField(max_length=200, verbose_name='致函单位')),
                ('lawyer_name', models.CharField(max_length=100, verbose_name='使用律师')),
                ('letter_type', models.CharField(choices=[('所函', '所函'), ('会见函', '会见函'), ('调查函', '调查函'), ('律师函', '律师函'), ('法律意见书', '法律意见书')], default='所函', max_length=20, verbose_name='函件类型')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_printed_at', models.DateTimeField(blank=True, null=True, verbose_name='最后打印时间')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_letter_records', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
            ],
            options={
                'verbose_name': '函件登记',
                'verbose_name_plural': '函件登记',
                'ordering': ['-year', '-sequence_number'],
                'unique_together': {('year', 'sequence_number', 'letter_type')},
            },
        ),
    ]
