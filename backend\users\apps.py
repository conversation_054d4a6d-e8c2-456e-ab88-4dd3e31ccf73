from django.apps import AppConfig


class UsersConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'users'
    verbose_name = '用户管理'

    def ready(self):
        """
        当应用程序准备好时，执行初始化任务
        """
        from django.db.models.signals import post_migrate
        from django.dispatch import receiver
        
        # 导入models
        import users.models
        import users.signals
        
        # 在迁移完成后创建权限
        @receiver(post_migrate)
        def create_permissions_after_migrate(sender, **kwargs):
            if sender.name == 'users':
                users.models.create_permissions()

