.export-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .error-message {
    color: red;
    text-align: center;
    padding: 20px;
  }

  mat-card {
    margin-bottom: 20px;

    mat-card-header {
      background-color: #f5f5f5;
      padding: 16px;
      margin-bottom: 20px;

      mat-card-title {
        font-size: 24px;
        color: #3f51b5;
        margin-bottom: 8px;
      }

      mat-card-subtitle {
        color: #666;
        font-size: 16px;
      }
    }

    mat-card-content {
      padding: 0 16px 16px;

      .export-controls {
        margin-bottom: 24px;

        // 第一行：选择模板和导出按钮
        .template-export-row {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          margin-bottom: 16px;

          .template-field {
            flex: 1;
            max-width: 400px;

            ::ng-deep {
              .mat-mdc-form-field-wrapper {
                margin: 0;
                padding: 0;
              }

              .mat-mdc-form-field-flex {
                height: 40px;
                align-items: center;
              }

              .mat-mdc-form-field-infix {
                padding: 8px 0;
                height: 40px;
                display: flex;
                align-items: center;
              }

              .mat-mdc-text-field-wrapper {
                height: 40px;
                padding: 0 12px;
              }

              .mdc-floating-label {
                top: 50%;
                transform: translateY(-50%);
              }

              .mdc-floating-label--float-above {
                top: -22px;
                transform: scale(0.75);
              }

              .mat-mdc-select-trigger {
                height: 40px;
                display: flex;
                align-items: center;
              }

              .mat-mdc-form-field-subscript-wrapper {
                display: none;
              }
            }
          }

          .export-button {
            height: 40px;
            padding: 0 24px;
            display: flex;
            align-items: center;
            margin-top: 0;

            mat-icon {
              margin-right: 8px;
            }
          }
        }

        // 第二行：选择函件功能
        .letter-selection-row {
          .selected-letters {
            margin-bottom: 16px;
            
            h4 {
              margin: 0 0 8px 0;
              font-size: 14px;
              color: #666;
              font-weight: 500;
            }
            
            .letter-chips {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
              
              .selected-letter-chip {
                display: flex;
                align-items: center;
                background-color: #e3f2fd;
                color: #1976d2;
                border: 1px solid #bbdefb;
                padding: 8px 12px;
                border-radius: 16px;
                font-size: 12px;
                line-height: 1.2;
                
                .remove-icon {
                  margin-left: 6px;
                  font-size: 16px;
                  cursor: pointer;
                  opacity: 0.7;
                  transition: opacity 0.2s;
                  width: 16px;
                  height: 16px;
                  
                  &:hover {
                    opacity: 1;
                    color: #d32f2f;
                  }
                }
              }
            }
          }

          .letter-search-field {
            width: 100%;
            margin: 0;

            ::ng-deep {
              .mat-mdc-form-field-wrapper {
                margin: 0;
                padding: 0;
              }

              .mat-mdc-form-field-flex {
                height: 40px;
                align-items: center;
              }

              .mat-mdc-form-field-infix {
                padding: 8px 0;
                height: 40px;
                display: flex;
                align-items: center;
              }

              .mat-mdc-text-field-wrapper {
                height: 40px;
                padding: 0 12px;
              }

              .mdc-floating-label {
                top: 50%;
                transform: translateY(-50%);
              }

              .mdc-floating-label--float-above {
                top: -22px;
                transform: scale(0.75);
              }

              .mat-mdc-select-trigger {
                height: 40px;
                display: flex;
                align-items: center;
              }

              .mat-mdc-form-field-subscript-wrapper {
                display: block;
              }
            }
          }
        }
      }

      .replacement-table-section {
        h3 {
          color: #3f51b5;
          font-size: 18px;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 2px solid #eee;
        }

        .replacement-table {
          width: 100%;
          background-color: white;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          th {
            background-color: #f5f5f5;
            color: #333;
            font-weight: 500;
            padding: 12px 16px;
          }

          td {
            padding: 12px 16px;
            border-bottom: 1px solid #eee;
          }

          .key-cell {
            font-family: monospace;
            color: #3f51b5;
            font-weight: 500;
          }

          .value-cell {
            color: #666;
          }

          tr:hover {
            background-color: #f8f9fa;
          }
        }
      }
    }
  }
}

@media (max-width: 599px) {
  .export-container {
    padding: 10px;

    .export-controls {
      .template-export-row {
        flex-direction: column;
        align-items: stretch !important;

        .template-field {
          max-width: none !important;
          margin-bottom: 16px;
        }

        .export-button {
          width: 100%;
        }
      }

      .letter-selection-row {
        .letter-search-field {
          max-width: none !important;
        }
      }
    }
  }
} 