from django.shortcuts import render
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Sum
from django.views.generic import TemplateView
from django.views.decorators.clickjacking import xframe_options_exempt
from django.utils.decorators import method_decorator
from decimal import Decimal
from .models import OfficeExpense, FinanceRecord
from .serializers import OfficeExpenseSerializer, OfficeExpenseCreateSerializer, FinanceRecordSerializer, FinanceRecordCreateSerializer, CaseSerializer

# 打印模板视图
@method_decorator(xframe_options_exempt, name='dispatch')
class PrintExpenseTemplateView(TemplateView):
    """办公费用打印模板视图，支持跨域访问"""
    template_name = 'finance/print_expense.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

@method_decorator(xframe_options_exempt, name='dispatch')
class PrintWithdrawalTemplateView(TemplateView):
    """提款申请打印模板视图，支持跨域访问"""
    template_name = 'finance/print_withdrawal.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context

# Create your views here.

class OfficeExpenseViewSet(viewsets.ModelViewSet):
    """办公费用视图集"""
    permission_classes = [permissions.IsAuthenticated]
    queryset = OfficeExpense.objects.all()
    serializer_class = OfficeExpenseSerializer
    
    def get_serializer_class(self):
        if self.action == 'create':
            return OfficeExpenseCreateSerializer
        return OfficeExpenseSerializer
    
    def get_queryset(self):
        """根据查询参数过滤结果"""
        queryset = OfficeExpense.objects.all()
        
        # 按申请人过滤
        applicant_id = self.request.query_params.get('applicant_id')
        if applicant_id:
            queryset = queryset.filter(applicant_id=applicant_id)
        
        # 按费用日期范围过滤
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(expense_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(expense_date__lte=end_date)
        
        # 按审批状态过滤
        approval_status = self.request.query_params.get('approval_status')
        if approval_status:
            if approval_status.lower() == 'approved':
                queryset = queryset.filter(approver__isnull=False)
            elif approval_status.lower() == 'pending':
                queryset = queryset.filter(approver__isnull=True)
        
        # 按关键词搜索
        keyword = self.request.query_params.get('keyword')
        if keyword:
            queryset = queryset.filter(
                Q(purpose__icontains=keyword) | 
                Q(description__icontains=keyword)
            )
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审批费用"""
        expense = self.get_object()
        
        # 检查当前用户是否有审批权限（使用UserProfile的has_case_director_approval_permission方法）
        if not hasattr(request.user, 'profile') or not request.user.profile.has_case_director_approval_permission():
            return Response(
                {"detail": "您没有主任审批权限"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 检查是否已经审批过
        if expense.approver:
            return Response(
                {"detail": "此费用已经被审批"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 执行审批（使用模型的approve方法，会自动创建财务记录）
        if expense.approve(request.user):
            serializer = self.get_serializer(expense)
            return Response(serializer.data)
        else:
            return Response(
                {"detail": "审批失败"},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def print(self, request, pk=None):
        """打印费用记录"""
        expense = self.get_object()
        
        # 从当前记录开始往前数9条已审批的记录
        print_records = []
        
        # 获取从当前记录开始往前的所有已审批记录
        # 使用创建时间小于等于当前记录的条件来获取"往前"的记录
        approved_expenses = OfficeExpense.objects.filter(
            approver__isnull=False,  # 只获取已审批的记录
            created_at__lte=expense.created_at  # 创建时间小于等于当前记录
        ).order_by('-created_at')[:9]  # 按创建时间倒序，取前9条
        
        # 更新这些记录的打印时间并添加到打印列表
        for approved_expense in approved_expenses:
            approved_expense.print_record()
            print_records.append(approved_expense)
        
        # 如果不足9条，说明从当前记录往前没有足够的已审批记录
        # 保持原有排序（已经按创建时间倒序）
        
        serializer = self.get_serializer(print_records, many=True)
        return Response(serializer.data)


class FinanceRecordViewSet(viewsets.ModelViewSet):
    """财务收支记录视图集"""
    permission_classes = [permissions.IsAuthenticated]
    queryset = FinanceRecord.objects.select_related('lawyer', 'case', 'created_by').all()
    serializer_class = FinanceRecordSerializer
    
    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return FinanceRecordCreateSerializer
        return FinanceRecordSerializer
    
    def get_queryset(self):
        """根据用户权限和查询参数过滤结果"""
        queryset = FinanceRecord.objects.select_related('lawyer', 'case', 'created_by').all()
        
        # 权限控制：如果当前用户没有财务人员权限，只能访问自己相关的记录
        user = self.request.user
        if not (hasattr(user, 'profile') and user.profile.has_finance_permission()):
            # 没有财务权限，只能看到自己作为律师的记录
            queryset = queryset.filter(lawyer=user)
        
        # 按律师过滤
        lawyer_id = self.request.query_params.get('lawyer_id')
        if lawyer_id:
            queryset = queryset.filter(lawyer_id=lawyer_id)
        
        # 按案件过滤
        case_id = self.request.query_params.get('case_id')
        if case_id:
            queryset = queryset.filter(case_id=case_id)
        
        # 按账目类型过滤
        account_type = self.request.query_params.get('account_type')
        if account_type:
            queryset = queryset.filter(account_type=account_type)
        
        # 按交易日期范围过滤
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        if start_date:
            queryset = queryset.filter(transaction_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(transaction_date__lte=end_date)
        
        # 按关键词搜索
        keyword = self.request.query_params.get('keyword')
        if keyword:
            queryset = queryset.filter(
                Q(purpose__icontains=keyword) | 
                Q(remarks__icontains=keyword) |
                Q(lawyer__username__icontains=keyword) |
                Q(lawyer__first_name__icontains=keyword) |
                Q(lawyer__last_name__icontains=keyword) |
                Q(case__case_number__icontains=keyword) |
                Q(case__case_cause__icontains=keyword)
            )
        
        return queryset
    
    @action(detail=False, methods=['get'])
    def my_balance(self, request):
        """获取当前用户的财务余额统计"""
        user = request.user
        
        # 计算当前用户作为律师的所有财务记录的总和
        records = FinanceRecord.objects.filter(lawyer=user)
        
        # 分别计算收入和支出
        income_sum = records.filter(account_type__in=FinanceRecord.INCOME_TYPES).aggregate(
            total=Sum('amount')
        )['total'] or 0
        
        expense_sum = records.filter(account_type__in=FinanceRecord.EXPENSE_TYPES).aggregate(
            total=Sum('amount')
        )['total'] or 0
        
        # 计算净余额
        balance = income_sum + expense_sum  # expense_sum已经是负数
        
        # 计算欠款（仅考虑缴款和应缴费用，与财务报告逻辑一致）
        payments = records.filter(account_type='lawyer_payment').aggregate(
            total=Sum('amount')
        )['total'] or Decimal('0')
        
        task_expenses = abs(records.filter(account_type='lawyer_task_expense').aggregate(
            total=Sum('amount')
        )['total'] or Decimal('0'))
        
        debt_balance = payments - task_expenses  # 缴款 - 应缴费用
        
        # 获取记录统计
        income_count = records.filter(account_type__in=FinanceRecord.INCOME_TYPES).count()
        expense_count = records.filter(account_type__in=FinanceRecord.EXPENSE_TYPES).count()
        
        return Response({
            'lawyer_id': user.id,
            'lawyer_name': f"{user.first_name} {user.last_name}".strip() or user.username,
            'balance': float(balance),
            'total_income': float(income_sum),
            'total_expense': float(abs(expense_sum)),  # 返回支出的绝对值
            'income_count': income_count,
            'expense_count': expense_count,
            'has_debt': debt_balance < 0,  # 基于缴款与应缴费用的差额
            'debt_amount': float(abs(debt_balance)) if debt_balance < 0 else 0
        })
    
    @action(detail=False, methods=['get'])
    def all_balances(self, request):
        """获取所有律师的财务余额统计（仅财务人员可访问）"""
        # 检查权限：只有有财务权限的用户才能查看所有律师的余额
        if not (hasattr(request.user, 'profile') and request.user.profile.has_finance_permission()):
            return Response(
                {"detail": "您没有财务管理权限"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        from django.contrib.auth.models import User
        
        # 获取所有有财务记录的律师
        lawyers_with_records = FinanceRecord.objects.values_list('lawyer', flat=True).distinct()
        lawyers = User.objects.filter(id__in=lawyers_with_records)
        
        balances = []
        for lawyer in lawyers:
            records = FinanceRecord.objects.filter(lawyer=lawyer)
            
            income_sum = records.filter(account_type__in=FinanceRecord.INCOME_TYPES).aggregate(
                total=Sum('amount')
            )['total'] or 0
            
            expense_sum = records.filter(account_type__in=FinanceRecord.EXPENSE_TYPES).aggregate(
                total=Sum('amount')
            )['total'] or 0
            
            balance = income_sum + expense_sum
            
            balances.append({
                'lawyer_id': lawyer.id,
                'lawyer_name': f"{lawyer.first_name} {lawyer.last_name}".strip() or lawyer.username,
                'balance': float(balance),
                'total_income': float(income_sum),
                'total_expense': float(abs(expense_sum)),
                'has_debt': balance < 0,
                'debt_amount': float(abs(balance)) if balance < 0 else 0
            })
        
        # 按余额排序，欠款的排在前面
        balances.sort(key=lambda x: x['balance'])
        
        return Response(balances)
    
    @action(detail=False, methods=['get'])
    def search_cases(self, request):
        """搜索案件，用于财务记录关联"""
        # 检查权限：只有有财务权限的用户才能使用此功能
        if not (hasattr(request.user, 'profile') and request.user.profile.has_finance_permission()):
            return Response(
                {"detail": "您没有财务管理权限"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        from cases.models import Case
        
        # 获取搜索关键词
        keyword = request.query_params.get('keyword', '').strip()
        
        if not keyword:
            return Response([])
        
        # 搜索案件
        cases = Case.objects.filter(
            Q(case_number__icontains=keyword) |
            Q(case_cause__icontains=keyword)
        ).select_related('lawyer')[:20]  # 限制返回20条结果
        
        serializer = CaseSerializer(cases, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def annual_report(self, request):
        """获取年度财务报告（仅行政人员可访问）"""
        # 检查权限：只有行政人员才能查看财务报告
        if not (hasattr(request.user, 'profile') and request.user.profile.has_case_admin_approval_permission()):
            return Response(
                {"detail": "您没有权限访问财务报告"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        from django.contrib.auth.models import User
        from cases.models import Case
        from decimal import Decimal
        
        # 获取年份参数
        year = request.query_params.get('year')
        if not year:
            return Response(
                {"detail": "请提供年份参数"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            year = int(year)
        except ValueError:
            return Response(
                {"detail": "年份格式错误"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 过滤该年度的记录
        year_records = FinanceRecord.objects.filter(
            transaction_date__year=year
        ).select_related('lawyer', 'case')
        
        # 计算总结区域数据
        # 1. 事务所当年总案件收入（案件收入类型的总和）
        total_case_income = year_records.filter(
            account_type='case_income'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
        
        # 2. 归属于事务所的总收入（总案件收入的12%）
        firm_income = total_case_income * Decimal('0.12')
        
        # 3. 办公费用支出总和
        total_office_expense = abs(year_records.filter(
            account_type='office_expense'
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0'))
        
        # 获取所有律师
        all_lawyers = User.objects.filter(
            finance_records__transaction_date__year=year
        ).distinct()
        
        # 构建每个律师的财务数据
        lawyers_data = []
        debt_lawyers_count = 0  # 有欠款的律师数量
        
        for lawyer in all_lawyers:
            lawyer_records = year_records.filter(lawyer=lawyer)
            
            # 案件收入（该律师作为承办律师的案件的商定律师费总和）
            case_income = lawyer_records.filter(
                account_type='case_income'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
            
            # 归属于事务所的收入（案件收入乘以12%）
            firm_share = case_income * Decimal('0.12')
            
            # 提款总和
            withdrawals = abs(lawyer_records.filter(
                account_type='lawyer_withdrawal'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0'))
            
            # 缴款总和（律师缴费）
            payments = lawyer_records.filter(
                account_type='lawyer_payment'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0')
            
            # 应缴费用总和
            task_expenses = abs(lawyer_records.filter(
                account_type='lawyer_task_expense'
            ).aggregate(total=Sum('amount'))['total'] or Decimal('0'))
            
            # 欠款（缴款总和 - 应缴费用总和）
            debt = payments - task_expenses
            
            # 统计有欠款的律师
            if debt < 0:
                debt_lawyers_count += 1
            
            lawyers_data.append({
                'lawyer_id': lawyer.id,
                'lawyer': {
                    'id': lawyer.id,
                    'username': lawyer.username,
                    'first_name': lawyer.first_name,
                    'last_name': lawyer.last_name,
                    'email': lawyer.email
                },
                'case_income': float(case_income),
                'firm_share': float(firm_share),
                'withdrawals': float(withdrawals),
                'payments': float(payments),
                'task_expenses': float(task_expenses),
                'debt': float(debt)
            })
        
        # 按案件收入排序（降序）
        lawyers_data.sort(key=lambda x: x['case_income'], reverse=True)
        
        return Response({
            'year': year,
            'summary': {
                'total_case_income': float(total_case_income),
                'firm_income': float(firm_income),
                'total_office_expense': float(total_office_expense),
                'debt_lawyers_count': debt_lawyers_count,
                'total_lawyers_count': len(lawyers_data)
            },
            'lawyers': lawyers_data
        })
    
    def perform_create(self, serializer):
        """创建时设置创建人"""
        # created_by 将由序列化器的 create 方法自动设置
        serializer.save()
    
    def create(self, request, *args, **kwargs):
        """创建财务记录，只有财务人员权限的用户可以创建"""
        # 检查权限：只有有财务权限的用户才能创建记录
        if not (hasattr(request.user, 'profile') and request.user.profile.has_finance_permission()):
            return Response(
                {"detail": "您没有财务管理权限"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        return super().create(request, *args, **kwargs)
    
    def update(self, request, *args, **kwargs):
        """更新财务记录，只有财务人员权限的用户可以更新"""
        # 检查权限：只有有财务权限的用户才能更新记录
        if not (hasattr(request.user, 'profile') and request.user.profile.has_finance_permission()):
            return Response(
                {"detail": "您没有财务管理权限"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        return super().update(request, *args, **kwargs)
    
    def destroy(self, request, *args, **kwargs):
        """删除财务记录，只有财务人员权限的用户可以删除"""
        # 检查权限：只有有财务权限的用户才能删除记录
        if not (hasattr(request.user, 'profile') and request.user.profile.has_finance_permission()):
            return Response(
                {"detail": "您没有财务管理权限"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        return super().destroy(request, *args, **kwargs)
