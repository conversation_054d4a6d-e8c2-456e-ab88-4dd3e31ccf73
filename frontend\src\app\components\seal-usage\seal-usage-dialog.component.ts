import { Component, OnInit, Inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { NgIf, NgFor } from '@angular/common';
import { CaseService, SealUsageRecord, SealUsageRecordCreateDto } from '../../services/case.service';
import { AuthService } from '../../services/auth.service';
import { Observable } from 'rxjs';
import { getUserDisplayName } from '../../utils/user.utils';

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
}

@Component({
  selector: 'app-seal-usage-dialog',
  standalone: true,
  imports: [
    NgIf, NgFor, ReactiveFormsModule,
    MatDialogModule, MatButtonModule, MatFormFieldModule,
    MatInputModule, MatDatepickerModule, MatNativeDateModule,
    MatSelectModule, MatIconModule, MatSnackBarModule
  ],
  template: `
    <h2 mat-dialog-title>{{ isEditMode ? '编辑公章使用记录' : '新增公章使用记录' }}</h2>
    
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div mat-dialog-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>日期</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="date" required>
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="form.get('date')?.hasError('required')">
              日期不能为空
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>事由</mat-label>
            <input matInput formControlName="matter" placeholder="请输入事由" required>
            <mat-error *ngIf="form.get('matter')?.hasError('required')">
              事由不能为空
            </mat-error>
            <mat-error *ngIf="form.get('matter')?.hasError('maxlength')">
              事由不能超过200个字符
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>文件名称</mat-label>
            <input matInput formControlName="documentName" placeholder="请输入文件名称" required>
            <mat-error *ngIf="form.get('documentName')?.hasError('required')">
              文件名称不能为空
            </mat-error>
            <mat-error *ngIf="form.get('documentName')?.hasError('maxlength')">
              文件名称不能超过200个字符
            </mat-error>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>数量</mat-label>
            <input matInput type="number" formControlName="quantity" placeholder="请输入数量" required>
            <mat-error *ngIf="form.get('quantity')?.hasError('required')">
              数量不能为空
            </mat-error>
            <mat-error *ngIf="form.get('quantity')?.hasError('min')">
              数量必须大于0
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>用章种类</mat-label>
            <mat-select formControlName="sealType" required>
              <mat-option value="公章">公章</mat-option>
              <mat-option value="合同章">合同章</mat-option>
              <mat-option value="财务章">财务章</mat-option>
              <mat-option value="法人章">法人章</mat-option>
              <mat-option value="其他">其他</mat-option>
            </mat-select>
            <mat-error *ngIf="form.get('sealType')?.hasError('required')">
              用章种类不能为空
            </mat-error>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>受送单位</mat-label>
            <input matInput formControlName="recipientUnit" placeholder="请输入受送单位" required>
            <mat-error *ngIf="form.get('recipientUnit')?.hasError('required')">
              受送单位不能为空
            </mat-error>
            <mat-error *ngIf="form.get('recipientUnit')?.hasError('maxlength')">
              受送单位不能超过200个字符
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>使用人</mat-label>
            <input matInput formControlName="userName" placeholder="请输入使用人姓名" required>
            <mat-error *ngIf="form.get('userName')?.hasError('required')">
              使用人不能为空
            </mat-error>
            <mat-error *ngIf="form.get('userName')?.hasError('maxlength')">
              使用人姓名不能超过100个字符
            </mat-error>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>盖章经办人</mat-label>
            <input matInput formControlName="operatorName" placeholder="请输入盖章经办人姓名">
            <mat-error *ngIf="form.get('operatorName')?.hasError('maxlength')">
              盖章经办人姓名不能超过100个字符
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>备注</mat-label>
            <textarea matInput formControlName="remarks" placeholder="请输入备注" rows="3"></textarea>
          </mat-form-field>
        </div>
      </div>
      
      <div mat-dialog-actions align="end">
        <button mat-button type="button" [mat-dialog-close]="false">取消</button>
        <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid">保存</button>
      </div>
    </form>
  `,
  styles: [`
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }
    .full-width {
      width: 100%;
    }
    .half-width {
      width: calc(50% - 8px);
    }
    mat-form-field {
      margin-bottom: 0;
    }
    @media (max-width: 600px) {
      .form-row {
        flex-direction: column;
      }
      .half-width {
        width: 100%;
      }
    }
  `]
})
export class SealUsageDialogComponent implements OnInit {
  form: FormGroup;
  isEditMode: boolean = false;
  users: User[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<SealUsageDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: SealUsageRecord | null,
    private caseService: CaseService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.isEditMode = !!data;
    this.form = this.createForm();
  }

  ngOnInit(): void {
    this.loadUsers();
    if (this.data) {
      this.populateForm();
    } else {
      // 新增时设置默认值
      this.setDefaultValues();
    }
  }

  createForm(): FormGroup {
    return this.fb.group({
      date: ['', Validators.required],
      matter: ['', [Validators.required, Validators.maxLength(200)]],
      documentName: ['', [Validators.required, Validators.maxLength(200)]],
      quantity: [1, [Validators.required, Validators.min(1)]],
      sealType: ['公章', Validators.required],
      recipientUnit: ['', [Validators.required, Validators.maxLength(200)]],
      userName: ['', [Validators.required, Validators.maxLength(100)]],
      operatorName: ['', [Validators.maxLength(100)]],
      remarks: ['']
    });
  }

  loadUsers(): void {
    this.authService.getAllUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (error) => {
        console.error('获取用户列表失败', error);
        this.snackBar.open('获取用户列表失败', '关闭', {
          duration: 3000
        });
      }
    });
  }

  setDefaultValues(): void {
    // 设置默认日期为今天
    this.form.patchValue({
      date: new Date()
    });

    // 设置当前用户为默认使用人
    this.authService.getCurrentUser().subscribe(user => {
      if (user) {
        this.form.patchValue({ userName: getUserDisplayName(user) });
      }
    });
  }

  populateForm(): void {
    if (this.data) {
      this.form.patchValue({
        date: new Date(this.data.date),
        matter: this.data.matter,
        documentName: this.data.document_name,
        quantity: this.data.quantity,
        sealType: this.data.seal_type,
        recipientUnit: this.data.recipient_unit,
        userName: this.data.user_name,
        operatorName: this.data.operator_name,
        remarks: this.data.remarks
      });
    }
  }

  onSubmit(): void {
    if (this.form.invalid) return;

    const formValue = this.form.value;
    
    const recordData: SealUsageRecordCreateDto = {
      date: this.formatDate(formValue.date),
      matter: formValue.matter,
      document_name: formValue.documentName,
      quantity: formValue.quantity,
      seal_type: formValue.sealType,
      recipient_unit: formValue.recipientUnit,
      user_name: formValue.userName,
      operator_name: formValue.operatorName,
      remarks: formValue.remarks
    };

    if (this.isEditMode && this.data?.id) {
      // 更新记录
      this.caseService.updateSealUsageRecord(this.data.id, recordData).subscribe({
        next: (result) => {
          this.snackBar.open('记录更新成功', '关闭', { duration: 3000 });
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error('记录更新失败', error);
          this.snackBar.open('记录更新失败: ' + (error.error?.detail || '未知错误'), '关闭', {
            duration: 3000
          });
        }
      });
    } else {
      // 创建新记录
      this.caseService.createSealUsageRecord(recordData).subscribe({
        next: (result) => {
          this.snackBar.open('记录创建成功', '关闭', { duration: 3000 });
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error('记录创建失败', error);
          this.snackBar.open('记录创建失败: ' + (error.error?.detail || '未知错误'), '关闭', {
            duration: 3000
          });
        }
      });
    }
  }

  private formatDate(date: Date): string {
    if (!date) return '';
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // getUserDisplayName(user: User): string {
  //   if (!user) return '未知用户';
  //   if (user.last_name || user.first_name) {
  //     return `${user.last_name || ''}${user.first_name || ''}`.trim();
  //   }
  //   return user.username || `用户ID: ${user.id}`;
  // }
} 