<div class="profile-container">
  <h1 class="profile-title">个人资料</h1>

  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <ng-container *ngIf="user$ | async as user">
    <mat-card *ngIf="!isLoading && !error">
      <mat-card-header>
        <mat-icon mat-card-avatar>account_circle</mat-icon>
        <mat-card-title>{{ getUserDisplayName(user) }}</mat-card-title>
        <mat-card-subtitle>{{ getUserRole(user) }}</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <div class="info-section">
          <p><strong>用户名:</strong> {{ user.username }}</p>
          <p><strong>电子邮件:</strong> {{ user.email || '未设置' }}</p>
          
          <!-- 权限信息区域 -->
          <div class="permissions-section">
            <h3>审批权限</h3>
            
            <!-- 详细权限表格 -->
            <div class="permission-details">
              <p>
                <strong>行政审批权限:</strong> 
                <span class="permission-status" 
                      [ngClass]="{'has-permission': user.profile?.has_admin_approval_permission}">
                  {{ user.profile?.has_admin_approval_permission ? '✓ 已授权' : '✗ 未授权' }}
                </span>
              </p>
              
              <p>
                <strong>主任审批权限:</strong> 
                <span class="permission-status"
                      [ngClass]="{'has-permission': user.profile?.has_director_approval_permission}">
                  {{ user.profile?.has_director_approval_permission ? '✓ 已授权' : '✗ 未授权' }}
                </span>
              </p>
            </div>
            
            <div class="permissions-note" *ngIf="!hasAnyApprovalPermission(user)">
              <p><mat-icon class="info-icon">info</mat-icon> 您当前没有任何案件审批权限。如需申请权限，请联系管理员。</p>
            </div>
          </div>
        </div>
      </mat-card-content>
      
      <mat-divider></mat-divider>
      
      <mat-card-actions>
        <button mat-button color="primary" [routerLink]="['/cases']">
          <mat-icon>assignment</mat-icon> 查看我的案件
        </button>
        <button mat-button color="accent">
          <mat-icon>edit</mat-icon> 编辑资料
        </button>
      </mat-card-actions>
    </mat-card>
  </ng-container>
</div> 