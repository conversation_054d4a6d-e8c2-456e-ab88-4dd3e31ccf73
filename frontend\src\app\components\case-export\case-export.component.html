<div class="export-container">
  <div *ngIf="isLoading" class="loading-spinner">
    <mat-spinner></mat-spinner>
  </div>

  <div *ngIf="error" class="error-message">
    {{ error }}
  </div>

  <mat-card *ngIf="case && !isLoading && !error">
    <mat-card-header>
      <mat-card-title>导出案件文档</mat-card-title>
      <mat-card-subtitle>{{ case.case_number }} - {{ case.case_cause }}</mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <div class="export-controls">
        <!-- 第一行：选择导出模板和导出按钮 -->
        <div class="template-export-row">
          <mat-form-field appearance="outline" class="template-field">
            <mat-label>选择导出模板</mat-label>
            <mat-select [(ngModel)]="selectedTemplateId">
              <mat-option disabled [value]="null">-- 请选择模板 --</mat-option>
              <mat-option *ngFor="let template of templates" [value]="template.id">
                {{ template.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <button mat-raised-button color="primary" 
                  [disabled]="!selectedTemplateId || isExporting"
                  (click)="exportDocument()"
                  class="export-button">
            <mat-icon>description</mat-icon>
            {{ isExporting ? '导出中...' : '导出' }}
          </button>
        </div>

        <!-- 第二行：选择函件功能 -->
        <div class="letter-selection-row">
          <!-- 显示已选择的函件 -->
          <div *ngIf="selectedLetters.length > 0" class="selected-letters">
            <h4>已选择的函件：</h4>
            <div class="letter-chips">
              <mat-chip *ngFor="let letter of selectedLetters" class="selected-letter-chip">
                {{ getLetterDisplayText(letter) }}
                <mat-icon class="remove-icon" (click)="removeLetter(letter)">close</mat-icon>
              </mat-chip>
            </div>
          </div>
          
          <mat-form-field appearance="outline" class="letter-search-field">
            <mat-label>搜索并选择函件编号</mat-label>
            <input matInput
                   placeholder="搜索函件编号、委托人名字、案件编号等..."
                   [formControl]="letterSearchControl"
                   [matAutocomplete]="auto"
                   [disabled]="isLoadingLetters">
            <mat-autocomplete #auto="matAutocomplete" 
                             (optionSelected)="selectLetter($event.option.value); letterSearchControl.setValue('')">
              <mat-option disabled *ngIf="isLoadingLetters">
                正在加载函件记录...
              </mat-option>
              <mat-option disabled *ngIf="!isLoadingLetters && letterRecords.length === 0">
                近一个月暂无函件记录
              </mat-option>
              <mat-option disabled *ngIf="!isLoadingLetters && letterRecords.length > 0 && filteredLetterRecords.length === 0">
                没有匹配的函件记录
              </mat-option>
              <mat-option *ngFor="let letter of filteredLetterRecords" 
                         [value]="letter"
                         [disabled]="selectedLetterIds.includes(letter.id!)">
                {{ getLetterDisplayText(letter) }}
              </mat-option>
            </mat-autocomplete>
            <mat-hint>可搜索函件编号、委托人名字、案件编号、致函单位、律师姓名等信息，支持多选</mat-hint>
          </mat-form-field>
        </div>
      </div>

      <div class="replacement-table-section">
        <h3>模板替换参数</h3>
        <table mat-table [dataSource]="replacementData" class="replacement-table">
          <!-- 键列 -->
          <ng-container matColumnDef="key">
            <th mat-header-cell *matHeaderCellDef>模板变量</th>
            <td mat-cell *matCellDef="let element" class="key-cell">{{ element.key }}</td>
          </ng-container>

          <!-- 值列 -->
          <ng-container matColumnDef="value">
            <th mat-header-cell *matHeaderCellDef>替换值</th>
            <td mat-cell *matCellDef="let element" class="value-cell">{{ element.value }}</td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>

      <!-- 调试信息 -->
      <!-- <div *ngIf="debugInfo" class="debug-info-section" style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-left: 4px solid #2196F3;">
        <h4>委托人获取调试信息</h4>
        <pre>{{ debugInfo }}</pre>
      </div> -->
    </mat-card-content>
  </mat-card>
</div> 