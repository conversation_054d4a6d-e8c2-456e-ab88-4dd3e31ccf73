import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { PricingService, CriminalCasePricingResult } from '../../../services/pricing.service';

interface CaseStage {
  value: string;
  viewValue: string;
}

@Component({
  selector: 'app-criminal-pricing',
  templateUrl: './criminal-pricing.component.html',
  styleUrls: ['./criminal-pricing.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatListModule
  ]
})
export class CriminalPricingComponent {
  stage: string = 'firstTrial'; // 默认一审阶段
  result: CriminalCasePricingResult | null = null;
  
  stages: CaseStage[] = [
    { value: 'investigation', viewValue: '刑事侦查阶段' },
    { value: 'prosecution', viewValue: '审查起诉阶段' },
    { value: 'firstTrial', viewValue: '一审阶段' },
    { value: 'secondTrial', viewValue: '二审阶段' },
    { value: 'deathPenalty', viewValue: '死刑复核阶段' },
    { value: 'other', viewValue: '其他刑事诉讼活动(减刑、假释等)' },
  ];
  
  constructor(private pricingService: PricingService) {
    this.calculateFee();
  }
  
  calculateFee(): void {
    this.result = this.pricingService.calculateCriminalCaseFee(this.stage);
  }
  
  updateStage(stage: string): void {
    this.stage = stage;
    this.calculateFee();
  }
} 