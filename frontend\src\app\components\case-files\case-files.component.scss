// 变量定义
$primary-color: #1976d2;
$accent-color: #ff4081;
$warning-color: #f44336;
$background-color: #f5f7fa;
$card-bg-color: #ffffff;
$border-radius: 8px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
$hover-bg-color: rgba(25, 118, 210, 0.04);
$active-bg-color: rgba(25, 118, 210, 0.12);
$border-color: #e0e0e0;
$text-primary: #333333;
$text-secondary: #666666;
$text-muted: #9e9e9e;
$transition-duration: 0.2s;

// 主容器样式
.case-files-container {
  background-color: $background-color;
  border-radius: $border-radius;
  padding: 0;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 150px);
  min-height: 600px;
  overflow: hidden;
}

// 顶部导航栏
.case-files-header {
  background-color: $card-bg-color;
  padding: 16px 24px;
  border-bottom: 1px solid $border-color;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  z-index: 10;
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h2 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 500;
      color: $text-primary;
    }
    
    .path-navigation {
      display: flex;
      align-items: center;
      
      .nav-button {
        margin-right: 8px;
      }
      
      .breadcrumb {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        font-size: 0.9rem;
        
        span {
          color: $text-secondary;
          white-space: nowrap;
          
          &.path-separator {
            margin: 0 8px;
            color: $text-muted;
          }
          
          &.active {
            color: $primary-color;
            font-weight: 500;
          }
          
          &:not(.path-separator):hover {
            cursor: pointer;
            text-decoration: underline;
            color: $primary-color;
          }
        }
      }
    }
  }
}

// 工具栏
.tool-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: $card-bg-color;
  border-bottom: 1px solid $border-color;
  
  .search-container {
    flex: 1;
    max-width: 400px;
    
    .search-field {
      width: 100%;
      margin: 0;
      
      ::ng-deep .mat-mdc-form-field-subscript-wrapper {
        display: none;
      }
      
      ::ng-deep .mat-mdc-text-field-wrapper {
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 24px;
        padding: 0 8px;
      }
    }
  }
  
  .upload-container {
    display: flex;
    gap: 12px;
    
    .upload-button {
      border-radius: 24px;
      min-width: 110px;
    }
  }
}

// 文件预览
.file-preview-container {
  padding: 12px 24px;
  
  .file-preview-card {
    background-color: rgba(25, 118, 210, 0.05);
    border: 1px solid rgba(25, 118, 210, 0.1);
    border-radius: $border-radius;
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 600px;
    
    .file-preview-content {
      display: flex;
      align-items: center;
      
      .file-icon {
        color: $primary-color;
        margin-right: 12px;
        font-size: 24px;
        width: 24px;
        height: 24px;
      }
      
      .file-info {
        display: flex;
        flex-direction: column;
        
        .file-name {
          font-weight: 500;
          max-width: 450px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        
        .file-size {
          font-size: 0.8rem;
          color: $text-muted;
        }
      }
    }
  }
}

// 加载指示器
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: rgba(255, 255, 255, 0.8);
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  backdrop-filter: blur(2px);
  
  span {
    margin-left: 12px;
    color: $text-secondary;
    font-weight: 500;
  }
}

// 主内容区
.content-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  transition: all $transition-duration ease;
  
  &.with-selection {
    .files-view-container {
      width: 70%;
    }
    
    .selection-panel {
      width: 30%;
    }
  }
}

// 文件视图容器
.files-view-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: $card-bg-color;
  transition: width $transition-duration ease;
  overflow: hidden;
  position: relative;
  
  .view-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    border-bottom: 1px solid $border-color;
    
    .view-tabs {
      display: flex;
      
      button {
        border-radius: 20px;
        margin-right: 8px;
        opacity: 0.7;
        
        &.active-tab {
          background-color: rgba(25, 118, 210, 0.1);
          color: $primary-color;
          opacity: 1;
        }
      }
    }
  }
  
  // 空文件夹提示
  .empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 64px 24px;
    color: $text-muted;
    
    mat-icon {
      font-size: 64px;
      height: 64px;
      width: 64px;
      margin-bottom: 16px;
      color: rgba(0, 0, 0, 0.1);
    }
    
    p {
      margin: 4px 0;
      font-size: 1rem;
    }
    
    button {
      margin-top: 24px;
      border-radius: 24px;
    }
  }
  
  // 列表视图
  .list-view {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;
    
    .list-header {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid $border-color;
      font-weight: 500;
      color: $text-secondary;
      position: sticky;
      top: 0;
      background-color: $card-bg-color;
      z-index: 2;
      
      .select-all {
        width: 48px;
      }
      
      .file-name-header {
        flex: 1;
        padding-left: 16px;
      }
      
      .file-type-header {
        width: 100px;
        text-align: center;
      }
      
      .file-size-header {
        width: 100px;
        text-align: center;
      }
      
      .file-actions-header {
        width: 120px;
        text-align: center;
      }
    }
    
    .list-item {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      border-radius: $border-radius;
      border-bottom: 1px solid rgba(0, 0, 0, 0.03);
      transition: background-color $transition-duration ease;
      
      &:hover {
        background-color: $hover-bg-color;
      }
      
      &.folder-item {
        cursor: pointer;
      }
      
      .select-cell {
        width: 48px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      
      .name-cell {
        flex: 1;
        display: flex;
        align-items: center;
        padding-right: 16px;
        cursor: pointer;
        
        mat-icon {
          margin-right: 12px;
          
          &.folder-icon {
            color: #FFC107;
          }
        }
        
        .item-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      
      .type-cell {
        width: 100px;
        text-align: center;
        color: $text-secondary;
        font-size: 0.9rem;
      }
      
      .size-cell {
        width: 100px;
        text-align: center;
        color: $text-secondary;
        font-size: 0.9rem;
      }
      
      .actions-cell {
        width: 120px;
        display: flex;
        justify-content: center;
        align-items: center;
        
        button {
          opacity: 0.7;
          transition: opacity $transition-duration ease;
          margin: 0 4px;
          
          &:hover {
            opacity: 1;
          }
        }
      }
    }
  }
  
  // 网格视图
  .grid-view {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    grid-gap: 20px;
    
    .grid-item {
      height: 180px;
      background-color: rgba(0, 0, 0, 0.02);
      border-radius: $border-radius;
      padding: 16px;
      display: flex;
      flex-direction: column;
      position: relative;
      transition: all $transition-duration ease;
      
      &:hover {
        background-color: $hover-bg-color;
        transform: translateY(-2px);
        box-shadow: $box-shadow;
        
        .item-actions {
          opacity: 1;
        }
      }
      
      &.folder-item {
        cursor: pointer;
        background-color: rgba(255, 193, 7, 0.05);
        border: 1px solid rgba(255, 193, 7, 0.1);
        
        &:hover {
          background-color: rgba(255, 193, 7, 0.1);
        }
      }
      
      .item-checkbox {
        position: absolute;
        top: 8px;
        left: 8px;
        z-index: 1;
      }
      
      .item-icon {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 12px;
        
        mat-icon {
          font-size: 48px;
          width: 48px;
          height: 48px;
          
          &.folder-icon {
            color: #FFC107;
          }
        }
      }
      
      .item-details {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .item-name {
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 4px;
        }
        
        .item-meta, .item-type {
          color: $text-secondary;
          font-size: 0.8rem;
        }
      }
      
      .item-actions {
        position: absolute;
        bottom: 8px;
        right: 8px;
        opacity: 0;
        transition: opacity $transition-duration ease;
        display: flex;
        
        button {
          width: 32px;
          height: 32px;
          line-height: 32px;
          
          mat-icon {
            font-size: 16px;
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
}

// 选择面板
.selection-panel {
  width: 0;
  background-color: $card-bg-color;
  border-left: 1px solid $border-color;
  display: flex;
  flex-direction: column;
  transition: width $transition-duration ease;
  overflow: hidden;
  
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    border-bottom: 1px solid $border-color;
    
    h3 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 500;
      color: $text-primary;
    }
  }
  
  .panel-section {
    padding: 16px 24px;
    border-bottom: 1px solid $border-color;
    
    h4 {
      margin: 0 0 12px 0;
      font-size: 0.9rem;
      font-weight: 500;
      color: $text-secondary;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .selected-files-list {
      max-height: 300px;
      overflow-y: auto;
      
      .selected-file-item {
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.02);
        border-radius: 4px;
        padding: 8px 12px;
        margin-bottom: 8px;
        
        .drag-handle {
          cursor: move;
          color: $text-muted;
          margin-right: 8px;
          font-size: 18px;
        }
        
        .file-type-icon {
          margin-right: 8px;
        }
        
        .file-name {
          flex: 1;
          font-size: 0.9rem;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
    
    .merge-actions {
      form {
        display: flex;
        flex-direction: column;
        
        .output-name-field {
          width: 100%;
          margin-bottom: 16px;
        }
        
        .action-buttons {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          justify-content: flex-end;
          
          button {
            min-width: 120px;
          }
        }
      }
    }
  }
}

// 文件类型颜色
.pdf {
  color: #F44336;
}

.doc, .docx {
  color: #2196F3;
}

.xls, .xlsx {
  color: #4CAF50;
}

.jpg, .jpeg, .png, .gif {
  color: #FF9800;
}

// 拖拽样式
.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  border-radius: 4px !important;
  opacity: 0.9;
}

.cdk-drag-placeholder {
  opacity: 0.4;
  background-color: rgba(0, 0, 0, 0.04);
  border-radius: 4px;
  border: 1px dashed rgba(0, 0, 0, 0.2);
}

.cdk-drag-animating {
  transition: transform 0.25s cubic-bezier(0, 0, 0.2, 1);
}

// 自定义滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25);
}

// 响应式设计
@media (max-width: 992px) {
  .content-container {
    flex-direction: column;
    
    &.with-selection {
      .files-view-container {
        width: 100%;
        height: 60%;
      }
      
      .selection-panel {
        width: 100%;
        height: 40%;
        border-left: none;
        border-top: 1px solid $border-color;
        min-height: 300px;
      }
    }
  }
  
  .case-files-header {
    .header-content {
      flex-direction: column;
      align-items: flex-start;
      
      h2 {
        margin-bottom: 8px;
      }
    }
  }
  
  .tool-bar {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    
    .search-container {
      max-width: 100%;
    }
  }
  
  .selection-panel {
    .panel-section {
      .merge-actions {
        form {
          .action-buttons {
            flex-direction: column;
            align-items: stretch;
            
            button {
              min-width: auto;
              width: 100%;
              margin: 4px 0;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .list-view {
    .list-header, .list-item {
      .file-type-header, .type-cell {
        display: none;
      }
      
      .file-size-header, .size-cell {
        width: 80px;
      }
      
      .file-actions-header, .actions-cell {
        width: 100px;
      }
    }
  }
  
  .grid-view {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    
    .grid-item {
      height: 150px;
      
      .item-icon {
        height: 60px;
        
        mat-icon {
          font-size: 36px;
          width: 36px;
          height: 36px;
        }
      }
    }
  }
  
  .content-container {
    &.with-selection {
      .files-view-container {
        height: 50%;
      }
      
      .selection-panel {
        height: 50%;
        min-height: 350px;
      }
    }
  }
  
  .selection-panel {
    .panel-section {
      padding: 12px 16px;
      
      .selected-files-list {
        max-height: 150px;
      }
      
      .merge-actions {
        form {
          .output-name-field {
            margin-bottom: 12px;
          }
          
          .action-buttons {
            gap: 12px;
            
            button {
              padding: 12px 16px;
              font-size: 0.9rem;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .content-container {
    &.with-selection {
      .files-view-container {
        height: 45%;
      }
      
      .selection-panel {
        height: 55%;
        min-height: 400px;
      }
    }
  }
  
  .selection-panel {
    .panel-header {
      padding: 12px 16px;
      
      h3 {
        font-size: 1rem;
      }
    }
    
    .panel-section {
      padding: 8px 12px;
      
      h4 {
        font-size: 0.8rem;
        margin-bottom: 8px;
      }
      
      .selected-files-list {
        max-height: 120px;
        
        .selected-file-item {
          padding: 6px 8px;
          margin-bottom: 6px;
          
          .file-name {
            font-size: 0.8rem;
          }
        }
      }
      
      .merge-actions {
        form {
          .output-name-field {
            margin-bottom: 8px;
            
            ::ng-deep .mat-mdc-form-field {
              font-size: 0.9rem;
            }
          }
          
          .action-buttons {
            gap: 8px;
            
            button {
              padding: 10px 12px;
              font-size: 0.8rem;
              min-height: 40px;
              
              mat-icon {
                font-size: 18px;
                width: 18px;
                height: 18px;
                margin-right: 4px;
              }
            }
          }
        }
      }
    }
  }
  
  .tool-bar {
    padding: 12px 16px;
    
    .upload-container {
      .upload-button {
        min-width: auto;
        width: 100%;
        margin: 4px 0;
      }
    }
  }
}
