import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-create-legal-entity-dialog',
  template: `
    <h2 mat-dialog-title>新建单位</h2>
    <mat-dialog-content class="dialog-content">
      <form [formGroup]="legalEntityForm" (ngSubmit)="onSubmit()">
        <div class="form-section">
          <h3 class="section-title">单位基本信息</h3>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>单位名称</mat-label>
              <input matInput formControlName="name" required>
              <mat-error *ngIf="legalEntityForm.get('name')?.hasError('required')">
                单位名称不能为空
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>统一社会信用代码</mat-label>
              <input matInput formControlName="credit_code">
            </mat-form-field>
          </div>
        </div>

        <div class="form-section">
          <h3 class="section-title">法定代表人信息</h3>
          
          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>法定代表人</mat-label>
              <input matInput formControlName="representative_name">
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>代表人身份证号</mat-label>
              <input matInput formControlName="representative_id_number">
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>代表人联系电话</mat-label>
              <input matInput formControlName="representative_phone_number">
            </mat-form-field>
          </div>
        </div>
        
        <div class="form-section">
          <h3 class="section-title">其他信息</h3>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>备注</mat-label>
              <textarea matInput formControlName="remarks" rows="3"></textarea>
            </mat-form-field>
          </div>
        </div>
      </form>
    </mat-dialog-content>

    <mat-dialog-actions align="end" class="dialog-actions">
      <button mat-button [mat-dialog-close]="null" class="cancel-button">
        <mat-icon>close</mat-icon>
        取消
      </button>
      <button mat-raised-button 
              color="primary" 
              class="save-button"
              [disabled]="legalEntityForm.invalid"
              (click)="onSubmit()">
        <mat-icon>save</mat-icon>
        创建
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    :host {
      --primary-color: #1976d2;
      --primary-light: #e3f2fd;
      --secondary-color: #ff9800;
      --secondary-light: #fff3e0;
      --text-primary: #212121;
      --text-secondary: #757575;
      --border-color: #e0e0e0;
      --success-color: #4caf50;
    }

    .dialog-content {
      padding: 24px;
      min-width: 650px;
      max-width: 800px;
      background: #fafafa;
      border-radius: 8px;
    }
    
    .form-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
      position: relative;
    }
    
    .form-section:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
    
    .form-section:nth-child(1) {
      border-left: 4px solid var(--primary-color);
    }
    
    .form-section:nth-child(2) {
      border-left: 4px solid var(--secondary-color);
    }
    
    .form-section:nth-child(3) {
      border-left: 4px solid var(--success-color);
    }
    
    .section-title {
      color: var(--text-primary);
      margin: 0 0 20px 0;
      padding: 0 0 12px 0;
      font-size: 18px;
      font-weight: 600;
      border-bottom: 2px solid var(--primary-light);
      position: relative;
      display: flex;
      align-items: center;
      gap: 10px;
    }
    
    .section-title::before {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 80px;
      height: 2px;
      background: var(--primary-color);
      border-radius: 1px;
    }
    
    .section-title::after {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: var(--primary-color);
      flex-shrink: 0;
    }
    
    .form-section:nth-child(2) .section-title::before,
    .form-section:nth-child(2) .section-title::after {
      background: var(--secondary-color);
    }
    
    .form-section:nth-child(3) .section-title::before,
    .form-section:nth-child(3) .section-title::after {
      background: var(--success-color);
    }
    
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      align-items: flex-start;
    }
    
    .form-row:last-child {
      margin-bottom: 0;
    }
    
    .form-row mat-form-field {
      flex: 1;
    }
    
    .full-width {
      width: 100%;
    }
    
    ::ng-deep .mat-mdc-form-field {
      margin-bottom: 4px;
    }
    
    ::ng-deep .mat-mdc-form-field .mat-mdc-form-field-focus-overlay {
      background-color: var(--primary-light);
    }
    
    ::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
      color: var(--primary-color);
    }
    
    ::ng-deep .mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing {
      border-color: var(--border-color);
      border-width: 1px;
    }
    
    ::ng-deep .mat-mdc-form-field .mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing {
      border-color: var(--primary-color);
      border-width: 2px;
    }
    
    .dialog-actions {
      padding: 20px 24px;
      margin: 0 -24px -24px -24px;
      background: white;
      border-top: 1px solid var(--border-color);
      border-radius: 0 0 8px 8px;
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }
    
    .cancel-button {
      min-width: 100px;
      height: 40px;
      border-radius: 20px;
      font-weight: 500;
      text-transform: none;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }
    
    .cancel-button:hover {
      background-color: #f5f5f5;
      border-color: var(--text-secondary);
    }
    
    .save-button {
      min-width: 120px;
      height: 40px;
      border-radius: 20px;
      font-weight: 500;
      text-transform: none;
      background: linear-gradient(45deg, var(--primary-color), #1565c0);
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
      transition: all 0.3s ease;
    }
    
    .save-button:hover:not(:disabled) {
      background: linear-gradient(45deg, #1565c0, var(--primary-color));
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
      transform: translateY(-1px);
    }
    
    .save-button:disabled {
      background: #cccccc;
      box-shadow: none;
      color: #888888;
    }
    
    .cancel-button,
    .save-button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .cancel-button mat-icon,
    .save-button mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
    
    ::ng-deep .mat-mdc-dialog-title {
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      padding: 24px 24px 0 24px;
      border-bottom: none;
    }
    
    ::ng-deep .mat-mdc-dialog-content {
      padding: 0;
      margin: 0;
      max-height: 70vh;
      overflow-y: auto;
    }
    
    ::ng-deep .mat-mdc-dialog-actions {
      padding: 0;
      margin: 0;
    }
    
    /* 滚动条样式 */
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar {
      width: 6px;
    }
    
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    /* 输入框聚焦效果 */
    ::ng-deep .mat-mdc-form-field .mdc-text-field--focused {
      background-color: rgba(25, 118, 210, 0.02);
    }
    
    /* 添加一些微动画 */
    .form-section {
      animation: slideInUp 0.3s ease-out;
    }
    
    .form-section:nth-child(2) {
      animation-delay: 0.1s;
    }
    
    .form-section:nth-child(3) {
      animation-delay: 0.2s;
    }
    
    @keyframes slideInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    /* 响应式设计 */
    @media (max-width: 768px) {
      .dialog-content {
        min-width: auto;
        max-width: 100%;
        padding: 16px;
      }
      
      .form-section {
        padding: 16px;
        margin-bottom: 16px;
      }
      
      .form-row {
        flex-direction: column;
        gap: 12px;
      }
      
      .dialog-actions {
        padding: 16px;
        margin: 0 -16px -16px -16px;
      }
      
      .section-title {
        font-size: 16px;
      }
    }
  `],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ]
})
export class CreateLegalEntityDialogComponent implements OnInit {
  legalEntityForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<CreateLegalEntityDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.legalEntityForm = this.fb.group({
      name: ['', Validators.required],
      representative_name: [''],
      representative_id_number: [''],
      representative_phone_number: [''],
      credit_code: [''],
      remarks: ['']
    });
  }

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.legalEntityForm.invalid) {
      this.snackBar.open('请填写必填信息', '关闭', { duration: 3000 });
      return;
    }

    const formData = this.legalEntityForm.value;

    this.http.post(`${environment.apiUrl}/legal-entities/`, formData)
      .subscribe({
        next: (response: any) => {
          this.snackBar.open('单位创建成功', '关闭', { duration: 3000 });
          this.dialogRef.close(response);
        },
        error: (error) => {
          console.error('创建单位失败:', error);
          this.snackBar.open('创建单位失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 5000 });
        }
      });
  }
} 