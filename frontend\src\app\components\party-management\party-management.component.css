.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

h2 {
  margin-bottom: 20px;
  color: #3f51b5;
}

.tab-content {
  padding: 20px 0;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

mat-form-field {
  flex: 1;
  min-width: 200px;
}

.data-table-container {
  margin-top: 30px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-search-field {
  width: 300px;
  margin-bottom: 0;
}

table {
  width: 100%;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
  margin: 16px 0;
}

.loading-spinner p {
  margin-top: 12px;
  color: #757575;
  font-size: 14px;
}

.no-data {
  text-align: center;
  padding: 30px;
  color: #777;
  font-style: italic;
}

.mat-mdc-row:hover {
  background-color: #f5f5f5;
}

.mat-mdc-header-cell {
  font-weight: bold;
  color: #3f51b5;
}

mat-card {
  margin-bottom: 20px;
}

.has-cases {
  color: #3f51b5;
  font-weight: bold;
}

button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
}

.full-width {
  width: 100%;
}

.form-row.full-width {
  margin-top: 10px;
  margin-bottom: 15px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

textarea {
  min-height: 80px;
  resize: vertical;
}

/* 让表单字段在移动设备上更美观 */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
  
  .form-row mat-form-field {
    width: 100%;
    margin-right: 0;
  }
}

/* 搜索区样式 */
.search-container {
  max-width: 700px;
  margin: 0 auto 32px auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  position: relative;
  z-index: 10;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.search-header h3 {
  margin: 0;
  font-size: 18px;
  color: #424242;
  font-weight: 500;
}

.search-close-btn {
  color: #757575;
}

.search-close-btn:hover {
  color: #f44336;
}

.search-field {
  width: 100%;
  margin-bottom: 8px;
}

.search-toggle {
  position: fixed;
  bottom: 32px;
  right: 32px;
  z-index: 1000;
}

.search-results {
  margin-top: 16px;
}

.results-section {
  margin-bottom: 24px;
}

.results-section.legacy-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 16px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  color: #424242;
  font-weight: 500;
}

.section-hint {
  margin-left: 8px;
  font-size: 12px;
  color: #757575;
  font-style: italic;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.result-card {
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s ease;
  cursor: pointer;
  border-left: 4px solid transparent;
}

.result-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background-color: #e3f2fd;
  border-left-color: #2196f3;
}

.result-card.legacy-card {
  background-color: #f5f5f5;
  cursor: default;
}

.result-card.legacy-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  background-color: #f0f0f0;
  border-left-color: #673ab7;
}

.result-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.result-name {
  font-weight: 500;
  font-size: 16px;
  color: #212121;
}

.result-badge {
  margin-left: 8px;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.result-badge.natural {
  background-color: #2196f3;
  color: white;
}

.result-badge.legal {
  background-color: #ff9800;
  color: white;
}

.result-badge.legacy {
  background-color: #673ab7;
  color: white;
}

.result-info {
  font-size: 13px;
  color: #616161;
  margin-top: 4px;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.no-results mat-icon {
  font-size: 36px;
  width: 36px;
  height: 36px;
  color: #bdbdbd;
  margin-bottom: 12px;
}

.no-results p {
  color: #757575;
  font-size: 14px;
  text-align: center;
  margin: 0;
}

/* 表格行高亮样式 */
.highlighted-row {
  background-color: #e3f2fd !important;
  animation: highlight-pulse 3s;
}

@keyframes highlight-pulse {
  0% { background-color: #e3f2fd !important; }
  50% { background-color: #bbdefb !important; }
  100% { background-color: #e3f2fd !important; }
} 