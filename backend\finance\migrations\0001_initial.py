# Generated by Django 5.0.3 on 2025-05-21 18:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OfficeExpense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('expense_date', models.DateField(verbose_name='费用发生日期')),
                ('purpose', models.CharField(max_length=100, verbose_name='用途')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='金额')),
                ('description', models.TextField(blank=True, null=True, verbose_name='说明')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('last_printed_at', models.DateTimeField(blank=True, null=True, verbose_name='最后打印时间')),
                ('applicant', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to=settings.AUTH_USER_MODEL, verbose_name='申请人')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_expenses', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
            ],
            options={
                'verbose_name': '办公费用',
                'verbose_name_plural': '办公费用',
                'ordering': ['-created_at'],
            },
        ),
    ]
