<h2 mat-dialog-title class="dialog-title">{{ isEdit ? '编辑当事人' : '添加当事人' }}</h2>
<mat-dialog-content class="dialog-content">
  <!-- 当事人表单 -->
  <div class="party-form">
    <!-- 自然人表单 -->
    <form *ngIf="partyType === 'natural'" [formGroup]="naturalPersonForm" (ngSubmit)="savePartyInfo()">
      <div class="form-section">
        <h3 class="section-title">自然人基本信息</h3>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>姓名</mat-label>
            <input matInput formControlName="name" required>
            <mat-error *ngIf="naturalPersonForm.get('name')?.hasError('required')">
              姓名不能为空
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>身份证号码</mat-label>
            <input matInput formControlName="id_number">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>手机号码</mat-label>
            <input matInput formControlName="phone_number">
          </mat-form-field>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">其他信息</h3>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>备注</mat-label>
            <textarea matInput formControlName="remarks" rows="2"></textarea>
          </mat-form-field>
        </div>
      </div>
    </form>

    <!-- 单位表单 -->
    <form *ngIf="partyType === 'legal'" [formGroup]="legalEntityForm" (ngSubmit)="savePartyInfo()">
      <div class="form-section">
        <h3 class="section-title">单位基本信息</h3>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>单位名称</mat-label>
            <input matInput formControlName="name" required>
            <mat-error *ngIf="legalEntityForm.get('name')?.hasError('required')">
              单位名称不能为空
            </mat-error>
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>统一社会信用代码</mat-label>
            <input matInput formControlName="credit_code">
          </mat-form-field>
        </div>
      </div>

      <div class="form-section">
        <h3 class="section-title">法定代表人信息</h3>
        <div class="form-row">
          <mat-form-field appearance="outline">
            <mat-label>法定代表人</mat-label>
            <input matInput formControlName="representative_name">
          </mat-form-field>

          <mat-form-field appearance="outline">
            <mat-label>代表人身份证号</mat-label>
            <input matInput formControlName="representative_id_number">
          </mat-form-field>
        </div>

        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>代表人联系电话</mat-label>
            <input matInput formControlName="representative_phone_number">
          </mat-form-field>
        </div>
      </div>
      
      <div class="form-section">
        <h3 class="section-title">其他信息</h3>
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>备注</mat-label>
            <textarea matInput formControlName="remarks" rows="2"></textarea>
          </mat-form-field>
        </div>
      </div>
    </form>
  </div>
  
  <!-- 类型选择（如果是新建当事人） -->
  <div *ngIf="!isEdit && !partyType" class="party-type-selection">
    <h3>选择当事人类型</h3>
    <div class="type-buttons">
      <button mat-raised-button (click)="selectPartyType('natural')">
        <mat-icon>person</mat-icon> 自然人
      </button>
      <button mat-raised-button (click)="selectPartyType('legal')">
        <mat-icon>business</mat-icon> 企业法人
      </button>
    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end" class="dialog-actions">
  <button mat-button [mat-dialog-close]="null" class="cancel-button">取消</button>
  <button mat-raised-button 
          color="primary" 
          class="save-button"
          [disabled]="(partyType === 'natural' && naturalPersonForm.invalid) || 
                     (partyType === 'legal' && legalEntityForm.invalid)"
          (click)="savePartyInfo()">
    保存
  </button>
</mat-dialog-actions> 