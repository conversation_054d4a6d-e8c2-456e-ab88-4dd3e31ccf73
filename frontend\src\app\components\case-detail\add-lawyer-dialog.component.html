<h2 mat-dialog-title>添加协作律师</h2>
<mat-dialog-content>
  <mat-form-field appearance="fill" style="width: 100%;">
    <mat-label>选择律师</mat-label>
    <mat-select [(ngModel)]="selectedLawyerId">
      <mat-option *ngFor="let lawyer of availableLawyers" [value]="lawyer.id">
        {{ getUserDisplay<PERSON>ame(lawyer) }}
      </mat-option>
    </mat-select>
  </mat-form-field>

  <mat-form-field appearance="fill" style="width: 100%; margin-top: 16px;">
    <mat-label>费用分成比例 (%)</mat-label>
    <input matInput type="number" 
           [(ngModel)]="feeSharePercentage" 
           min="0" max="100" step="0.01"
           placeholder="例如: 20 表示20%">
    <mat-hint>请输入0-100之间的数值，例如20表示20%</mat-hint>
  </mat-form-field>

  <mat-form-field appearance="fill" style="width: 100%; margin-top: 16px;">
    <mat-label>备注(可选)</mat-label>
    <textarea matInput [(ngModel)]="remarks" rows="3" placeholder="添加关于协作的说明"></textarea>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button [mat-dialog-close]="null">取消</button>
  <button mat-raised-button color="primary" [disabled]="!selectedLawyerId"
          [mat-dialog-close]="{
            lawyerId: selectedLawyerId, 
            feeShareRatio: feeSharePercentage / 100,
            remarks: remarks
          }">
    添加
  </button>
</mat-dialog-actions> 