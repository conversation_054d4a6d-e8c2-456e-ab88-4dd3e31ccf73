# Generated by Django 5.0.3 on 2025-05-24 20:55

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0029_caselawyercollaboration_fee_share_ratio'),
        ('finance', '0002_financerecord'),
    ]

    operations = [
        migrations.AddField(
            model_name='financerecord',
            name='case',
            field=models.ForeignKey(blank=True, help_text='可选择关联的案件', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='finance_records', to='cases.case', verbose_name='关联案件'),
        ),
    ]
