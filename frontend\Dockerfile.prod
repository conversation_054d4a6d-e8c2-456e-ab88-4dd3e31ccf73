# 后端静态文件构建阶段
FROM python:3.11-slim AS backend_staticfile
WORKDIR /app
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources
RUN apt-get update && apt-get install -y gcc postgresql-client && rm -rf /var/lib/apt/lists/*
COPY ./backend/requirements.prod.txt ./backend/requirements.txt .
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install --no-cache-dir -r requirements.prod.txt
COPY ./backend/ ./
RUN mkdir -p /var/log/django
RUN python manage.py collectstatic --noinput

# 前端构建阶段
# FROM node:20-alpine AS builder
FROM node:20-alpine AS builder

WORKDIR /app
COPY ./frontend/package*.json ./
RUN npm config set registry https://mirrors.cloud.tencent.com/npm/
RUN npm install

COPY ./frontend/. .
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制nginx配置
COPY ./frontend/nginx.conf /etc/nginx/conf.d/default.conf

# 复制构建产物
COPY --from=builder /app/dist/law-firm-frontend/browser /usr/share/nginx/html

# 复制后端静态文件
COPY --from=backend_staticfile /app/staticfiles /var/www/staticfiles

EXPOSE 80