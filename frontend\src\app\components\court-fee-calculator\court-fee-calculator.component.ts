import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CourtFeeService, CourtFeeResult } from '../../services/court-fee.service';

interface CaseType {
  value: string;
  viewValue: string;
}

interface CaseTypeGroup {
  name: string;
  caseTypes: CaseType[];
}

@Component({
  selector: 'app-court-fee-calculator',
  templateUrl: './court-fee-calculator.component.html',
  styleUrls: ['./court-fee-calculator.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatRadioModule,
    MatDividerModule,
    MatListModule,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class CourtFeeCalculatorComponent {
  // 案件类型选项分组
  caseTypeGroups: CaseTypeGroup[] = [
    {
      name: '民事案件',
      caseTypes: [
        { value: 'propertyCivil', viewValue: '民事财产案件' },
        { value: 'divorce', viewValue: '离婚案件' },
        { value: 'personalRights', viewValue: '人格权案件' },
        { value: 'nonProperty', viewValue: '其他非财产案件' },
        { value: 'intellectualProperty', viewValue: '知识产权案件' },
        { value: 'laborDispute', viewValue: '劳动争议、人事争议案件' },
      ]
    },
    {
      name: '申请费',
      caseTypes: [
        { value: 'paymentOrder', viewValue: '申请支付令' },
        { value: 'preservation', viewValue: '申请保全措施' },
        { value: 'publicNotification', viewValue: '申请公示催告' },
        { value: 'arbitrationRevocation', viewValue: '申请撤销仲裁裁决或认定仲裁协议效力' },
        { value: 'bankruptcy', viewValue: '申请破产' },
      ]
    },
    {
      name: '行政案件',
      caseTypes: [
        { value: 'administrative', viewValue: '行政案件' },
      ]
    }
  ];
  
  // 当前选择的案件类型
  selectedCaseType: string = 'propertyCivil';
  
  // 民事财产案件
  propertyAmount: number = 100000;
  
  // 离婚案件
  hasDivorceProperty: boolean = false;
  divorcePropertyAmount: number = 0;
  
  // 人格权案件
  hasPersonalRightsDamages: boolean = false;
  personalRightsDamagesAmount: number = 0;
  
  // 知识产权案件
  hasIntellectualPropertyDispute: boolean = false;
  intellectualPropertyDisputeAmount: number = 0;
  
  // 申请支付令
  paymentOrderAmount: number = 100000;
  
  // 申请保全措施
  preservationAmount: number = 100000;
  
  // 申请破产
  bankruptcyAmount: number = 1000000;
  
  // 计算结果
  result: CourtFeeResult | null = null;
  
  constructor(private courtFeeService: CourtFeeService) {
    this.calculateFee();
  }
  
  /**
   * 计算诉讼费
   */
  calculateFee(): void {
    switch (this.selectedCaseType) {
      case 'propertyCivil':
        this.result = this.courtFeeService.calculatePropertyCaseFee(this.propertyAmount);
        break;
      case 'divorce':
        this.result = this.courtFeeService.calculateDivorceFee(
          this.hasDivorceProperty, 
          this.divorcePropertyAmount
        );
        break;
      case 'personalRights':
        this.result = this.courtFeeService.calculatePersonalRightsFee(
          this.hasPersonalRightsDamages, 
          this.personalRightsDamagesAmount
        );
        break;
      case 'nonProperty':
        this.result = this.courtFeeService.calculateNonPropertyFee();
        break;
      case 'intellectualProperty':
        this.result = this.courtFeeService.calculateIntellectualPropertyFee(
          this.hasIntellectualPropertyDispute, 
          this.intellectualPropertyDisputeAmount
        );
        break;
      case 'laborDispute':
        this.result = this.courtFeeService.calculateLaborDisputeFee();
        break;
      case 'paymentOrder':
        this.result = this.courtFeeService.calculatePaymentOrderFee(this.paymentOrderAmount);
        break;
      case 'preservation':
        this.result = this.courtFeeService.calculatePreservationFee(this.preservationAmount);
        break;
      case 'publicNotification':
        this.result = this.courtFeeService.calculatePublicNotificationFee();
        break;
      case 'arbitrationRevocation':
        this.result = this.courtFeeService.calculateArbitrationRevocationFee();
        break;
      case 'bankruptcy':
        this.result = this.courtFeeService.calculateBankruptcyFee(this.bankruptcyAmount);
        break;
      case 'administrative':
        this.result = this.courtFeeService.calculateAdministrativeFee();
        break;
      default:
        this.result = null;
    }
  }
  
  /**
   * 更新案件类型
   */
  updateCaseType(caseType: string): void {
    this.selectedCaseType = caseType;
    this.calculateFee();
  }
  
  /**
   * 更新财产金额
   */
  updatePropertyAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.propertyAmount = value > 0 ? value : 1;
    this.calculateFee();
  }
  
  /**
   * 更新离婚案件财产相关参数
   */
  updateDivorceHasProperty(hasProperty: boolean): void {
    this.hasDivorceProperty = hasProperty;
    this.calculateFee();
  }
  
  updateDivorcePropertyAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.divorcePropertyAmount = value > 0 ? value : 0;
    this.calculateFee();
  }
  
  /**
   * 更新人格权案件相关参数
   */
  updatePersonalRightsHasDamages(hasDamages: boolean): void {
    this.hasPersonalRightsDamages = hasDamages;
    this.calculateFee();
  }
  
  updatePersonalRightsDamagesAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.personalRightsDamagesAmount = value > 0 ? value : 0;
    this.calculateFee();
  }
  
  /**
   * 更新知识产权案件相关参数
   */
  updateIntellectualPropertyHasDispute(hasDispute: boolean): void {
    this.hasIntellectualPropertyDispute = hasDispute;
    this.calculateFee();
  }
  
  updateIntellectualPropertyDisputeAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.intellectualPropertyDisputeAmount = value > 0 ? value : 0;
    this.calculateFee();
  }
  
  /**
   * 更新支付令金额
   */
  updatePaymentOrderAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.paymentOrderAmount = value > 0 ? value : 1;
    this.calculateFee();
  }
  
  /**
   * 更新保全措施金额
   */
  updatePreservationAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.preservationAmount = value > 0 ? value : 1;
    this.calculateFee();
  }
  
  /**
   * 更新破产案件金额
   */
  updateBankruptcyAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.bankruptcyAmount = value > 0 ? value : 1;
    this.calculateFee();
  }
} 