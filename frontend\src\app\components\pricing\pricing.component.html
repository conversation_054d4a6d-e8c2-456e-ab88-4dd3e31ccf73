<div class="pricing-container">
  <h1 class="pricing-title">案件收费标准</h1>
  
  <mat-card class="pricing-card">
    <mat-card-content>
      <mat-tab-group animationDuration="300ms" (selectedTabChange)="onTabChange($event.index)">
        <!-- 计时收费选项卡 -->
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="tab-icon">schedule</mat-icon>
            <span class="tab-label">计时收费</span>
          </ng-template>
          
          <div class="tab-content">
            <h2>计时收费计算器</h2>
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>工作时长（小时）</mat-label>
                <input matInput type="number" min="1" [value]="hours" (input)="updateHours($event)">
                <mat-hint>请输入律师需要工作的小时数</mat-hint>
              </mat-form-field>
            </div>
            
            <div *ngIf="hourlyResult" class="result-section">
              <h3>收费标准</h3>
              <mat-divider></mat-divider>
              
              <div class="fee-range">
                <div class="fee-item">
                  <span class="fee-label">最低收费：</span>
                  <span class="fee-value">{{ hourlyResult.hourlyRate.min.toLocaleString() }}元</span>
                </div>
                <div class="fee-item">
                  <span class="fee-label">最高收费：</span>
                  <span class="fee-value">{{ hourlyResult.hourlyRate.max.toLocaleString() }}元</span>
                </div>
              </div>
              
              <h3>收费明细</h3>
              <mat-list>
                <mat-list-item *ngFor="let detail of hourlyResult.details">
                  {{ detail }}
                </mat-list-item>
              </mat-list>
            </div>
          </div>
        </mat-tab>
        
        <!-- 民事诉讼选项卡 -->
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="tab-icon">gavel</mat-icon>
            <span class="tab-label">民事诉讼</span>
          </ng-template>
          
          <div class="tab-content">
            <h2>民事诉讼收费计算器</h2>
            
            <div class="form-row">
              <mat-radio-group class="case-type-radio" [value]="isPropertyCase" (change)="updateCivilCaseType($event.value)">
                <mat-radio-button [value]="true">涉及财产关系</mat-radio-button>
                <mat-radio-button [value]="false">不涉及财产关系</mat-radio-button>
              </mat-radio-group>
            </div>
            
            <div class="form-row" *ngIf="isPropertyCase">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>争议标的额（元）</mat-label>
                <input matInput type="number" min="0" [value]="civilAmount" (input)="updateCivilAmount($event)">
                <mat-hint>请输入案件的争议标的额</mat-hint>
              </mat-form-field>
            </div>
            
            <div *ngIf="civilResult" class="result-section">
              <h3>收费标准</h3>
              <mat-divider></mat-divider>
              
              <div class="fee-range">
                <div class="fee-item" *ngIf="civilResult.fixedFee">
                  <span class="fee-label">固定收费范围：</span>
                  <span class="fee-value">{{ civilResult.fixedFee.min.toLocaleString() }}元 - {{ civilResult.fixedFee.max.toLocaleString() }}元</span>
                </div>
                <div class="fee-item" *ngIf="civilResult.percentageFee">
                  <span class="fee-label">按比例收费：</span>
                  <span class="fee-value">{{ civilResult.percentageFee.min.toLocaleString() }}元 - {{ civilResult.percentageFee.max.toLocaleString() }}元</span>
                </div>
                <div class="fee-item total-fee">
                  <span class="fee-label">总计收费范围：</span>
                  <span class="fee-value">{{ civilResult.totalFee.min.toLocaleString() }}元 - {{ civilResult.totalFee.max.toLocaleString() }}元</span>
                </div>
              </div>
              
              <h3>收费明细</h3>
              <mat-list>
                <mat-list-item *ngFor="let detail of civilResult.details">
                  {{ detail }}
                </mat-list-item>
              </mat-list>
            </div>
          </div>
        </mat-tab>
        
        <!-- 刑事诉讼选项卡 -->
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="tab-icon">security</mat-icon>
            <span class="tab-label">刑事诉讼</span>
          </ng-template>
          
          <div class="tab-content">
            <h2>刑事诉讼收费计算器</h2>
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>案件阶段</mat-label>
                <mat-select [value]="criminalStage" (selectionChange)="updateCriminalStage($event.value)">
                  <mat-option *ngFor="let s of stages" [value]="s.value">
                    {{s.viewValue}}
                  </mat-option>
                </mat-select>
                <mat-hint>请选择案件所处的阶段</mat-hint>
              </mat-form-field>
            </div>
            
            <div *ngIf="criminalResult" class="result-section">
              <h3>收费标准</h3>
              <mat-divider></mat-divider>
              
              <div class="fee-range">
                <div class="fee-item total-fee">
                  <span class="fee-label">收费范围：</span>
                  <span class="fee-value">{{ criminalResult.fee.min.toLocaleString() }}元 - {{ criminalResult.fee.max.toLocaleString() }}元</span>
                </div>
              </div>
              
              <h3>收费明细</h3>
              <mat-list>
                <mat-list-item *ngFor="let detail of criminalResult.details">
                  {{ detail }}
                </mat-list-item>
              </mat-list>
            </div>
          </div>
        </mat-tab>
        
        <!-- 行政诉讼选项卡 -->
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="tab-icon">account_balance</mat-icon>
            <span class="tab-label">行政诉讼</span>
          </ng-template>
          
          <div class="tab-content">
            <h2>行政诉讼收费计算器</h2>
            
            <div class="form-row">
              <mat-radio-group class="case-type-radio" [value]="involveProperty" (change)="updateAdminCaseType($event.value)">
                <mat-radio-button [value]="true">涉及财产关系</mat-radio-button>
                <mat-radio-button [value]="false">不涉及财产关系</mat-radio-button>
              </mat-radio-group>
            </div>
            
            <div class="form-row" *ngIf="involveProperty">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>争议标的额（元）</mat-label>
                <input matInput type="number" min="0" [value]="adminAmount" (input)="updateAdminAmount($event)">
                <mat-hint>请输入案件的争议标的额</mat-hint>
              </mat-form-field>
            </div>
            
            <div *ngIf="adminResult" class="result-section">
              <h3>收费标准</h3>
              <mat-divider></mat-divider>
              
              <div class="fee-range">
                <div class="fee-item" *ngIf="adminResult.fixedFee">
                  <span class="fee-label">固定收费范围：</span>
                  <span class="fee-value">{{ adminResult.fixedFee.min.toLocaleString() }}元 - {{ adminResult.fixedFee.max.toLocaleString() }}元</span>
                </div>
                <div class="fee-item" *ngIf="adminResult.percentageFee">
                  <span class="fee-label">按比例收费：</span>
                  <span class="fee-value">{{ adminResult.percentageFee.min.toLocaleString() }}元 - {{ adminResult.percentageFee.max.toLocaleString() }}元</span>
                </div>
                <div class="fee-item total-fee">
                  <span class="fee-label">总计收费范围：</span>
                  <span class="fee-value">{{ adminResult.totalFee.min.toLocaleString() }}元 - {{ adminResult.totalFee.max.toLocaleString() }}元</span>
                </div>
              </div>
              
              <h3>收费明细</h3>
              <mat-list>
                <mat-list-item *ngFor="let detail of adminResult.details">
                  {{ detail }}
                </mat-list-item>
              </mat-list>
            </div>
          </div>
        </mat-tab>
        
        <!-- 非诉服务选项卡 -->
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="tab-icon">description</mat-icon>
            <span class="tab-label">非诉服务</span>
          </ng-template>
          
          <div class="tab-content">
            <h2>非诉讼服务收费计算器</h2>
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>服务类型</mat-label>
                <mat-select [value]="serviceType" (selectionChange)="updateServiceType($event.value)">
                  <mat-option *ngFor="let type of serviceTypes" [value]="type.value">
                    {{type.viewValue}}
                  </mat-option>
                </mat-select>
                <mat-hint>请选择非诉讼服务的类型</mat-hint>
              </mat-form-field>
            </div>
            
            <div class="form-row" *ngIf="isFinancialType">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>涉及财产标的额（元）</mat-label>
                <input matInput type="number" min="0" [value]="nonLitigationAmount" (input)="updateNonLitigationAmount($event)">
                <mat-hint>请输入涉及的财产标的额</mat-hint>
              </mat-form-field>
            </div>
            
            <div *ngIf="nonLitigationResult" class="result-section">
              <h3>收费标准</h3>
              <mat-divider></mat-divider>
              
              <div class="fee-range">
                <div class="fee-item total-fee">
                  <span class="fee-label">收费范围：</span>
                  <span class="fee-value">{{ nonLitigationResult.fee.min.toLocaleString() }}元 - {{ nonLitigationResult.fee.max.toLocaleString() }}元</span>
                </div>
              </div>
              
              <h3>收费明细</h3>
              <mat-list>
                <mat-list-item *ngFor="let detail of nonLitigationResult.details">
                  {{ detail }}
                </mat-list-item>
              </mat-list>
            </div>
          </div>
        </mat-tab>
        
        <!-- 风险代理选项卡 -->
        <mat-tab>
          <ng-template mat-tab-label>
            <mat-icon class="tab-icon">trending_up</mat-icon>
            <span class="tab-label">风险代理</span>
          </ng-template>
          
          <div class="tab-content">
            <h2>风险代理收费计算器</h2>
            
            <div class="form-row">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>案件标的额（元）</mat-label>
                <input matInput type="number" min="1" [value]="riskAmount" (input)="updateRiskAmount($event)">
                <mat-hint>请输入案件的标的额</mat-hint>
              </mat-form-field>
            </div>
            
            <div *ngIf="riskResult" class="result-section">
              <h3>收费标准</h3>
              <mat-divider></mat-divider>
              
              <div class="fee-range">
                <div class="fee-item">
                  <span class="fee-label">风险代理最高比例：</span>
                  <span class="fee-value">{{ riskResult.maxPercentage }}%</span>
                </div>
                <div class="fee-item total-fee">
                  <span class="fee-label">最高收费金额：</span>
                  <span class="fee-value">{{ riskResult.maxFee.toLocaleString() }}元</span>
                </div>
              </div>
              
              <div class="risk-notice">
                <p>根据相关规定，某些类型的案件不得采用风险代理方式，包括：</p>
                <ul>
                  <li>刑事诉讼案件</li>
                  <li>行政诉讼案件</li>
                  <li>国家赔偿案件</li>
                  <li>群体性诉讼案件</li>
                  <li>婚姻继承案件</li>
                  <li>请求给予社会保险待遇、最低生活保障待遇等案件</li>
                </ul>
              </div>
              
              <h3>收费明细</h3>
              <mat-list>
                <mat-list-item *ngFor="let detail of riskResult.details">
                  {{ detail }}
                </mat-list-item>
              </mat-list>
            </div>
          </div>
        </mat-tab>
      </mat-tab-group>
    </mat-card-content>
  </mat-card>
</div> 