# Generated by Django 5.0.3 on 2025-05-12 17:42

from django.db import migrations, models


def migrate_approval_permissions(apps, schema_editor):
    UserProfile = apps.get_model('users', 'UserProfile')
    User = apps.get_model('auth', 'User')
    Permission = apps.get_model('auth', 'Permission')
    ContentType = apps.get_model('contenttypes', 'ContentType')
    
    # 使用指定的数据库
    db_alias = schema_editor.connection.alias
    
    # 确保内容类型和权限存在
    try:
        # 获取或创建cases.case的ContentType
        content_type, created = ContentType.objects.using(db_alias).get_or_create(
            app_label='cases', 
            model='case',
            defaults={'model': 'case'}
        )
        if created:
            print(f"创建了ContentType: {content_type}")
        
        # 创建权限对象
        admin_perm, created = Permission.objects.using(db_alias).get_or_create(
            codename='can_admin_approve_case',
            defaults={
                'name': '行政审批',
                'content_type': content_type,
            }
        )
        if created:
            print(f"创建了权限: {admin_perm.name}")
        
        director_perm, created = Permission.objects.using(db_alias).get_or_create(
            codename='can_director_approve_case',
            defaults={
                'name': '主任审批',
                'content_type': content_type,
            }
        )
        if created:
            print(f"创建了权限: {director_perm.name}")
        
        # 迁移权限：所有有can_approve_cases=True的用户都获得这两个权限
        user_count = 0
        for profile in UserProfile.objects.using(db_alias).filter(can_approve_cases=True):
            user = profile.user
            # 检查权限是否已存在，避免重复添加
            if not user.user_permissions.filter(id=admin_perm.id).exists():
                user.user_permissions.add(admin_perm)
            if not user.user_permissions.filter(id=director_perm.id).exists():
                user.user_permissions.add(director_perm)
            user_count += 1
            print(f"为用户 {user.username} 添加了审批权限")
        
        if user_count == 0:
            print("没有找到需要迁移权限的用户")
        else:
            print(f"成功为 {user_count} 个用户添加了审批权限")
            
    except Exception as e:
        # 记录错误但不中止迁移
        print(f"迁移权限时出错: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")


def reverse_approval_permissions(apps, schema_editor):
    """
    反向操作：删除创建的权限
    """
    Permission = apps.get_model('auth', 'Permission')
    ContentType = apps.get_model('contenttypes', 'ContentType')
    
    # 使用指定的数据库
    db_alias = schema_editor.connection.alias
    
    try:
        # 删除权限
        content_type = ContentType.objects.using(db_alias).get(app_label='cases', model='case')
        deleted_count = Permission.objects.using(db_alias).filter(
            codename__in=['can_admin_approve_case', 'can_director_approve_case'],
            content_type=content_type
        ).delete()
        print(f"已删除案件审批权限，删除数量：{deleted_count[0]}")
    except (ContentType.DoesNotExist, Exception) as e:
        print(f"回退迁移时出错: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
        ('cases', '0003_alter_case_options_case_is_paid_case_status'),  # 确保cases应用已经迁移足够多的内容
        ('contenttypes', '0002_remove_content_type_name'),  # 确保contenttypes已迁移
        ('auth', '0012_alter_user_first_name_max_length'),  # 确保auth应用已完成初始化
    ]

    operations = [
        migrations.AlterField(
            model_name='userprofile',
            name='can_approve_cases',
            field=models.BooleanField(default=False, help_text='旧版权限设置，将逐步被新权限系统替代', verbose_name='可以审批案件（旧版）'),
        ),
        migrations.RunPython(
            migrate_approval_permissions,
            reverse_approval_permissions  # 添加反向操作
        ),
    ]
