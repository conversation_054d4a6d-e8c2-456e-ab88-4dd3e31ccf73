<h2 mat-dialog-title>编辑当事人信息</h2>
<mat-dialog-content class="dialog-content">
  <!-- 显示当事人基本信息（只读） -->
  <div class="party-info-section">
    <h3>当事人基本信息</h3>
    <div class="party-info-card">
      <div class="party-header">
        <span class="party-name">{{ getPartyName() }}</span>
        <span class="party-type-badge" [ngClass]="getEntityType() === 'natural' ? 'natural' : 'legal'">
          {{ getEntityType() === 'natural' ? '自然人' : '单位' }}
        </span>
      </div>
      <div class="party-details">
        <p *ngIf="getEntityType() === 'natural' && partyData.natural_person?.id_number">
          <strong>身份证号:</strong> {{ partyData.natural_person?.id_number }}
        </p>
        <p *ngIf="getEntityType() === 'natural' && partyData.natural_person?.phone_number">
          <strong>联系电话:</strong> {{ partyData.natural_person?.phone_number }}
        </p>
        <p *ngIf="getEntityType() === 'legal' && partyData.legal_entity?.representative_name">
          <strong>法定代表人:</strong> {{ partyData.legal_entity?.representative_name }}
        </p>
        <p *ngIf="getEntityType() === 'legal' && partyData.legal_entity?.credit_code">
          <strong>统一社会信用代码:</strong> {{ partyData.legal_entity?.credit_code }}
        </p>
      </div>
    </div>
  </div>

  <!-- 编辑当事人与案件的关联信息 -->
  <div class="relation-edit-section">
    <h3>关联信息设置</h3>
    
    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>当事人角色</mat-label>
      <mat-select [(ngModel)]="partyType" required>
        <!-- 根据案件类型显示不同选项 -->
        <ng-container *ngIf="caseType === '民事案件' || caseType === '行政案件'">
          <mat-option [value]="PartyType.PLAINTIFF">原告</mat-option>
          <mat-option [value]="PartyType.DEFENDANT">被告</mat-option>
          <mat-option [value]="PartyType.THIRD_PARTY">第三人</mat-option>
        </ng-container>
        
        <ng-container *ngIf="caseType === '刑事案件'">
          <mat-option [value]="PartyType.PLAINTIFF">受害人</mat-option>
          <mat-option [value]="PartyType.SUSPECT">犯罪嫌疑人</mat-option>
          <mat-option [value]="PartyType.SUSPECT_FAMILY">嫌疑人家属</mat-option>
        </ng-container>
        
        <ng-container *ngIf="caseType === '其他案件'">
          <mat-option [value]="PartyType.PLAINTIFF">原告</mat-option>
          <mat-option [value]="PartyType.DEFENDANT">被告</mat-option>
          <mat-option [value]="PartyType.THIRD_PARTY">第三人</mat-option>
          <mat-option [value]="PartyType.NON_LITIGATION_CLIENT">非诉委托人</mat-option>
        </ng-container>
      </mat-select>
    </mat-form-field>
    
    <mat-checkbox [(ngModel)]="isClient" class="client-checkbox">
      设为本案委托人
    </mat-checkbox>
    
    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>内部编号(选填)</mat-label>
      <input matInput [(ngModel)]="internalNumber" placeholder="内部编号，可不填">
    </mat-form-field>
    
    <mat-form-field appearance="outline" style="width: 100%;">
      <mat-label>备注(选填)</mat-label>
      <textarea matInput [(ngModel)]="remarks" rows="3" placeholder="关于当事人的备注信息"></textarea>
    </mat-form-field>
  </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
  <button mat-button [mat-dialog-close]="null">取消</button>
  <button mat-raised-button color="primary" 
          [disabled]="!partyType" 
          [mat-dialog-close]="{
            id: partyData.id,
            party_type: partyType,
            is_client: isClient,
            internal_number: internalNumber,
            remarks: remarks
          }">
    保存
  </button>
</mat-dialog-actions> 