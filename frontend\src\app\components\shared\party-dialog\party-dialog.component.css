.dialog-title {
  padding: 16px 24px 0;
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.dialog-content {
  height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 16px 24px;
  max-width: 800px;
  margin: 0 auto;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
  width: 100%;
}

.form-row mat-form-field {
  flex: 1 1 calc(33.33% - 16px);
  min-width: 200px;
}

.form-row .full-width,
.full-width {
  flex: 1 1 100%;
  width: 100%;
}

.party-form {
  padding: 16px 0;
}

mat-form-field {
  width: 100%;
}

@media (max-width: 768px) {
  .form-row mat-form-field {
    flex: 1 1 100%;
  }
}

.form-section {
  margin-bottom: 24px;
  border-bottom: 1px solid #eee;
  padding-bottom: 16px;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.7);
  margin-bottom: 16px;
}

.dialog-actions {
  padding: 16px 24px;
  margin: 0;
  border-top: 1px solid #eee;
}

.save-button {
  margin-left: 8px;
  min-width: 88px;
}

.cancel-button {
  margin-right: 8px;
}

/* 当事人类型选择 */
.party-type-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
}

.type-buttons {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.type-buttons button {
  padding: 20px 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.type-buttons mat-icon {
  font-size: 36px;
  height: 36px;
  width: 36px;
  margin-bottom: 10px;
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  margin: 20px 0;
}

.search-results-container {
  min-height: 100px;
  max-height: 200px;
  margin-bottom: 16px;
}

.search-results-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.search-result-item {
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.search-result-item:hover {
  background-color: #f5f5f5;
}

.search-result-item:last-child {
  border-bottom: none;
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.selected-party {
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.party-details p {
  margin: 5px 0;
}

.party-role-section {
  margin-top: 16px;
}

.client-checkbox {
  display: block;
  margin: 15px 0;
}

.party-option {
  display: flex;
  align-items: center;
}

.party-name {
  font-weight: 500;
}

.party-type-badge {
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
}

.party-type-badge.natural {
  background-color: #2196f3;
  color: white;
}

.party-type-badge.legal {
  background-color: #ff9800;
  color: white;
}

.legacy-badge {
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  background-color: #673ab7;
  color: white;
}

.party-info {
  display: block;
  font-size: 0.8em;
  color: rgba(0,0,0,0.6);
}

/* 历史案件搜索结果样式 */
.legacy-results {
  margin-top: 20px;
  border-top: 1px solid #ddd;
  padding-top: 10px;
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #fafafa;
}

.legacy-header {
  margin-bottom: 10px;
  padding: 5px 12px;
  background-color: #f0f0f0;
  border-bottom: 1px solid #e0e0e0;
}

.legacy-header h4 {
  font-size: 14px;
  color: #555;
  margin: 0;
}

.legacy-result-item {
  padding: 8px 12px;
  border-bottom: 1px dotted #eee;
  background-color: #f8f8f8;
  cursor: default;
}

.legacy-result-item:hover {
  background-color: #f0f0f0;
}

.legacy-result-item:last-child {
  border-bottom: none;
}

.legacy-badge {
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  background-color: #673ab7;
  color: white;
}

/* 针对不同类型的历史案件当事人标签样式 */
.legacy-badge {
  background-color: #673ab7;
}

/* 加载指示器特殊样式 */
.legacy-loading {
  min-height: 50px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border: 1px dashed #ddd;
}

.legacy-loading p {
  margin-top: 8px;
  color: #555;
  font-size: 13px;
} 