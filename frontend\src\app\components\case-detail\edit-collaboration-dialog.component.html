<h2 mat-dialog-title>编辑协作详情</h2>
<mat-dialog-content>
  <div class="lawyer-info">
    <p><strong>律师:</strong> {{ lawyer<PERSON><PERSON> }}</p>
  </div>
  
  <mat-form-field appearance="fill" style="width: 100%; margin-top: 16px;">
    <mat-label>费用分成比例 (%)</mat-label>
    <input matInput type="number" 
           [(ngModel)]="feeSharePercentage" 
           min="0" max="100" step="0.01"
           placeholder="例如: 20 表示20%">
    <mat-hint>请输入0-100之间的数值，例如20表示20%</mat-hint>
  </mat-form-field>
  
  <mat-form-field appearance="fill" style="width: 100%; margin-top: 16px;">
    <mat-label>备注(可选)</mat-label>
    <textarea matInput [(ngModel)]="remarks" rows="3" placeholder="添加关于协作的说明"></textarea>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button [mat-dialog-close]="null">取消</button>
  <button mat-raised-button color="primary" 
          [mat-dialog-close]="{
            feeShareRatio: feeSharePercentage / 100,
            remarks: remarks
          }">
    保存
  </button>
</mat-dialog-actions> 