.case-detail-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .back-button {
    margin-bottom: 20px;
  }

  .loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  .error-message {
    color: red;
    text-align: center;
    padding: 20px;
  }

  .status-section {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;

    button {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 32px;
      padding: 0 16px;
      border-radius: 16px;
      font-size: 14px;
      line-height: 32px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }

  .status-badge {
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;

    &.created { background-color: #e3f2fd; color: #1976d2; }
    &.applying { background-color: #fff3e0; color: #f57c00; }
    &.admin_reviewing { background-color: #e8f5e9; color: #388e3c; }
    &.director_reviewing { background-color: #f3e5f5; color: #8e24aa; }
    &.processing_delegation { background-color: #e0f7fa; color: #00acc1; }
    &.in_progress { background-color: #e8eaf6; color: #3f51b5; }
    &.applying_closure { background-color: #fce4ec; color: #d81b60; }
    &.closure_approved { background-color: #f1f8e9; color: #689f38; }
    &.archived { background-color: #eceff1; color: #546e7a; }
  }

  .payment-status {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 14px;
    background-color: #ffebee;
    color: #d32f2f;

    &.paid { background-color: #e8f5e9; color: #2e7d32; }

    mat-icon { font-size: 18px; width: 18px; height: 18px; }
  }

  .mark-paid-button {
    margin-left: 12px;
    height: 36px;
    font-size: 14px;

    mat-icon {
      margin-right: 6px;
      font-size: 18px;
      width: 18px;
      height: 18px;
    }

    &:disabled {
      opacity: 0.7;
    }
  }

  mat-card {
    margin-bottom: 20px;
    overflow: hidden;

    ::ng-deep .mat-mdc-card-header {
      padding: 0;
      margin: 0;
    }

    mat-card-header {
      background-color: #f5f5f5;
      padding: 16px 24px;
      border-bottom: 1px solid #e0e0e0;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .title-section {
          flex: 1;
          h1 {
            margin: 0;
            font-size: 24px;
          }
          .subtitle {
            color: #666;
            font-size: 14px;
            margin-top: 4px;
          }
        }

        .action-buttons {
          display: flex;
          gap: 12px;
          margin-right: 16px;

          button {
            height: 36px;

            mat-icon {
              margin-right: 8px;
            }
          }
        }

        .template-select {
          width: 200px;
          margin-bottom: 0;

          ::ng-deep {
            .mat-mdc-form-field-infix { padding: 8px 0; min-height: 36px; display: flex; align-items: center; }
            .mat-mdc-text-field-wrapper { padding: 0 10px; height: 36px; }
            .mat-mdc-form-field-flex { min-height: 36px; height: 36px; }
            .mdc-floating-label { top: 50%; transform: translateY(-50%); }
            .mdc-floating-label.mdc-floating-label--float-above { top: 10px; transform: translateY(0) scale(0.75); background: white; padding: 0 4px; margin-left: -4px; }
            .mat-mdc-select-trigger, .mat-mdc-select-value { height: 100%; display: flex; align-items: center; }
            .mat-mdc-form-field-subscript-wrapper { display: none; }
          }
        }
      }
    }

    mat-card-content {
      padding: 24px;
    }

    .info-section {
      margin-bottom: 30px;

      h3 {
        color: #333;
        font-size: 18px;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 2px solid #eee;
      }

      .status-section {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 16px;

        .status-badge {
          padding: 6px 12px;
          border-radius: 4px;
          font-weight: 500;
          background-color: #e0e0e0;
        }

        .payment-status {
          color: #f44336;

          &.paid {
            color: #4caf50;
          }
        }
      }

      .content-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 16px;
        margin-bottom: 24px;

        .content-item {
          strong {
            color: #666;
            margin-right: 8px;
          }
        }
      }

      .long-content-section {
        .content-item {
          margin-bottom: 20px;

          strong {
            display: block;
            color: #666;
            margin-bottom: 8px;
          }

          .content-text {
            padding: 12px;
            background-color: #f9f9f9;
            border-radius: 4px;
            min-height: 60px;

            &.pre-formatted {
              white-space: pre-wrap;
              font-family: inherit;
            }
          }
        }
      }

      .party-info {
        padding: 12px;
        background-color: #f9f9f9;
        border-radius: 4px;
        margin-bottom: 12px;
        position: relative;

        .party-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          flex-wrap: wrap;

          p {
            margin: 4px 0;
            flex: 1;
          }
        }

        .edit-party-button, .delete-party-button {
          margin-left: 8px;
        }
      }
    }

    pre {
      background-color: #f5f5f5;
      padding: 15px;
      border-radius: 4px;
      overflow-x: auto;
    }
  }
}

.approval-section {
  margin-top: 20px;
  margin-bottom: 20px;

  .full-width {
    width: 100%;
  }

  .approval-buttons {
    display: flex;
    gap: 16px;
    margin-top: 16px;
    justify-content: flex-end;

    button {
      min-width: 120px;
    }
  }
}

mat-card_actions {
  padding: 16px;
  display: flex;
  justify-content: flex-end;

  button {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.approval-history {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;

  .approval-record {
    margin-bottom: 16px;
    border-left: 4px solid #2196f3;

    .approval-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;

      .approver {
        font-weight: 500;
      }

      .approval-time {
        color: #666;
      }
    }

    .approval-status {
      color: #666;
      margin-bottom: 8px;
    }

    .approval-action {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      margin-bottom: 8px;
      font-weight: 500;

      &.approve {
        background-color: #e8f5e9;
        color: #2e7d32;
      }

      &.reject {
        background-color: #ffebee;
        color: #c62828;
      }

      &.revise {
        background-color: #fff3e0;
        color: #ef6c00;
      }
    }

    .approval-comment {
      padding: 8px;
      background-color: #f5f5f5;
      border-radius: 4px;
      white-space: pre-wrap;
    }
  }
}

// 添加当事人相关样式
.related-client-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;

  h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #3f51b5;
    font-weight: 500;
  }

  .loading-spinner {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 10px 0;

    span {
      color: rgba(0, 0, 0, 0.6);
    }
  }

  .error-message {
    color: #f44336;
    margin: 10px 0;
  }

  .client-details {
    .client-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .client-name {
        font-size: 1.1rem;
        font-weight: 500;
        margin-right: 10px;
      }

      .client-type-badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;

        &.natural {
          background-color: #e3f2fd;
          color: #1976d2;
        }

        &.legal {
          background-color: #e8f5e9;
          color: #2e7d32;
        }
      }
    }

    .client-info {
      padding-left: 5px;

      p {
        margin: 5px 0;
        color: rgba(0, 0, 0, 0.7);
      }
    }
  }

  .no-client {
    color: rgba(0, 0, 0, 0.5);
    font-style: italic;
  }
}

// 新增当事人列表样式
.party-list-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;

  h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-weight: 500;
  }
}

.party-info {
  padding: 12px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 12px;
  position: relative;

  .party-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    flex-wrap: wrap;

    p {
      margin: 4px 0;
      flex: 1;
    }
  }

  .edit-party-button, .delete-party-button {
    margin-left: 8px;
  }
}

.no-parties {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
  color: #666;
  font-style: italic;
}

// 律师费分配样式
.fee-distribution {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #3f51b5;
  font-size: 0.9rem;
}

.fee-distribution-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  padding: 3px 0;

  &:last-child {
    margin-bottom: 0;
    padding-top: 8px;
    border-top: 1px dashed #ccc;
    font-weight: 500;
  }
}

.fee-label {
  color: #555;
}

.fee-amount {
  font-weight: 500;
  color: #3f51b5;
}

/* 案件费用区域 */
.case-fees-section {
  .agreed-fee-section {
    margin-bottom: 20px;
  }

  .main-lawyer-share-section {
    margin-top: 25px;

    h4 {
      margin: 0 0 15px;
      font-size: 16px;
      color: #444;
      font-weight: 500;
    }
  }

  .fee-share-summary {
    padding: 12px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border-left: 3px solid #3f51b5;

    .main-lawyer-share,
    .collaborator-total-share,
    .total-fee-summary {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      padding: 4px 0;
    }

    .individual-collaborator-share {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .collaborator-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        padding-right: 10px;
        align-items: center;
      }

      .mini-edit-button {
        flex-shrink: 0;

        .mat-icon {
          font-size: 16px;
          width: 16px;
          height: 16px;
          line-height: 16px;
        }
      }
    }

    .share-values {
      display: flex;
      align-items: center;
      gap: 6px;
      white-space: nowrap;
    }

    .collaborator-total-share {
      margin-top: 5px;
      padding-top: 8px;
      border-top: 1px dashed #ccc;
      font-weight: 500;
    }

    .share-label {
      color: #555;
      flex: 1;
    }

    .share-value, .share-amount {
      font-weight: 500;

      &.main-lawyer {
        color: #1976d2;
      }

      &.collaborator {
        color: #7b1fa2;
      }

      &.collaborator-individual {
        color: #9c27b0;
      }

      &.total {
        color: #e91e63;
        font-weight: 700;
      }

      &.warning {
        color: #f44336;
        animation: pulse 1.5s infinite;
      }
    }

    .share-amount {
      font-size: 0.95em;
      color: #555;
    }

    .total-fee-summary {
      margin-top: 16px;
      padding-top: 12px;
      border-top: 2px dashed #ccc;
      font-weight: 500;
    }

    .fee-share-warning {
      margin-top: 10px;
      padding: 8px;
      background-color: #ffebee;
      border-radius: 4px;
      color: #c62828;
      display: flex;
      align-items: center;

      mat-icon {
        margin-right: 8px;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* 自定义属性样式 */
.custom-properties-section {
  margin-top: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #3f51b5;

  h3 {
    color: #3f51b5;
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 500;
  }
}

.custom-properties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.custom-property-item {
  padding: 12px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  strong {
    color: #3f51b5;
    margin-right: 8px;
  }

  span {
    color: #333;
  }
}

@media (max-width: 599px) {
  .custom-properties-grid {
    grid-template-columns: 1fr;
  }

  .custom-property-item {
    margin-bottom: 8px;
  }
}

// 协作律师样式
.collaborating-lawyers-section {
  margin-top: 16px;

  // 承办律师分成比例显示
  .main-lawyer-share-section {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3f51b5;

    h4 {
      margin: 0 0 12px 0;
      color: #3f51b5;
      font-size: 1rem;
      font-weight: 500;
    }
  }

  .fee-share-summary {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .main-lawyer-share,
    .collaborator-total-share {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      background-color: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      .share-label {
        font-weight: 500;
        color: #555;
        font-size: 0.95rem;
      }

      .share-value {
        font-weight: bold;
        font-size: 1.1rem;
        padding: 4px 8px;
        border-radius: 4px;

        &.main-lawyer {
          background-color: #e8f5e9;
          color: #2e7d32;
        }

        &.collaborator {
          background-color: #e3f2fd;
          color: #1976d2;
        }

        &.warning {
          background-color: #ffebee;
          color: #d32f2f;
        }
      }
    }

    // 各个协作律师的具体分成比例
    .individual-collaborators {
      display: flex;
      flex-direction: column;
      gap: 6px;
      margin: 4px 0;

      .individual-collaborator-share {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 12px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 3px solid #6c757d;

        .share-label {
          font-weight: 400;
          color: #6c757d;
          font-size: 0.9rem;
        }

        .share-value.collaborator-individual {
          font-weight: 600;
          font-size: 1rem;
          color: #495057;
          background-color: #e9ecef;
          padding: 2px 6px;
          border-radius: 3px;
        }

        .mini-edit-button {
          height: 24px;
          width: 24px;
          line-height: 24px;
          margin-left: 8px;

          mat-icon {
            font-size: 14px;
            width: 14px;
            height: 14px;
          }
        }

        &:hover {
          background-color: #f1f3f4;

          .mini-edit-button {
            background-color: rgba(63, 81, 181, 0.1);
          }
        }
      }
    }

    .fee-share-warning {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background-color: #fff3e0;
      border-radius: 6px;
      border-left: 3px solid #ff9800;
      color: #e65100;
      font-weight: 500;

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        color: #ff9800;
      }

      span {
        font-size: 0.9rem;
      }
    }
  }

  .lawyer-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;

    .lawyer-item {
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      padding: 8px 12px;
      border-radius: 4px;

      .lawyer-info {
        margin-right: 8px;

        .lawyer-name {
          font-weight: 500;
        }
      }
    }
  }

  .lawyer-collaborations {
    margin-top: 16px;

    .collaboration-item {
      padding: 12px;
      border-radius: 4px;
      background-color: #f5f5f5;
      margin-bottom: 8px;

      .lawyer-info {
        display: flex;
        flex-direction: column;

        .lawyer-name {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .joined-at {
          font-size: 0.9em;
          color: #666;
          margin-bottom: 4px;
        }

        .remarks {
          font-style: italic;
          margin-top: 8px;
          white-space: pre-line;
        }
      }
    }
  }

  .no-lawyers {
    color: #666;
    font-style: italic;
    margin: 16px 0;
  }

  .add-lawyer-section {
    margin-top: 16px;
  }
}

.client-badge {
  display: inline-block;
  background-color: #4caf50;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-left: 8px;
  font-weight: 500;
}

// 当事人历史记录样式
.party-history-section {
  margin-top: 12px;
  border-top: 1px dashed #e0e0e0;
  padding-top: 8px;
}

.party-history-header {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
  color: #5c6bc0;
  font-size: 14px;

  &:hover {
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  mat-icon {
    margin-right: 4px;
    transition: transform 0.3s ease;
    font-size: 18px;
    width: 18px;
    height: 18px;
  }
}

.party-history-content {
  margin-left: 24px;
  padding: 8px 0;

  .loading-spinner-small {
    display: flex;
    align-items: center;
    padding: 8px;

    span {
      margin-left: 8px;
      font-size: 14px;
      color: #666;
    }
  }
}

.party-history-item {
  background-color: #f5f7ff;
  border-left: 3px solid #5c6bc0;
  padding: 10px;
  margin-bottom: 8px;
  border-radius: 0 4px 4px 0;

  .history-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
    font-size: 14px;

    .history-item-type {
      font-weight: 500;
      color: #3f51b5;
    }

    .history-item-date {
      color: #757575;
    }
  }

  p {
    margin: 4px 0;
    font-size: 14px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

// 当事人操作按钮样式
.party-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  p {
    margin-right: 10px;
    flex: 1;
  }

  .party-actions {
    display: flex;
    gap: 4px;

    button {
      width: 28px;
      height: 28px;
      line-height: 28px;

      mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }
  }
}

// 费用分成比例样式
.fee-share-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  border-left: 3px solid #3f51b5;

  .fee-share-label {
    font-weight: 500;
    color: #555;
    font-size: 0.9rem;
  }

  .fee-share-value {
    font-weight: bold;
    color: #3f51b5;
    font-size: 1rem;
  }

  .edit-fee-button {
    margin-left: auto;
    height: 32px;
    width: 32px;
    line-height: 32px;

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }
}

.collaboration-item {
  .lawyer-info {
    .lawyer-name {
      font-weight: 500;
      color: #333;
    }

    .joined-at {
      font-size: 0.9rem;
      color: #666;
      margin-top: 4px;
      display: block;
    }

    .remarks {
      font-size: 0.9rem;
      color: #666;
      margin-top: 8px;
      font-style: italic;
    }
  }
}

// 对话框样式
.mat-dialog-content {
  .lawyer-info {
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #3f51b5;
    margin-bottom: 16px;

    p {
      margin: 0;
      font-size: 0.95rem;
      color: #333;
    }
  }
}

// 标签页相关样式
.mat-tab-group {
  margin-top: 16px;

  ::ng-deep {
    .mat-tab-header {
      border-bottom: 1px solid #e0e0e0;
    }

    .mat-tab_label {
      font-weight: 500;
      font-size: 14px;
      padding: 0 24px;
      min-width: 120px;
    }

    .mat-tab_label-active {
      color: #3f51b5;
    }

    .mat-ink-bar {
      background-color: #3f51b5;
      height: 3px;
    }

    .mat-tab-body-wrapper {
      // 确保内容有足够的空间
      min-height: 400px;
    }
  }
}

.tab-content {
  padding: 24px 0;

  // 确保每个信息部分之间有适当的间距
  .info-section {
    margin-bottom: 30px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 敏感案件和风险代理标签样式
.sensitive-case-section,
.risk-agency-section {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 12px 0;

  mat-checkbox {
    margin-right: 8px;
  }

  .info-icon {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #666;
    cursor: help;
  }
}
