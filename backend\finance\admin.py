from django.contrib import admin
from .models import OfficeExpense, FinanceRecord, WithdrawalRequest

@admin.register(OfficeExpense)
class OfficeExpenseAdmin(admin.ModelAdmin):
    list_display = ('id', 'expense_date', 'applicant', 'purpose', 'amount', 'approver', 'created_at', 'last_printed_at')
    list_filter = ('expense_date', 'approver', 'created_at')
    search_fields = ('purpose', 'description', 'applicant__username', 'approver__username')
    date_hierarchy = 'expense_date'
    readonly_fields = ('created_at', 'last_printed_at')
    autocomplete_fields = ('applicant', 'approver')
    fieldsets = (
        ('基本信息', {
            'fields': ('expense_date', 'applicant', 'purpose', 'amount', 'description')
        }),
        ('审批信息', {
            'fields': ('approver',)
        }),
        ('系统信息', {
            'fields': ('created_at', 'last_printed_at'),
            'classes': ('collapse',)
        }),
    )
    ordering = ('-created_at',)

@admin.register(FinanceRecord)
class FinanceRecordAdmin(admin.ModelAdmin):
    list_display = ('id', 'transaction_date', 'lawyer', 'account_type', 'display_amount', 'purpose', 'created_by', 'created_at')
    list_filter = ('account_type', 'transaction_date', 'created_at', 'lawyer')
    search_fields = ('purpose', 'remarks', 'lawyer__username', 'created_by__username')
    date_hierarchy = 'transaction_date'
    readonly_fields = ('created_at', 'display_amount', 'is_income', 'is_expense')
    autocomplete_fields = ('lawyer', 'created_by')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('transaction_date', 'account_type', 'amount', 'purpose')
        }),
        ('关联信息', {
            'fields': ('lawyer', 'remarks')
        }),
        ('系统信息', {
            'fields': ('created_by', 'created_at', 'display_amount', 'is_income', 'is_expense'),
            'classes': ('collapse',)
        }),
    )
    
    ordering = ('-transaction_date', '-created_at')
    
    def save_model(self, request, obj, form, change):
        """保存时自动设置创建人"""
        if not change:  # 如果是新建记录
            obj.created_by = request.user
        super().save_model(request, obj, form, change)

@admin.register(WithdrawalRequest)
class WithdrawalRequestAdmin(admin.ModelAdmin):
    list_display = ('id', 'requester', 'case', 'amount', 'deduction_tax', 'final_amount_display', 'status', 'approver', 'created_at')
    list_filter = ('status', 'created_at')
    search_fields = ('requester__username', 'case__case_number', 'remarks', 'other_deductions')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at', 'final_amount_display')
    autocomplete_fields = ('case', 'requester', 'approver')
    
    fieldsets = (
        ('申请信息', {
            'fields': ('requester', 'case', 'amount', 'remarks')
        }),
        ('审批信息', {
            'fields': ('status', 'approver', 'deduction_tax', 'other_deductions', 'final_amount_display')
        }),
        ('系统信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def final_amount_display(self, obj):
        """显示实际提取金额"""
        return f"¥{obj.final_amount:.2f}"
    final_amount_display.short_description = '实际提取金额'
    
    ordering = ('-created_at',)
    
    def save_model(self, request, obj, form, change):
        """保存审批信息时可能需要创建财务记录"""
        # 检查状态是否从未审批变为已批准
        if change and obj.status == 'APPROVED' and 'status' in form.changed_data:
            # 设置当前用户为审批人
            obj.approver = request.user
            
        super().save_model(request, obj, form, change)
