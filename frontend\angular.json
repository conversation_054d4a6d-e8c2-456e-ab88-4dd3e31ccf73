{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"law-firm-frontend": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/law-firm-frontend", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "src/styles.scss"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "25kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"host": "0.0.0.0", "port": 4200}, "configurations": {"production": {"buildTarget": "law-firm-frontend:build:production"}, "development": {"buildTarget": "law-firm-frontend:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "law-firm-frontend:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": false}}