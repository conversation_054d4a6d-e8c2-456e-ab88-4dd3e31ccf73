import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FinanceRecordsComponent } from '../finance-records/finance-records.component';
import { OfficeExpenseListComponent } from '../office-expense/office-expense-list.component';
import { WithdrawalListComponent } from '../withdrawal-list/withdrawal-list.component';
import { FinanceReportComponent } from './finance-report.component';
import { AuthService } from '../../services/auth.service';

@Component({
  selector: 'app-finance-management',
  templateUrl: './finance-management.component.html',
  styleUrls: ['./finance-management.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FinanceRecordsComponent,
    OfficeExpenseListComponent,
    WithdrawalListComponent,
    FinanceReportComponent
  ]
})
export class FinanceManagementComponent implements OnInit {
  activeTab: string = 'records';
  hasAdminPermission = false;

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.checkPermissions();
  }

  /**
   * 检查用户权限
   */
  checkPermissions(): void {
    this.authService.user$.subscribe(user => {
      this.hasAdminPermission = !!(user?.profile?.has_admin_approval_permission);
    });
  }

  /**
   * 切换标签页
   */
  switchTab(tab: string): void {
    this.activeTab = tab;
  }
} 