"""
数据库路由器 - 根据访问域名选择数据库
"""
from .middleware import get_current_firm

class DomainDatabaseRouter:
    """
    根据访问域名选择数据库的路由器
    - 如果域名以 firm2 开头，使用 firm2 数据库
    - 否则使用 default 数据库
    """
    
    def db_for_read(self, model, **hints):
        """选择读取操作的数据库"""
        return get_current_firm()
    
    def db_for_write(self, model, **hints):
        """选择写入操作的数据库"""
        return get_current_firm()
    
    def allow_relation(self, obj1, obj2, **hints):
        """允许同一数据库内的关系"""
        db_set = {'default', 'firm2'}
        if obj1._state.db in db_set and obj2._state.db in db_set:
            return True
        return None
    
    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """允许所有应用在所有数据库中迁移"""
        return True 