# Generated by Django 5.0.3 on 2025-05-11 07:35

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0023_alter_legacycase_case_cause'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='legalentity',
            options={'verbose_name': '单位', 'verbose_name_plural': '单位'},
        ),
        migrations.AlterField(
            model_name='caseparty',
            name='legal_entity',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='case_relationships', to='cases.legalentity', verbose_name='单位'),
        ),
        migrations.AlterField(
            model_name='legalentity',
            name='credit_code',
            field=models.CharField(blank=True, help_text='单位的统一社会信用代码', max_length=50, null=True, verbose_name='统一社会信用代码'),
        ),
        migrations.AlterField(
            model_name='legalentity',
            name='name',
            field=models.Char<PERSON>ield(help_text='单位的名称，用于唯一标识', max_length=200, unique=True, verbose_name='单位名称'),
        ),
        migrations.AlterField(
            model_name='legalentity',
            name='remarks',
            field=models.TextField(blank=True, help_text='关于单位的补充信息', null=True, verbose_name='备注'),
        ),
    ]
