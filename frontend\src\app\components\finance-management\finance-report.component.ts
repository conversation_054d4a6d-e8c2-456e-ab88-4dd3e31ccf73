import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FinanceService } from '../../services/finance.service';
import { AuthService } from '../../services/auth.service';
import { getUserDisplayName } from '../../utils/user.utils';

interface AnnualReportSummary {
  total_case_income: number;
  firm_income: number;
  total_office_expense: number;
  debt_lawyers_count: number;
  total_lawyers_count: number;
}

interface LawyerFinanceData {
  lawyer_id: number;
  lawyer: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  case_income: number;
  firm_share: number;
  withdrawals: number;
  payments: number;
  task_expenses: number;
  debt: number;
}

interface AnnualReportData {
  year: number;
  summary: AnnualReportSummary;
  lawyers: LawyerFinanceData[];
}

@Component({
  selector: 'app-finance-report',
  templateUrl: './finance-report.component.html',
  styleUrls: ['./finance-report.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatSelectModule,
    MatButtonModule,
    MatTableModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ]
})
export class FinanceReportComponent implements OnInit {
  filterForm: FormGroup;
  reportData: AnnualReportData | null = null;
  isLoading = false;
  hasAdminPermission = false;

  displayedColumns: string[] = [
    'lawyer_name',
    'case_income',
    'firm_share',
    'withdrawals',
    'payments',
    'task_expenses',
    'debt'
  ];

  // 生成年份选项（从2020年到当前年份）
  yearOptions: number[] = [];
  
  getUserDisplayName = getUserDisplayName;

  constructor(
    private fb: FormBuilder,
    private financeService: FinanceService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    // 生成年份选项
    const currentYear = new Date().getFullYear();
    for (let year = currentYear; year >= 2020; year--) {
      this.yearOptions.push(year);
    }

    this.filterForm = this.fb.group({
      year: [currentYear]
    });
  }

  ngOnInit(): void {
    this.checkPermissions();
  }

  checkPermissions(): void {
    this.authService.user$.subscribe(user => {
      if (user?.profile?.has_admin_approval_permission) {
        this.hasAdminPermission = true;
        // 自动加载当前年份的报告
        this.loadReport();
      } else {
        this.hasAdminPermission = false;
        this.snackBar.open('您没有权限访问财务报告', '关闭', { duration: 3000 });
      }
    });
  }

  loadReport(): void {
    if (!this.hasAdminPermission) {
      return;
    }

    const year = this.filterForm.get('year')?.value;
    if (!year) {
      return;
    }

    this.isLoading = true;
    this.financeService.getAnnualReport(year).subscribe({
      next: (data: AnnualReportData) => {
        this.reportData = data;
        this.isLoading = false;
      },
      error: (error: any) => {
        console.error('加载财务报告失败:', error);
        this.snackBar.open('加载财务报告失败', '关闭', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(amount);
  }

  getDebtStatusClass(debt: number): string {
    if (debt > 0) {
      return 'positive-debt'; // 盈余
    } else if (debt < 0) {
      return 'negative-debt'; // 欠款
    }
    return 'zero-debt'; // 无欠款
  }

  getAbsValue(value: number): number {
    return Math.abs(value);
  }
} 