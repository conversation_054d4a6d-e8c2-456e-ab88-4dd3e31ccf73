from django.db import models
from django.contrib.auth.models import User
from django.contrib.postgres.fields import J<PERSON><PERSON><PERSON>
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.db.models import Max

class CaseNumber(models.Model):
    """案件编号管理"""
    TYPE_CHOICES = (
        ('民', '民事案件'),
        ('刑', '刑事案件'),
        ('行', '行政案件'),
        ('非诉', '其他案件')
    )
    
    type_code = models.CharField(
        max_length=10, 
        verbose_name='类型代号',
        choices=TYPE_CHOICES,
        help_text='选择案件类型的代号'
    )
    year = models.IntegerField(verbose_name='年份')
    start_number = models.IntegerField(verbose_name='顺序编号起点', default=1)
    last_used_number = models.IntegerField(verbose_name='最后使用的顺序编号', default=0)
    
    class Meta:
        verbose_name = '案件编号'
        verbose_name_plural = '案件编号'
        unique_together = ['type_code', 'year']
        
    def __str__(self):
        return f"{self.year}年{self.get_type_code_display()}编号管理"

class Case(models.Model):
    STATUS_CHOICES = (
        ('CREATED', '创建'),
        ('APPLYING', '申请中'),
        ('ADMIN_REVIEWING', '行政审批中'),
        ('DIRECTOR_REVIEWING', '主任审批中'),
        ('PROCESSING_DELEGATION', '办理委托手续中'),
        ('IN_PROGRESS', '办案中'),
        ('APPLYING_CLOSURE', '申请结案中'),
        ('CLOSURE_APPROVED', '已通过主任审批结案'),
        ('ARCHIVED', '已归档'),
    )

    case_number = models.CharField(max_length=50, unique=True, null=True, blank=True)
    case_cause = models.CharField(max_length=200)
    lawyer = models.ForeignKey(User, on_delete=models.PROTECT, related_name='cases')
    status = models.CharField(
        max_length=50, 
        choices=STATUS_CHOICES,
        default='CREATED'
    )
    is_paid = models.BooleanField(default=False)
    is_sensitive = models.BooleanField(default=False, verbose_name='重大敏感案件', help_text='标记是否为重大敏感案件')
    is_risk_agency = models.BooleanField(default=False, verbose_name='是否风险代理', help_text='标记是否为风险代理案件')
    agreed_lawyer_fee = models.DecimalField(
        max_digits=12, 
        decimal_places=2, 
        null=True, 
        blank=True,
        verbose_name='商定律师费',
        help_text='与客户商定的律师费用金额'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    content = models.JSONField(default=dict)

    def __str__(self):
        return f"{self.case_number} - {self.case_cause}"

    def check_and_update_payment_status(self):
        """检查财务记录并更新付费状态"""
        if not self.agreed_lawyer_fee:
            return False
            
        # 查询该案件的所有案件收入记录
        from finance.models import FinanceRecord
        case_income_records = FinanceRecord.objects.filter(
            case=self,
            account_type='case_income'
        )
        
        # 计算总收入金额
        total_income = sum(record.amount for record in case_income_records)
        
        # 如果总收入大于等于商定律师费，更新为已付费
        if total_income >= self.agreed_lawyer_fee:
            if not self.is_paid:
                self.is_paid = True
                self.save(update_fields=['is_paid'])
            return True
        else:
            if self.is_paid:
                self.is_paid = False
                self.save(update_fields=['is_paid'])
            return False

    class Meta:
        ordering = ['-created_at']
        verbose_name = '案件'
        verbose_name_plural = '案件'


class CaseApproval(models.Model):
    ACTION_CHOICES = (
        ('approve', '审批通过'),
        ('reject', '审批不通过'),
        ('revise', '需要修改'),
    )

    case = models.ForeignKey(Case, on_delete=models.CASCADE, related_name='approvals')
    approver = models.ForeignKey(User, on_delete=models.PROTECT, related_name='case_approvals')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    comment = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    status_when_approved = models.CharField(max_length=50, default='APPLYING_CLOSURE')

    def __str__(self):
        return f"{self.case.case_number} - {self.get_action_display()} by {self.approver.username}"

    class Meta:
        ordering = ['-created_at']
        verbose_name = '案件审批'
        verbose_name_plural = '案件审批'

class NaturalPerson(models.Model):
    """自然人信息"""
    name = models.CharField(
        max_length=100,
        verbose_name='姓名'
    )
    id_number = models.CharField(
        max_length=18,
        null=True,
        blank=True,
        verbose_name='身份证号码',
        help_text='身份证号码，用于唯一标识'
    )
    phone_number = models.CharField(
        max_length=11,
        null=True,
        blank=True,
        verbose_name='手机号码',
        help_text='手机号码，用于唯一标识'
    )
    remarks = models.TextField(
        null=True,
        blank=True,
        verbose_name='备注',
        help_text='关于自然人的补充信息'
    )
    creator = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_natural_persons',
        verbose_name='创建人',
        help_text='创建该自然人记录的用户'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '自然人'
        verbose_name_plural = '自然人'

    def clean(self):
        # 如果提供了身份证号码，验证其唯一性（非空值）
        if self.id_number:
            # 查找是否存在相同身份证号的记录（排除当前实例）
            same_id_number = NaturalPerson.objects.filter(id_number=self.id_number)
            if self.pk:
                same_id_number = same_id_number.exclude(pk=self.pk)
            if same_id_number.exists():
                raise ValidationError({'id_number': '该身份证号码已被使用'})
        
        # 如果提供了手机号码，验证其唯一性（非空值）
        if self.phone_number:
            # 查找是否存在相同手机号的记录（排除当前实例）
            same_phone = NaturalPerson.objects.filter(phone_number=self.phone_number)
            if self.pk:
                same_phone = same_phone.exclude(pk=self.pk)
            if same_phone.exists():
                raise ValidationError({'phone_number': '该手机号码已被使用'})

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.id_number or self.phone_number})"

class LegalEntity(models.Model):
    """单位信息"""
    name = models.CharField(
        max_length=200,
        verbose_name='单位名称',
        unique=True,
        help_text='单位的名称，用于唯一标识'
    )
    representative_name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name='法人代表姓名'
    )
    representative_id_number = models.CharField(
        max_length=18,
        null=True,
        blank=True,
        verbose_name='法人代表身份证号码'
    )
    representative_phone_number = models.CharField(
        max_length=11,
        null=True,
        blank=True,
        verbose_name='法人代表手机号码'
    )
    credit_code = models.CharField(
        max_length=50,
        null=True,
        blank=True,
        verbose_name='统一社会信用代码',
        help_text='单位的统一社会信用代码'
    )
    remarks = models.TextField(
        null=True,
        blank=True,
        verbose_name='备注',
        help_text='关于单位的补充信息'
    )
    creator = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_legal_entities',
        verbose_name='创建人',
        help_text='创建该单位记录的用户'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '单位'
        verbose_name_plural = '单位'

    def clean(self):
        if not self.name:
            raise ValidationError('必须提供单位名称')

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} (法人代表: {self.representative_name})"

class CaseParty(models.Model):
    """案件当事人关系表"""
    PARTY_TYPE_CHOICES = (
        ('PLAINTIFF', '原告'),
        ('DEFENDANT', '被告'),
        ('THIRD_PARTY', '第三人'),
        ('SUSPECT', '犯罪嫌疑人'),
        ('SUSPECT_FAMILY', '嫌疑人家属'),
        ('NON_LITIGATION_CLIENT', '非诉委托人'),
    )
    
    case = models.ForeignKey(
        'Case',
        on_delete=models.CASCADE,
        related_name='party_relationships',
        verbose_name='案件'
    )
    natural_person = models.ForeignKey(
        NaturalPerson,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='case_relationships',
        verbose_name='自然人'
    )
    legal_entity = models.ForeignKey(
        LegalEntity,
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='case_relationships',
        verbose_name='单位'
    )
    party_type = models.CharField(
        max_length=30,
        choices=PARTY_TYPE_CHOICES,
        verbose_name='当事人类型'
    )
    is_client = models.BooleanField(
        default=False,
        verbose_name='是否为委托人',
        help_text='标记该当事人是否为本案委托人'
    )
    internal_number = models.CharField(
        max_length=50, 
        null=True, 
        blank=True, 
        verbose_name='内部编号',
        help_text='当事人在案件内部的编号'
    )
    remarks = models.TextField(
        null=True, 
        blank=True, 
        verbose_name='备注',
        help_text='关于当事人的补充信息'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '案件当事人关系'
        verbose_name_plural = '案件当事人关系'
        
    def clean(self):
        # 验证只能关联一种当事人类型
        if (self.natural_person and self.legal_entity) or \
           (not self.natural_person and not self.legal_entity):
            raise ValidationError('必须且只能关联一个自然人或单位')
        
        # 验证同一个当事人在一个案件中只能有一种角色
        if self.natural_person:
            existing = CaseParty.objects.filter(
                case=self.case,
                natural_person=self.natural_person
            ).exclude(pk=self.pk).exists()
            if existing:
                raise ValidationError('同一个自然人在一个案件中只能出现一次')
        
        if self.legal_entity:
            existing = CaseParty.objects.filter(
                case=self.case,
                legal_entity=self.legal_entity
            ).exclude(pk=self.pk).exists()
            if existing:
                raise ValidationError('同一个单位在一个案件中只能出现一次')
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
    
    def __str__(self):
        party = self.natural_person or self.legal_entity
        return f"{self.case} - {party} ({self.get_party_type_display()})"

class CaseLawyerCollaboration(models.Model):
    """案件律师协作关系表"""
    case = models.ForeignKey(
        Case,
        on_delete=models.CASCADE,
        related_name='lawyer_collaborations',
        verbose_name='案件'
    )
    lawyer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='case_collaborations',
        verbose_name='协作律师'
    )
    fee_share_ratio = models.DecimalField(
        max_digits=5,
        decimal_places=4,
        default=0.0000,
        verbose_name='费用分成比例',
        help_text='该律师对案件费用的分成比例，取值范围0-1（如0.2表示20%）'
    )
    joined_at = models.DateTimeField(auto_now_add=True, verbose_name='加入时间')
    remarks = models.TextField(
        null=True,
        blank=True,
        verbose_name='备注',
        help_text='关于律师协作的补充信息'
    )
    
    class Meta:
        verbose_name = '案件律师协作关系'
        verbose_name_plural = '案件律师协作关系'
        unique_together = ['case', 'lawyer']
        
    def clean(self):
        # 验证不能将案件创建人添加为协作律师
        if self.lawyer == self.case.lawyer:
            raise ValidationError('不能将案件创建人添加为协作律师')
    
    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
        
    def __str__(self):
        return f"{self.case} - 协作律师: {self.lawyer.username}"

class LegacyCase(models.Model):
    """历史案件记录"""
    CASE_TYPE_CHOICES = (
        ('民事', '民事案件'),
        ('刑事', '刑事案件'),
        ('行政', '行政案件'),
    )
    
    contract_number = models.CharField(max_length=50, verbose_name='合同编号')
    filing_date = models.DateField(verbose_name='收案日期')
    client = models.CharField(max_length=200, verbose_name='委托人/当事人')
    opposing_party = models.CharField(max_length=200, verbose_name='对方当事人', null=True, blank=True)
    criminal = models.CharField(max_length=200, verbose_name='犯罪嫌疑人', null=True, blank=True)
    case_cause = models.CharField(max_length=200, verbose_name='案由', null=True, blank=True)
    court = models.CharField(max_length=200, verbose_name='受理机关', null=True, blank=True)
    trial_level = models.CharField(max_length=50, verbose_name='审级', null=True, blank=True)
    lawyer = models.CharField(max_length=100, verbose_name='承办律师', null=True, blank=True)
    case_type = models.CharField(max_length=10, choices=CASE_TYPE_CHOICES, default='民事', verbose_name='案件类型')
    fee_amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='收费金额', null=True, blank=True)
    is_fee_compliant = models.BooleanField(verbose_name='是否符合收费标准', default=True)
    invoice_number = models.CharField(max_length=50, verbose_name='发票号码', null=True, blank=True)
    is_conflict = models.BooleanField(verbose_name='是否利益冲突', default=False)
    is_sensitive = models.BooleanField(verbose_name='是否重大敏感案件', default=False)
    approver = models.CharField(max_length=100, verbose_name='审批人', null=True, blank=True)
    closing_date = models.DateField(verbose_name='结案日期', null=True, blank=True)
    archive_date = models.DateField(verbose_name='归档日期', null=True, blank=True)
    remarks = models.TextField(verbose_name='备注', null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '历史案件'
        verbose_name_plural = '历史案件'
        ordering = ['-filing_date']

    def __str__(self):
        return f"{self.contract_number} - {self.case_cause}"

class SealUsageRecord(models.Model):
    """公章使用登记表"""
    SEAL_TYPE_CHOICES = (
        ('公章', '公章'),
        ('合同章', '合同章'),
        ('财务章', '财务章'),
        ('法人章', '法人章'),
        ('其他', '其他'),
    )
    
    # 序号每年从1开始，通过年份和序号组合保证唯一性
    year = models.IntegerField(verbose_name="年份", help_text="记录年份")
    sequence_number = models.IntegerField(verbose_name="序号", help_text="当年序号", null=True, blank=True)
    date = models.DateField(verbose_name="日期")
    matter = models.CharField(max_length=200, verbose_name="事由")
    
    # 盖章相关信息
    document_name = models.CharField(max_length=200, verbose_name="文件名称")
    quantity = models.IntegerField(verbose_name="数量", default=1)
    seal_type = models.CharField(
        max_length=20, 
        choices=SEAL_TYPE_CHOICES, 
        verbose_name="用章种类",
        default='公章'
    )
    
    recipient_unit = models.CharField(max_length=200, verbose_name="受送单位")
    user_name = models.CharField(max_length=100, verbose_name="使用人", default="")
    approver = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='approved_seal_records',
        verbose_name="审批人"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="审批时间")
    operator_name = models.CharField(max_length=100, verbose_name="盖章经办人", blank=True, null=True, default="")
    remarks = models.TextField(blank=True, null=True, verbose_name="备注")
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    last_printed_at = models.DateTimeField(null=True, blank=True, verbose_name="最后打印时间")
    
    class Meta:
        verbose_name = "公章使用登记"
        verbose_name_plural = "公章使用登记"
        unique_together = ['year', 'sequence_number']
        ordering = ['-created_at']
    
    def __str__(self):
        if self.sequence_number:
            return f"（{self.year}）公章使用{self.sequence_number}号 - {self.matter}"
        else:
            return f"未审批 - {self.matter}"
    
    def save(self, *args, **kwargs):
        # 处理年份设置
        if isinstance(self.date, str):
            from datetime import datetime
            date_obj = datetime.strptime(self.date, '%Y-%m-%d').date()
            current_year = date_obj.year
        else:
            current_year = self.date.year if self.date else timezone.now().year
        
        self.year = current_year
        
        # 只有在有审批人且没有序号时才生成序号
        if self.approver and not self.sequence_number:
            # 获取当年最大序号（只计算已审批的记录）
            max_seq = SealUsageRecord.objects.filter(
                year=current_year,
                approver__isnull=False  # 只计算已审批的记录
            ).aggregate(
                max_seq=Max('sequence_number')
            )['max_seq']
            
            self.sequence_number = (max_seq or 0) + 1
            # 设置审批时间
            if not self.approved_at:
                self.approved_at = timezone.now()
        elif not self.approver:
            # 如果没有审批人，清空序号和审批时间
            self.sequence_number = None
            self.approved_at = None
        
        super().save(*args, **kwargs)
    
    def print_record(self):
        """更新打印时间"""
        self.last_printed_at = timezone.now()
        self.save()

class LetterRecord(models.Model):
    """函件登记表"""
    LETTER_TYPE_CHOICES = (
        ('广承民函', '广承民函'),
        ('广承刑函', '广承刑函'),
        ('广承行函', '广承行函'),
        ('广承刑会', '广承刑会'),
        ('广承律调', '广承律调'),
        ('广承律师函', '广承律师函'),
        ('广承意见', '广承意见'),
        ('广承雷民函', '广承雷民函'),
        ('广承雷刑函', '广承雷刑函'),
        ('广承雷行函', '广承雷行函'),
        ('广承雷刑会', '广承雷刑会'),
        ('广承雷律调', '广承雷律调'),
        ('广承雷律师函', '广承雷律师函'),
        ('广承雷意见', '广承雷意见'),
    )
    
    # 年份
    year = models.IntegerField(verbose_name="年份", help_text="记录年份")
    
    # 函数编号 - 原有的sequence_number字段改为函数编号，格式如：（2024）广承民函1号
    letter_number = models.IntegerField(verbose_name="函数编号", help_text="函件编号，按年份和函件类型分组", null=True, blank=True)
    
    # 新的数字序号 - 只有审批后才显示，按数字顺序生成
    sequence_number = models.IntegerField(verbose_name="序号", help_text="审批后的数字序号", null=True, blank=True)
    
    date = models.DateField(verbose_name="日期")
    case_number = models.CharField(max_length=100, verbose_name="对应案件号", blank=True, null=True)
    client_name = models.CharField(max_length=200, verbose_name="委托人/当事人")
    quantity = models.IntegerField(verbose_name="数量", default=1)
    recipient_unit = models.CharField(max_length=200, verbose_name="致函单位")
    lawyer_name = models.CharField(max_length=100, verbose_name="使用律师")
    letter_type = models.CharField(
        max_length=20, 
        choices=LETTER_TYPE_CHOICES, 
        verbose_name="函件类型",
        default='广承民函'
    )
    approver = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='approved_letter_records',
        verbose_name="审批人"
    )
    approved_at = models.DateTimeField(null=True, blank=True, verbose_name="审批时间")
    remarks = models.TextField(blank=True, null=True, verbose_name="备注")
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    last_printed_at = models.DateTimeField(null=True, blank=True, verbose_name="最后打印时间")
    
    class Meta:
        verbose_name = "函件登记"
        verbose_name_plural = "函件登记"
        unique_together = ['year', 'letter_number', 'letter_type']
        ordering = ['sequence_number', '-created_at']  # 按序号排序，未审批的按创建时间倒序
    
    def __str__(self):
        if self.letter_number:
            return f"（{self.year}）{self.letter_type}{self.letter_number}号 - {self.client_name}"
        else:
            return f"未审批 - {self.client_name}"
    
    def save(self, *args, **kwargs):
        # 处理年份设置
        if isinstance(self.date, str):
            from datetime import datetime
            date_obj = datetime.strptime(self.date, '%Y-%m-%d').date()
            current_year = date_obj.year
        else:
            current_year = self.date.year if self.date else timezone.now().year
        
        self.year = current_year
        
        # 只有在有审批人且没有函数编号时才生成函数编号（审批后才生成）
        if self.approver and not self.letter_number:
            # 获取当年同类型函件的最大函数编号
            max_letter_num = LetterRecord.objects.filter(
                year=current_year, 
                letter_type=self.letter_type,
                approver__isnull=False  # 只计算已审批的记录
            ).aggregate(
                max_num=Max('letter_number')
            )['max_num']
            
            self.letter_number = (max_letter_num or 0) + 1
        elif not self.approver:
            # 如果没有审批人，清空函数编号
            self.letter_number = None
        
        # 只有在有审批人且没有序号时才生成序号
        if self.approver and not self.sequence_number:
            # 获取当年所有已审批记录的最大序号
            max_seq = LetterRecord.objects.filter(
                year=current_year,
                approver__isnull=False  # 只计算已审批的记录
            ).aggregate(
                max_seq=Max('sequence_number')
            )['max_seq']
            
            self.sequence_number = (max_seq or 0) + 1
            # 设置审批时间
            if not self.approved_at:
                self.approved_at = timezone.now()
        elif not self.approver:
            # 如果没有审批人，清空序号和审批时间
            self.sequence_number = None
            self.approved_at = None
        
        super().save(*args, **kwargs)
    
    def print_record(self):
        """更新打印时间"""
        self.last_printed_at = timezone.now()
        self.save() 