.container {
  padding: 20px;
  
  h2 {
    margin-bottom: 20px;
    font-weight: 500;
  }

  .description {
    color: #666;
    margin-bottom: 24px;
  }

  .upload-container {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    button {
      margin-right: 16px;
    }

    .selected-file {
      margin-top: 16px;
      padding: 12px;
      border: 1px solid #e8e8e8;
      border-radius: 4px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      p {
        margin-bottom: 0;
        font-size: 14px;
        color: #333;
      }
    }
  }

  .tips {
    margin-top: 24px;

    h3 {
      margin-bottom: 16px;
      font-weight: 500;
    }

    ul {
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        
        ul {
          margin-top: 8px;
        }
      }
    }
  }
} 