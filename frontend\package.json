{"name": "law-firm-frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200 --poll=2000", "dev": "ng serve --host 0.0.0.0 --port 4200 --poll=2000 --proxy-config proxy.conf.js --configuration development", "build": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular-devkit/build-angular": "^17.3.0", "@angular/animations": "17.3.0", "@angular/cdk": "17.3.0", "@angular/cli": "^17.3.0", "@angular/common": "17.3.0", "@angular/compiler": "17.3.0", "@angular/core": "17.3.0", "@angular/forms": "17.3.0", "@angular/material": "17.3.0", "@angular/platform-browser": "17.3.0", "@angular/platform-browser-dynamic": "17.3.0", "@angular/router": "17.3.0", "file-saver": "^2.0.5", "jszip": "^3.10.1", "@ctrl/tinycolor": "^3.6.1", "jwt-decode": "4.0.0", "ng-zorro-antd": "^17.0.0", "ngx-toastr": "19.0.0", "rxjs": "7.8.0", "tslib": "2.3.0", "zone.js": "0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "17.3.0", "@angular/cli": "17.3.0", "@angular/compiler-cli": "17.3.0", "@types/file-saver": "^2.0.7", "@types/jasmine": "5.1.0", "esbuild": "^0.20.0", "jasmine-core": "5.1.0", "karma": "6.4.0", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.0", "karma-jasmine": "5.1.0", "karma-jasmine-html-reporter": "2.1.0", "rollup": "3.29.4", "typescript": "5.2.2", "vite": "^5.1.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.9.5"}}