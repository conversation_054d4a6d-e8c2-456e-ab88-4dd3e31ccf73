.finance-records-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  
  .header-card {
    margin-bottom: 20px;
    
    mat-card-header {
      display: flex;
      align-items: center;
      
      mat-card-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 1.5rem;
        color: #1976d2;
        
        mat-icon {
          font-size: 1.8rem;
        }
      }
    }
    
    .permission-notice {
      color: #666;
      font-style: italic;
      padding: 8px 16px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
  }
  
  .filter-card {
    margin-bottom: 20px;
    
    .filter-form {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      align-items: start;
      
      mat-form-field {
        width: 100%;
      }
      
      button {
        align-self: end;
        height: 56px;
        margin-bottom: 1.25em;
      }
    }
  }
  
  .table-card {
    .loading-container {
      display: flex;
      justify-content: center;
      padding: 40px;
    }
    
    .table-container {
      overflow-x: auto;
      
      .finance-table {
        width: 100%;
        
        th {
          font-weight: 600;
          color: #333;
        }
        
        td {
          &.income-amount {
            color: #4caf50;
            font-weight: 600;
          }
          
          &.expense-amount {
            color: #f44336;
            font-weight: 600;
          }
        }
        
        mat-chip {
          &.income-chip {
            background-color: #e8f5e8;
            color: #2e7d32;
          }
          
          &.expense-chip {
            background-color: #ffebee;
            color: #c62828;
          }
        }
        
        .case-info {
          display: flex;
          flex-direction: column;
          gap: 2px;
          min-width: 120px;
          
          .case-number {
            font-weight: 500;
            color: #1976d2;
            font-size: 0.9em;
          }
          
          .case-cause {
            color: #666;
            font-size: 0.8em;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
        
        .no-case {
          color: #999;
          font-style: italic;
        }
      }
    }
    
    .no-data {
      text-align: center;
      padding: 40px;
      color: #666;
      
      mat-icon {
        font-size: 48px;
        width: 48px;
        height: 48px;
        margin-bottom: 16px;
        opacity: 0.5;
      }
      
      p {
        font-size: 1.1rem;
        margin: 0;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .finance-records-container {
    padding: 10px;
    
    .filter-card .filter-form {
      grid-template-columns: 1fr;
    }
    
    .table-card .table-container {
      .finance-table {
        font-size: 0.9rem;
        
        th, td {
          padding: 8px 4px;
        }
      }
    }
  }
}

// 对话框样式
::ng-deep .mat-mdc-dialog-container {
  .record-form {
    mat-form-field {
      margin-bottom: 16px;
    }
  }
} 