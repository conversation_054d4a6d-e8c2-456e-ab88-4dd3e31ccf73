#!/usr/bin/env python3
"""
修复函件登记表数据库迁移问题的脚本
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'law_firm.settings')
django.setup()

from django.db import connection, transaction
from cases.models import LetterRecord

def fix_letter_record_table():
    """修复函件登记表的数据库结构"""
    with connection.cursor() as cursor:
        print("检查当前表结构...")
        
        # 检查表是否存在letter_number字段
        cursor.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name='cases_letterrecord' AND column_name='letter_number';
        """)
        has_letter_number = cursor.fetchone()
        
        if not has_letter_number:
            print("添加letter_number字段...")
            cursor.execute("""
                ALTER TABLE cases_letterrecord 
                ADD COLUMN letter_number INTEGER;
            """)
        
        # 检查sequence_number字段是否允许null
        cursor.execute("""
            SELECT is_nullable FROM information_schema.columns 
            WHERE table_name='cases_letterrecord' AND column_name='sequence_number';
        """)
        result = cursor.fetchone()
        
        if result and result[0] == 'NO':
            print("修改sequence_number字段允许NULL...")
            cursor.execute("""
                ALTER TABLE cases_letterrecord 
                ALTER COLUMN sequence_number DROP NOT NULL;
            """)
        
        # 删除旧的唯一约束（如果存在）
        cursor.execute("""
            SELECT constraint_name FROM information_schema.table_constraints 
            WHERE table_name='cases_letterrecord' AND constraint_type='UNIQUE';
        """)
        constraints = cursor.fetchall()
        
        for constraint in constraints:
            constraint_name = constraint[0]
            if 'sequence_number' in constraint_name.lower():
                print(f"删除约束: {constraint_name}")
                cursor.execute(f"ALTER TABLE cases_letterrecord DROP CONSTRAINT {constraint_name};")
        
        # 添加新的唯一约束
        print("添加新的唯一约束...")
        cursor.execute("""
            ALTER TABLE cases_letterrecord 
            ADD CONSTRAINT cases_letterrecord_year_letter_number_letter_type_uniq 
            UNIQUE (year, letter_number, letter_type);
        """)
        
        print("数据库结构修复完成!")

def populate_letter_numbers():
    """为现有记录填充letter_number"""
    print("开始填充letter_number...")
    
    from collections import defaultdict
    records_by_year_type = defaultdict(list)
    
    # 获取所有记录并按年份和类型分组
    for record in LetterRecord.objects.all().order_by('created_at'):
        records_by_year_type[(record.year, record.letter_type)].append(record)
    
    # 为每个分组的记录分配letter_number
    with transaction.atomic():
        for (year, letter_type), records in records_by_year_type.items():
            print(f"处理 {year}年 {letter_type}: {len(records)} 条记录")
            for i, record in enumerate(records, 1):
                record.letter_number = i
                record.save(update_fields=['letter_number'])
    
    print("letter_number填充完成!")

def reset_sequence_numbers():
    """重置sequence_number，只有已审批的记录才有序号"""
    print("重置sequence_number...")
    
    with transaction.atomic():
        # 清空所有sequence_number
        LetterRecord.objects.all().update(sequence_number=None)
        
        # 为已审批的记录重新分配序号
        for year in LetterRecord.objects.values_list('year', flat=True).distinct():
            approved_records = LetterRecord.objects.filter(
                year=year,
                approver__isnull=False
            ).order_by('approved_at')
            
            for i, record in enumerate(approved_records, 1):
                record.sequence_number = i
                record.save(update_fields=['sequence_number'])
            
            print(f"{year}年已审批记录: {approved_records.count()} 条")
    
    print("sequence_number重置完成!")

if __name__ == '__main__':
    try:
        print("开始修复函件登记表...")
        fix_letter_record_table()
        populate_letter_numbers()
        reset_sequence_numbers()
        print("修复完成!")
    except Exception as e:
        print(f"修复过程中出现错误: {e}")
        sys.exit(1) 