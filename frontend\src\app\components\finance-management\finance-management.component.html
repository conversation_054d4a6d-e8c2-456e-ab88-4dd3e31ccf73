<div class="finance-management">
  <div class="header">
    <h1>财务管理</h1>
  </div>
  
  <div class="tabs">
    <button 
      class="tab-button" 
      [class.active]="activeTab === 'records'"
      (click)="switchTab('records')">
      财务记录
    </button>
    <button 
      class="tab-button" 
      [class.active]="activeTab === 'expenses'"
      (click)="switchTab('expenses')">
      办公费用
    </button>
    <button 
      class="tab-button" 
      [class.active]="activeTab === 'withdrawals'"
      (click)="switchTab('withdrawals')">
      提款申请
    </button>
    <button 
      class="tab-button" 
      [class.active]="activeTab === 'report'"
      (click)="switchTab('report')"
      *ngIf="hasAdminPermission">
      财务报告
    </button>
  </div>
  
  <div class="tab-content">
    <div *ngIf="activeTab === 'records'" class="tab-pane">
      <app-finance-records></app-finance-records>
    </div>
    
    <div *ngIf="activeTab === 'expenses'" class="tab-pane">
      <app-office-expense-list></app-office-expense-list>
    </div>
    
    <div *ngIf="activeTab === 'withdrawals'" class="tab-pane">
      <app-withdrawal-list></app-withdrawal-list>
    </div>
    
    <div *ngIf="activeTab === 'report'" class="tab-pane">
      <app-finance-report></app-finance-report>
    </div>
  </div>
</div> 