import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpResponse,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

@Injectable()
export class HttpLoggerInterceptor implements HttpInterceptor {
  constructor() {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<unknown>> {
    // 只记录非生产环境下的API请求日志
    if (environment.production || !request.url.includes(environment.apiUrl)) {
      return next.handle(request);
    }

    // 记录请求信息
    this.logRequest(request);

    return next.handle(request).pipe(
      tap(event => {
        if (event instanceof HttpResponse) {
          this.logResponse(event);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.logError(request, error);
        return throwError(() => error);
      })
    );
  }

  /**
   * 记录HTTP请求详情
   */
  private logRequest(request: HttpRequest<any>): void {
    console.group(`🚀 HTTP请求: ${request.method} ${request.url.split('/').slice(-2).join('/')}`);
    
    const headers = {};
    request.headers.keys().forEach(key => {
      headers[key] = request.headers.getAll(key);
    });
    
    console.log('请求头:', headers);
    
    // 对FormData类型的请求体特殊处理
    if (request.body instanceof FormData) {
      const formDataEntries = {};
      request.body.forEach((value, key) => {
        if (value instanceof File) {
          formDataEntries[key] = `文件: ${value.name} (${value.type}, ${value.size} 字节)`;
        } else {
          formDataEntries[key] = value;
        }
      });
      console.log('请求体 (FormData):', formDataEntries);
    } else if (request.body) {
      console.log('请求体:', request.body);
    }
    
    console.groupEnd();
  }

  /**
   * 记录HTTP响应详情
   */
  private logResponse(response: HttpResponse<any>): void {
    console.group(`✅ HTTP响应: ${response.status} ${response.url?.split('/').slice(-2).join('/')}`);
    console.log('响应头:', response.headers);
    console.log('响应体:', response.body);
    console.groupEnd();
  }

  /**
   * 记录HTTP错误详情
   */
  private logError(request: HttpRequest<any>, error: HttpErrorResponse): void {
    console.group(`❌ HTTP错误: ${request.method} ${request.url.split('/').slice(-2).join('/')}`);
    console.log('状态:', error.status, error.statusText);
    console.log('错误信息:', error.message);
    
    if (error.error) {
      if (typeof error.error === 'string') {
        try {
          const parsedError = JSON.parse(error.error);
          console.log('错误详情:', parsedError);
        } catch {
          console.log('错误详情:', error.error);
        }
      } else {
        console.log('错误详情:', error.error);
      }
    }
    
    console.log('请求详情:', {
      url: request.url,
      method: request.method,
      headers: request.headers,
      body: request.body
    });
    
    console.groupEnd();
  }
} 