.dialog-content {
  height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 16px;
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100px;
  margin: 20px 0;
}

.search-results-container {
  min-height: 100px;
  max-height: 200px;
  margin-bottom: 16px;
}

.search-results-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.search-result-item {
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}

.search-result-item:hover {
  background-color: #f5f5f5;
}

.search-result-item:last-child {
  border-bottom: none;
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
}

.selected-party {
  margin: 20px 0;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f5f5f5;
}

.party-details p {
  margin: 5px 0;
}

.party-role-section {
  margin-top: 16px;
}

.client-checkbox {
  display: block;
  margin: 15px 0;
}

.party-option {
  display: flex;
  align-items: center;
}

.party-name {
  font-weight: 500;
}

.party-type-badge {
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
}

.party-type-badge.natural {
  background-color: #2196f3;
  color: white;
}

.party-type-badge.legal {
  background-color: #ff9800;
  color: white;
}

// 编辑表单样式
.edit-form {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
  margin-bottom: 16px;

  .form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    &.full-width {
      width: 100%;
    }

    mat-form-field {
      flex: 1;
    }
  }

  form {
    display: flex;
    flex-direction: column;
  }

  .mat-form-field {
    width: 100%;
  }

  .mat-error {
    font-size: 12px;
    margin-top: 4px;
  }
}

// 响应式布局
@media screen and (max-width: 600px) {
  .form-row {
    flex-direction: column;
    gap: 8px;
  }

  .dialog-content {
    height: auto;
    max-height: 80vh;
  }
} 