import { Component } from '@angular/core';
import { MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule } from '@angular/material/dialog';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-create-folder-dialog',
  templateUrl: './create-folder-dialog.component.html',
  styleUrls: ['./create-folder-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatDialogModule
  ]
})
export class CreateFolderDialogComponent {
  folderForm: FormGroup;

  constructor(
    private dialogRef: MatDialogRef<CreateFolderDialogComponent>,
    private fb: FormBuilder
  ) {
    this.folderForm = this.fb.group({
      folderName: ['', [Validators.required, Validators.maxLength(100)]]
    });
  }

  /**
   * 关闭对话框，取消创建
   */
  onCancel(): void {
    this.dialogRef.close();
  }

  /**
   * 提交表单创建文件夹
   */
  onSubmit(): void {
    if (this.folderForm.valid) {
      this.dialogRef.close(this.folderForm.value.folderName);
    }
  }
} 