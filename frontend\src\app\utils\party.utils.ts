import { PartyType } from '../interfaces/case.interface';

/**
 * 获取当事人类型显示文本
 * @param type 当事人类型
 * @returns 对应的中文显示文本
 */
export function getPartyTypeText(type: string): string {
  // 将输入转换为大写，确保一致性
  const upperType = type ? type.toUpperCase() : '';
  
  const typeMap: { [key: string]: string } = {
    [PartyType.PLAINTIFF]: '原告',
    [PartyType.DEFENDANT]: '被告',
    [PartyType.THIRD_PARTY]: '第三人',
    [PartyType.SUSPECT]: '犯罪嫌疑人',
    [PartyType.SUSPECT_FAMILY]: '嫌疑人家属',
    [PartyType.NON_LITIGATION_CLIENT]: '非诉委托人'
  };
  
  return typeMap[upperType] || upperType;
}

/**
 * 根据案件类型获取当事人类型映射
 * @param caseType 案件类型
 * @returns 当事人类型映射对象
 */
export function getPartyTypeMapByCaseType(caseType: string): { [key: string]: string } {
  switch(caseType) {
    case '刑事案件':
      return {
        [PartyType.PLAINTIFF]: '受害人',
        [PartyType.SUSPECT]: '犯罪嫌疑人',
        [PartyType.SUSPECT_FAMILY]: '嫌疑人家属'
      };
    case '民事案件':
    case '行政案件':
      return {
        [PartyType.PLAINTIFF]: '原告',
        [PartyType.DEFENDANT]: '被告',
        [PartyType.THIRD_PARTY]: '第三人'
      };
    case '其他案件':
    default:
      return {
        [PartyType.PLAINTIFF]: '原告',
        [PartyType.DEFENDANT]: '被告',
        [PartyType.THIRD_PARTY]: '第三人',
        [PartyType.NON_LITIGATION_CLIENT]: '非诉委托人'
      };
  }
}

/**
 * 根据案件类型获取特定当事人类型的显示文本
 * @param partyType 当事人类型
 * @param caseType 案件类型
 * @returns 显示文本
 */
export function getPartyTypeDisplay(partyType: string, caseType: string): string {
  // 确保partyType始终使用大写
  const upperType = partyType ? partyType.toUpperCase() : '';
  const typeMap = getPartyTypeMapByCaseType(caseType);
  return typeMap[upperType] || upperType;
} 