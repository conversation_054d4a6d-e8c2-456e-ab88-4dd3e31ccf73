import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { WithdrawalRequest, WithdrawalApprovalData } from '../../interfaces/withdrawal.interface';

@Component({
  selector: 'app-withdrawal-approval-dialog',
  templateUrl: './withdrawal-approval-dialog.component.html',
  styleUrls: ['./withdrawal-approval-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule
  ]
})
export class WithdrawalApprovalDialogComponent {
  form: FormGroup;

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<WithdrawalApprovalDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { withdrawal: WithdrawalRequest }
  ) {
    this.form = this.fb.group({
      deduction_tax: [0, [Validators.min(0), Validators.max(this.data.withdrawal.amount)]],
      other_deductions: [''],
      remarks: [this.data.withdrawal.remarks || '']
    });
  }

  get finalAmount(): number {
    const deductionTax = this.form.get('deduction_tax')?.value || 0;
    return this.data.withdrawal.amount - deductionTax;
  }

  onSubmit(): void {
    if (this.form.valid) {
      const approvalData: WithdrawalApprovalData = this.form.value;
      this.dialogRef.close(approvalData);
    }
  }

  onCancel(): void {
    this.dialogRef.close(null);
  }
} 