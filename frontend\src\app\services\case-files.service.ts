import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

export interface CaseFile {
  id: number;
  case: number;
  folder: number;
  file: string;
  file_url: string;
  file_type: string;
  file_type_display: string;
  original_filename: string;
  uploaded_by: number;
  uploaded_by_name: string;
  uploaded_at: string;
  order: number;
}

export interface CaseFileFolder {
  id: number;
  case: number;
  name: string;
  parent: number | null;
  path: string;
  created_at: string;
  files: CaseFile[];
  children: CaseFileFolder[];
}

export interface FileMergeRequest {
  file_ids: number[];
  output_filename: string;
  start_page_number?: number;
}

@Injectable({
  providedIn: 'root'
})
export class CaseFilesService {
  private apiUrl = `${environment.apiUrl}/case-files`;

  constructor(private http: HttpClient) { }

  // 文件夹操作
  getFolders(caseId: number): Observable<CaseFileFolder[]> {
    return this.http.get<CaseFileFolder[]>(`${this.apiUrl}/folders/`, {
      params: { case: caseId.toString() }
    });
  }

  getFolder(folderId: number): Observable<CaseFileFolder> {
    return this.http.get<CaseFileFolder>(`${this.apiUrl}/folders/${folderId}/`);
  }

  createFolder(folder: { case: number; name: string; parent?: number | null }): Observable<CaseFileFolder> {
    return this.http.post<CaseFileFolder>(`${this.apiUrl}/folders/`, folder);
  }

  deleteFolder(folderId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/folders/${folderId}/delete_folder/`);
  }

  // 文件操作
  getFiles(folderId: number): Observable<CaseFile[]> {
    return this.http.get<CaseFile[]>(`${this.apiUrl}/folders/${folderId}/list_files/`);
  }

  // 获取案件根目录下的文件（不属于任何文件夹的文件）
  getCaseRootFiles(caseId: number): Observable<CaseFile[]> {
    return this.http.get<CaseFile[]>(`${this.apiUrl}/files/case_root_files/`, {
      params: { case: caseId.toString() }
    });
  }

  uploadFile(file: File, caseId: number, folderId: number | null): Observable<CaseFile | CaseFileFolder> {
    // 创建FormData对象
    const formData = new FormData();
    
    // 确保以正确的格式添加参数
    formData.append('file', file);
    formData.append('case', caseId.toString());
    
    // 只有当folderId存在时才添加到表单中
    if (folderId !== null) {
      formData.append('folder', folderId.toString());
    }

    // 添加日志，帮助调试
    console.log('上传文件参数:', {
      file: file.name,
      fileSize: file.size,
      fileType: file.type,
      case: caseId,
      folder: folderId
    });

    // 设置请求头，让浏览器自动处理Content-Type
    return this.http.post<CaseFile | CaseFileFolder>(`${this.apiUrl}/files/`, formData);
  }

  deleteFile(fileId: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/files/${fileId}/`);
  }

  downloadFile(fileId: number): Observable<Blob> {
    return this.http.get(`${this.apiUrl}/files/${fileId}/download/`, {
      responseType: 'blob'
    });
  }

  updateFileOrder(fileId: number, order: number): Observable<CaseFile> {
    return this.http.put<CaseFile>(`${this.apiUrl}/files/${fileId}/update_order/`, { order });
  }

  // PDF合并
  mergePdf(request: FileMergeRequest): Observable<Blob> {
    return this.http.post(`${this.apiUrl}/files/merge_pdf/`, request, {
      responseType: 'blob'
    });
  }
} 