# Generated by Django 5.0.3 on 2025-06-10 16:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0031_merge_20250604_0257'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='legalentity',
            name='creator',
            field=models.ForeignKey(blank=True, help_text='创建该单位记录的用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_legal_entities', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
        migrations.AddField(
            model_name='naturalperson',
            name='creator',
            field=models.ForeignKey(blank=True, help_text='创建该自然人记录的用户', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_natural_persons', to=settings.AUTH_USER_MODEL, verbose_name='创建人'),
        ),
    ]
