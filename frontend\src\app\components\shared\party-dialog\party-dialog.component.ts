import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, Validators } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { environment } from '../../../../environments/environment';
import { finalize } from 'rxjs/operators';
import { MatSnackBar } from '@angular/material/snack-bar';

export interface PartyDialogData {
  isEdit?: boolean;
  partyData?: any;
  partyType?: string;
}

@Component({
  selector: 'app-party-dialog',
  templateUrl: './party-dialog.component.html',
  styleUrls: ['./party-dialog.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule
  ]
})
export class PartyDialogComponent implements OnInit {
  isEdit: boolean = false;
  partyType: string | null = null;
  partyData: any = null;
  entityId: number | null = null;
  
  // 表单对象声明
  naturalPersonForm: any;
  legalEntityForm: any;

  constructor(
    private http: HttpClient,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<PartyDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: PartyDialogData
  ) {
    this.isEdit = !!data.isEdit;
    
    // 如果是编辑模式，预填写表单
    if (this.isEdit && data.partyData) {
      this.initEditMode(data.partyData);
    } else if (data.partyType) {
      // 如果指定了当事人类型
      this.partyType = data.partyType;
    }
  }
  
  ngOnInit() {
    // 初始化表单
    this.initForms();
  }
  
  // 初始化表单
  private initForms() {
    // 自然人表单
    this.naturalPersonForm = this.fb.group({
      name: ['', Validators.required],
      id_number: [''],
      phone_number: [''],
      remarks: ['']
    });

    // 单位表单
    this.legalEntityForm = this.fb.group({
      name: ['', Validators.required],
      credit_code: [''],
      representative_name: [''],
      representative_id_number: [''],
      representative_phone_number: [''],
      remarks: ['']
    });
  }
  
  // 初始化编辑模式
  private initEditMode(partyData: any) {
    this.partyData = partyData;
    
    if (partyData.natural_person) {
      this.partyType = 'natural';
      this.entityId = partyData.natural_person.id;
    } else if (partyData.legal_entity) {
      this.partyType = 'legal';
      this.entityId = partyData.legal_entity.id;
    }

    // 加载当事人详细信息
    this.loadPartyDetails();
  }

  // 选择当事人类型（新建模式）
  selectPartyType(type: string) {
    this.partyType = type;
  }
  
  // 取消
  cancel() {
    this.dialogRef.close(null);
  }

  // 加载当事人详细信息
  private loadPartyDetails() {
    if (!this.partyData) return;

    if (this.partyData.natural_person) {
      this.http.get(`${environment.apiUrl}/natural-persons/${this.partyData.natural_person.id}/`).subscribe({
        next: (response: any) => {
          this.naturalPersonForm.patchValue({
            name: response.name,
            id_number: response.id_number,
            phone_number: response.phone_number,
            remarks: response.remarks
          });
        },
        error: (error) => {
          console.error('加载自然人信息失败:', error);
          this.snackBar.open('加载自然人信息失败', '关闭', { duration: 3000 });
        }
      });
    } else if (this.partyData.legal_entity) {
      this.http.get(`${environment.apiUrl}/legal-entities/${this.partyData.legal_entity.id}/`).subscribe({
        next: (response: any) => {
          this.legalEntityForm.patchValue({
            name: response.name,
            credit_code: response.credit_code,
            representative_name: response.representative_name,
            representative_id_number: response.representative_id_number,
            representative_phone_number: response.representative_phone_number,
            remarks: response.remarks
          });
        },
        error: (error) => {
          console.error('加载单位信息失败:', error);
          this.snackBar.open('加载单位信息失败', '关闭', { duration: 3000 });
        }
      });
    }
  }

  // 保存当事人信息
  savePartyInfo() {
    // 编辑已有当事人
    if (this.isEdit && this.partyData) {
      if (this.partyData.natural_person) {
        if (!this.naturalPersonForm.get('name')?.value?.trim()) {
          this.snackBar.open('姓名不能为空', '关闭', { duration: 3000 });
          return;
        }
        this.http.patch(
          `${environment.apiUrl}/natural-persons/${this.partyData.natural_person.id}/`,
          this.naturalPersonForm.value
        ).subscribe({
          next: (response) => {
            this.snackBar.open('自然人信息更新成功', '关闭', { duration: 3000 });
            this.dialogRef.close({
              ...this.partyData,
              natural_person: response
            });
          },
          error: (error) => {
            console.error('更新自然人信息失败:', error);
            this.snackBar.open('更新自然人信息失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 5000 });
          }
        });
      } else if (this.partyData.legal_entity) {
        if (!this.legalEntityForm.get('name')?.value?.trim()) {
          this.snackBar.open('单位名称不能为空', '关闭', { duration: 3000 });
          return;
        }
        this.http.patch(
          `${environment.apiUrl}/legal-entities/${this.partyData.legal_entity.id}/`,
          this.legalEntityForm.value
        ).subscribe({
          next: (response) => {
            this.snackBar.open('单位信息更新成功', '关闭', { duration: 3000 });
            this.dialogRef.close({
              ...this.partyData,
              legal_entity: response
            });
          },
          error: (error) => {
            console.error('更新单位信息失败:', error);
            this.snackBar.open('更新单位信息失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 5000 });
          }
        });
      }
      return;
    }
    
    // 创建新当事人
    if (!this.isEdit && this.partyType) {
      if (this.partyType === 'natural') {
        if (this.naturalPersonForm.invalid) {
          this.snackBar.open('请填写必填信息', '关闭', { duration: 3000 });
          return;
        }
        
        this.http.post(`${environment.apiUrl}/natural-persons/`, this.naturalPersonForm.value)
          .subscribe({
            next: (response: any) => {
              this.snackBar.open('自然人信息创建成功', '关闭', { duration: 3000 });
              this.dialogRef.close({
                natural_person: response,
                entityType: 'natural'
              });
            },
            error: (error) => {
              console.error('创建自然人信息失败:', error);
              this.snackBar.open('创建自然人信息失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 5000 });
            }
          });
      } else if (this.partyType === 'legal') {
        if (this.legalEntityForm.invalid) {
          this.snackBar.open('请填写必填信息', '关闭', { duration: 3000 });
          return;
        }
        
        this.http.post(`${environment.apiUrl}/legal-entities/`, this.legalEntityForm.value)
          .subscribe({
            next: (response: any) => {
              this.snackBar.open('单位信息创建成功', '关闭', { duration: 3000 });
              this.dialogRef.close({
                legal_entity: response,
                entityType: 'legal'
              });
            },
            error: (error) => {
              console.error('创建单位信息失败:', error);
              this.snackBar.open('创建单位信息失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 5000 });
            }
          });
      }
    }
  }
} 