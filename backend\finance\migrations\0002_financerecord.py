# Generated by Django 5.0.3 on 2025-05-24 19:48

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='FinanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_date', models.DateField(default=django.utils.timezone.now, help_text='默认为当天，可以修改', verbose_name='费用发生时间')),
                ('account_type', models.CharField(choices=[('case_income', '案件收入'), ('lawyer_withdrawal', '律师提款'), ('lawyer_task_expense', '律师任务开支'), ('other_expense', '其他支出')], max_length=50, verbose_name='账目类型')),
                ('amount', models.DecimalField(decimal_places=2, help_text='入账类型存储为正数，出账类型存储为负数', max_digits=12, verbose_name='数额')),
                ('purpose', models.CharField(max_length=200, verbose_name='用途')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_finance_records', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('lawyer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='finance_records', to=settings.AUTH_USER_MODEL, verbose_name='关联律师')),
            ],
            options={
                'verbose_name': '财务收支记录',
                'verbose_name_plural': '财务收支记录',
                'ordering': ['-transaction_date', '-created_at'],
                'permissions': [('can_manage_finance_records', '财务管理权限')],
            },
        ),
    ]
