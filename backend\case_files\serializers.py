from rest_framework import serializers
from .models import CaseFileFolder, CaseFile

class CaseFileSerializer(serializers.ModelSerializer):
    file_url = serializers.SerializerMethodField()
    uploaded_by_name = serializers.SerializerMethodField()
    file_type_display = serializers.SerializerMethodField()
    
    class Meta:
        model = CaseFile
        fields = [
            'id', 'case', 'folder', 'file', 'file_url', 'file_type', 
            'file_type_display', 'original_filename', 'uploaded_by', 
            'uploaded_by_name', 'uploaded_at', 'order'
        ]
        read_only_fields = ['file_url', 'uploaded_by_name', 'file_type_display']
    
    def get_file_url(self, obj):
        request = self.context.get('request')
        if obj.file and hasattr(obj.file, 'url') and request:
            return request.build_absolute_uri(obj.file.url)
        return None
    
    def get_uploaded_by_name(self, obj):
        if obj.uploaded_by:
            return obj.uploaded_by.get_full_name() or obj.uploaded_by.username
        return None
    
    def get_file_type_display(self, obj):
        return obj.get_file_type_display()

class CaseFileFolderSerializer(serializers.ModelSerializer):
    files = CaseFileSerializer(many=True, read_only=True)
    children = serializers.SerializerMethodField()
    parent = serializers.PrimaryKeyRelatedField(
        queryset=CaseFileFolder.objects.all(),
        required=False,
        allow_null=True
    )
    path = serializers.CharField(
        required=False,
        allow_blank=True,
        default=""
    )
    
    class Meta:
        model = CaseFileFolder
        fields = ['id', 'case', 'name', 'parent', 'path', 'created_at', 'files', 'children']
    
    def get_children(self, obj):
        return CaseFileFolderSerializer(obj.children.all(), many=True, context=self.context).data
        
    def create(self, validated_data):
        """创建文件夹时，如果parent不存在，设置为None，path设置为空字符串"""
        if 'parent' not in validated_data:
            validated_data['parent'] = None
        if 'path' not in validated_data or not validated_data['path']:
            validated_data['path'] = ""
        return super().create(validated_data)

    def validate(self, data):
        """验证数据并自动处理parent和path字段"""
        # 如果前端没有提供parent，设为None
        if 'parent' not in data:
            data['parent'] = None
        
        # path字段在model的save方法中会自动生成，这里设为空字符串
        if 'path' not in data or not data['path']:
            data['path'] = ""
        
        return data

class CaseFileFolderListSerializer(serializers.ModelSerializer):
    """用于列表视图的文件夹序列化器，不包含递归子文件夹"""
    file_count = serializers.SerializerMethodField()
    child_count = serializers.SerializerMethodField()
    
    class Meta:
        model = CaseFileFolder
        fields = ['id', 'case', 'name', 'parent', 'path', 'created_at', 'file_count', 'child_count']
    
    def get_file_count(self, obj):
        return obj.files.count()
    
    def get_child_count(self, obj):
        return obj.children.count()

class FileMergeRequestSerializer(serializers.Serializer):
    """用于文件合并请求的序列化器"""
    file_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text='要合并的文件ID列表，按顺序排列'
    )
    output_filename = serializers.CharField(
        max_length=255,
        help_text='输出的PDF文件名'
    )
    start_page_number = serializers.IntegerField(
        default=1,
        min_value=1,
        help_text='PDF页码起始编号，默认从1开始'
    ) 