import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-create-natural-person-dialog',
  template: `
    <h2 mat-dialog-title>新建自然人</h2>
    <mat-dialog-content class="dialog-content">
      <form [formGroup]="naturalPersonForm" (ngSubmit)="onSubmit()">
        <div class="form-section">
          <h3 class="section-title">自然人基本信息</h3>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>姓名</mat-label>
              <input matInput formControlName="name" required>
              <mat-error *ngIf="naturalPersonForm.get('name')?.hasError('required')">
                姓名不能为空
              </mat-error>
            </mat-form-field>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline">
              <mat-label>身份证号码</mat-label>
              <input matInput formControlName="id_number">
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>手机号码</mat-label>
              <input matInput formControlName="phone_number">
            </mat-form-field>
          </div>
          
          <div class="form-row">
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>备注</mat-label>
              <textarea matInput formControlName="remarks" rows="3"></textarea>
            </mat-form-field>
          </div>
        </div>
      </form>
    </mat-dialog-content>

    <mat-dialog-actions align="end" class="dialog-actions">
      <button mat-button [mat-dialog-close]="null" class="cancel-button">
        <mat-icon>close</mat-icon>
        取消
      </button>
      <button mat-raised-button 
              color="primary" 
              class="save-button"
              [disabled]="naturalPersonForm.invalid"
              (click)="onSubmit()">
        <mat-icon>save</mat-icon>
        创建
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    :host {
      --primary-color: #1976d2;
      --primary-light: #e3f2fd;
      --text-primary: #212121;
      --text-secondary: #757575;
      --border-color: #e0e0e0;
      --success-color: #4caf50;
      --warning-color: #ff9800;
    }

    .dialog-content {
      padding: 24px;
      min-width: 520px;
      max-width: 600px;
      background: #fafafa;
      border-radius: 8px;
    }
    
    .form-section {
      background: white;
      border-radius: 12px;
      padding: 24px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--border-color);
      transition: box-shadow 0.3s ease;
    }
    
    .form-section:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
    
    .section-title {
      color: var(--primary-color);
      margin: 0 0 20px 0;
      padding: 0 0 12px 0;
      font-size: 18px;
      font-weight: 500;
      border-bottom: 2px solid var(--primary-light);
      position: relative;
    }
    
    .section-title::before {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 60px;
      height: 2px;
      background: var(--primary-color);
      border-radius: 1px;
    }
    
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      align-items: flex-start;
    }
    
    .form-row:last-child {
      margin-bottom: 0;
    }
    
    .form-row mat-form-field {
      flex: 1;
    }
    
    .full-width {
      width: 100%;
    }
    
    ::ng-deep .mat-mdc-form-field {
      margin-bottom: 4px;
    }
    
    ::ng-deep .mat-mdc-form-field .mat-mdc-form-field-focus-overlay {
      background-color: var(--primary-light);
    }
    
    ::ng-deep .mat-mdc-form-field.mat-focused .mat-mdc-form-field-outline-thick {
      color: var(--primary-color);
    }
    
    ::ng-deep .mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing {
      border-color: var(--border-color);
      border-width: 1px;
    }
    
    ::ng-deep .mat-mdc-form-field .mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,
    ::ng-deep .mat-mdc-form-field .mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing {
      border-color: var(--primary-color);
      border-width: 2px;
    }
    
    .dialog-actions {
      padding: 20px 24px;
      margin: 0 -24px -24px -24px;
      background: white;
      border-top: 1px solid var(--border-color);
      border-radius: 0 0 8px 8px;
      display: flex;
      gap: 12px;
      justify-content: flex-end;
    }
    
    .cancel-button {
      min-width: 100px;
      height: 40px;
      border-radius: 20px;
      font-weight: 500;
      text-transform: none;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);
      transition: all 0.3s ease;
    }
    
    .cancel-button:hover {
      background-color: #f5f5f5;
      border-color: var(--text-secondary);
    }
    
    .save-button {
      min-width: 120px;
      height: 40px;
      border-radius: 20px;
      font-weight: 500;
      text-transform: none;
      background: linear-gradient(45deg, var(--primary-color), #1565c0);
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
      transition: all 0.3s ease;
    }
    
    .save-button:hover:not(:disabled) {
      background: linear-gradient(45deg, #1565c0, var(--primary-color));
      box-shadow: 0 4px 12px rgba(25, 118, 210, 0.4);
      transform: translateY(-1px);
    }
    
    .save-button:disabled {
      background: #cccccc;
      box-shadow: none;
      color: #888888;
    }
    
    .cancel-button,
    .save-button {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .cancel-button mat-icon,
    .save-button mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
    
    ::ng-deep .mat-mdc-dialog-title {
      color: var(--text-primary);
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      padding: 24px 24px 0 24px;
      border-bottom: none;
    }
    
    ::ng-deep .mat-mdc-dialog-content {
      padding: 0;
      margin: 0;
      max-height: 70vh;
      overflow-y: auto;
    }
    
    ::ng-deep .mat-mdc-dialog-actions {
      padding: 0;
      margin: 0;
    }
    
    /* 滚动条样式 */
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar {
      width: 6px;
    }
    
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }
    
    ::ng-deep .mat-mdc-dialog-content::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    /* 响应式设计 */
    @media (max-width: 600px) {
      .dialog-content {
        min-width: auto;
        max-width: 100%;
        padding: 16px;
      }
      
      .form-section {
        padding: 16px;
      }
      
      .form-row {
        flex-direction: column;
        gap: 12px;
      }
      
      .dialog-actions {
        padding: 16px;
        margin: 0 -16px -16px -16px;
      }
    }
  `],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSnackBarModule
  ]
})
export class CreateNaturalPersonDialogComponent implements OnInit {
  naturalPersonForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private http: HttpClient,
    private snackBar: MatSnackBar,
    private dialogRef: MatDialogRef<CreateNaturalPersonDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.naturalPersonForm = this.fb.group({
      name: ['', Validators.required],
      id_number: [''],
      phone_number: [''],
      remarks: ['']
    });
  }

  ngOnInit(): void {}

  onSubmit(): void {
    if (this.naturalPersonForm.invalid) {
      this.snackBar.open('请填写必填信息', '关闭', { duration: 3000 });
      return;
    }

    const formData = this.naturalPersonForm.value;

    this.http.post(`${environment.apiUrl}/natural-persons/`, formData)
      .subscribe({
        next: (response: any) => {
          this.snackBar.open('自然人创建成功', '关闭', { duration: 3000 });
          this.dialogRef.close(response);
        },
        error: (error) => {
          console.error('创建自然人失败:', error);
          this.snackBar.open('创建自然人失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 5000 });
        }
      });
  }
} 