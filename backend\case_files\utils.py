import mimetypes
import os
import shutil
import subprocess
import tempfile
import urllib.parse
import zipfile
from pathlib import Path

from django.conf import settings
from django.core.files.uploadedfile import SimpleUploadedFile
from django.http import HttpResponse
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.utils import ImageReader
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfgen import canvas
from reportlab.platypus import (Image, PageBreak, Paragraph, SimpleDocTemplate,
                                Spacer)

try:
    # 尝试导入新版本PyPDF2
    from PyPDF2 import PdfReader, PdfWriter
except ImportError:
    # 如果失败，尝试导入旧版本
    try:
        from PyPDF2 import PdfFileReader as PdfReader
        from PyPDF2 import PdfFileWriter as PdfWriter
    except ImportError:
        # 如果还是失败，尝试导入pyPdf
        from pyPdf import PdfFileReader as PdfReader, PdfFileWriter as PdfWriter

from io import BytesIO

import docx2pdf
import img2pdf
from openpyxl import load_workbook
from PIL import Image as PILImage

# 是否禁用目录功能
DISABLE_TOC = True

# 检查是否已安装poppler
def is_poppler_installed():
    """检查是否安装了Poppler工具"""
    try:
        # 尝试执行pdftoppm命令（Poppler的一部分）
        result = subprocess.run(
            ["pdftoppm", "-v"], capture_output=True, text=True, check=False
        )
        return result.returncode == 0
    except FileNotFoundError:
        return False


# 获取poppler安装状态
POPPLER_INSTALLED = is_poppler_installed()


# 注册中文字体
def register_fonts():
    """注册中文字体"""
    try:
        font_path = os.path.join(settings.BASE_DIR, "fonts", "simsun.ttf")
        # 检查字体文件是否存在
        if os.path.exists(font_path):
            pdfmetrics.registerFont(TTFont("SimSun", font_path))
            return True
        else:
            # 尝试在系统其他位置查找中文字体
            system_font_paths = [
                "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",  # Ubuntu/Debian
                "/usr/share/fonts/wqy-microhei/wqy-microhei.ttc",  # CentOS/Fedora
                "/usr/share/fonts/wenquanyi/wqy-microhei/wqy-microhei.ttc",  # 其他Linux
                "/usr/share/fonts/chinese/TrueType/simsun.ttc",  # 其他可能位置
                "/System/Library/Fonts/STHeiti Light.ttc",  # macOS
                "C:/Windows/Fonts/simsun.ttc",  # Windows
                "C:/Windows/Fonts/simhei.ttf",  # Windows
            ]

            # 尝试查找并加载系统中文字体
            for sys_font_path in system_font_paths:
                if os.path.exists(sys_font_path):
                    try:
                        if sys_font_path.lower().endswith(".ttc"):
                            # 对于.ttc文件，使用另一种方法注册
                            from reportlab.pdfbase.ttfonts import TTFontFile

                            font_name = os.path.basename(sys_font_path).split(".")[0]
                            pdfmetrics.registerFont(
                                TTFont(font_name, sys_font_path, subfontIndex=0)
                            )
                        else:
                            font_name = os.path.basename(sys_font_path).split(".")[0]
                            pdfmetrics.registerFont(TTFont(font_name, sys_font_path))
                        print(f"已加载系统中文字体: {sys_font_path}")
                        return True
                    except Exception as font_err:
                        print(f"加载系统字体失败 {sys_font_path}: {str(font_err)}")
                        continue

            # 如果SimSun字体不存在，使用系统默认字体
            # 先注册Helvetica字体，这是PDF默认支持的
            pdfmetrics.getFont("Helvetica")
            return False
    except Exception as e:
        # 记录异常但不中断程序
        print(f"注册字体失败: {str(e)}")
        return False


# 获取中文字体名
def get_chinese_font_name():
    """获取已注册的中文字体名"""
    has_chinese_font = register_fonts()
    # 按优先级尝试不同的中文字体
    font_candidates = ["SimSun", "wqy-microhei", "STHeiti Light", "simhei"]
    for font in font_candidates:
        try:
            pdfmetrics.getFont(font)
            return font
        except Exception:
            continue
    return "Helvetica"  # 如果没有中文字体，返回默认字体


def get_file_type(filename):
    """根据文件扩展名获取文件类型"""
    ext = filename.split(".")[-1].lower()
    file_type_mapping = {
        "doc": "doc",
        "docx": "docx",
        "xls": "xls",
        "xlsx": "xlsx",
        "pdf": "pdf",
        "jpg": "jpg",
        "jpeg": "jpg",
        "png": "png",
        "zip": "zip",
    }
    return file_type_mapping.get(ext, "other")


def extract_zip(zip_file, case, user):
    """
    提取ZIP文件并创建文件夹和文件
    
    参数:
        zip_file: 可以是Django的UploadedFile对象或文件路径
        case: 案件对象
        user: 用户对象
        
    返回:
        CaseFileFolder或None: 如果创建了根文件夹则返回该对象，否则返回None
    """
    import os

    from .models import CaseFile, CaseFileFolder
    
    print(f"开始解压ZIP文件，参数类型: {type(zip_file)}")
    if hasattr(zip_file, 'path'):
        print(f"文件路径: {zip_file.path}")
    if hasattr(zip_file, 'name'):
        print(f"文件名: {zip_file.name}")
    if hasattr(zip_file, 'url'):
        print(f"文件URL: {zip_file.url}")

    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"创建临时目录: {temp_dir}")
        # 解压ZIP文件到临时目录
        # 检查zip_file是否是文件路径或者Django文件对象
        if isinstance(zip_file, str) and os.path.exists(zip_file):
            # 如果是文件路径
            zip_path = zip_file
            zip_name = os.path.basename(zip_file)
            print(f"使用文件路径: {zip_path}")
            # 使用文件路径
            with zipfile.ZipFile(zip_path, "r") as zip_ref:
                zip_ref.extractall(temp_dir)
                print(f"成功解压ZIP文件到临时目录")
        else:
            # 如果是Django文件对象，需要查找对应的文件记录获取实际文件路径
            zip_name = getattr(zip_file, 'name', 'unknown.zip')
            print(f"处理Django文件对象，文件名: {zip_name}")
            
            # 尝试直接使用path属性
            if hasattr(zip_file, 'path') and os.path.exists(zip_file.path):
                zip_path = zip_file.path
                print(f"直接使用文件路径: {zip_path}")
                with zipfile.ZipFile(zip_path, "r") as zip_ref:
                    zip_ref.extractall(temp_dir)
                    print(f"成功解压ZIP文件到临时目录")
            else:
                # 尝试查找最近创建的匹配此文件名的CaseFile记录
                try:
                    from django.db.models import Q

                    # 查找匹配原始文件名或名称末尾的文件记录
                    base_name = os.path.basename(zip_name)
                    print(f"搜索文件记录，基本文件名: {base_name}")
                    zip_file_obj = CaseFile.objects.filter(
                        Q(original_filename=zip_name) | 
                        Q(original_filename=base_name) |
                        Q(file__endswith=base_name)
                    ).order_by('-uploaded_at').first()
                    
                    if zip_file_obj and hasattr(zip_file_obj.file, 'path'):
                        # 使用找到的文件记录的路径
                        zip_path = zip_file_obj.file.path
                        print(f"找到ZIP文件路径: {zip_path}")
                        if os.path.exists(zip_path):
                            print(f"文件存在，开始解压")
                            with zipfile.ZipFile(zip_path, "r") as zip_ref:
                                zip_ref.extractall(temp_dir)
                                print(f"成功解压ZIP文件到临时目录")
                        else:
                            print(f"文件不存在: {zip_path}")
                            raise FileNotFoundError(f"找到的文件路径不存在: {zip_path}")
                    else:
                        # 如果找不到对应的文件记录，尝试直接使用文件对象
                        print(f"未找到ZIP文件记录，尝试直接使用文件对象")
                        if hasattr(zip_file, 'temporary_file_path') and callable(getattr(zip_file, 'temporary_file_path')):
                            # 如果有临时文件路径方法（TemporaryUploadedFile）
                            temp_file_path = zip_file.temporary_file_path()
                            print(f"使用临时文件路径: {temp_file_path}")
                            if os.path.exists(temp_file_path):
                                with zipfile.ZipFile(temp_file_path, "r") as zip_ref:
                                    zip_ref.extractall(temp_dir)
                                    print(f"成功解压ZIP文件到临时目录")
                            else:
                                print(f"临时文件不存在: {temp_file_path}")
                                raise FileNotFoundError(f"临时文件不存在: {temp_file_path}")
                        else:
                            # 如果是内存中的文件（InMemoryUploadedFile），写入临时文件再解压
                            print(f"使用内存文件对象，写入临时文件")
                            temp_zip = os.path.join(temp_dir, "temp.zip")
                            try:
                                with open(temp_zip, 'wb') as f:
                                    if hasattr(zip_file, 'read') and callable(zip_file.read):
                                        # 如果有read方法，读取文件内容
                                        content = zip_file.read()
                                        print(f"读取文件内容，大小: {len(content)} 字节")
                                        f.write(content)
                                        print(f"成功写入临时文件: {temp_zip}")
                                    else:
                                        # 最后尝试直接打开
                                        print(f"尝试直接使用zipfile打开")
                                        try:
                                            with zipfile.ZipFile(zip_file, "r") as zip_ref:
                                                file_list = zip_ref.namelist()
                                                print(f"ZIP文件内容列表: {file_list}")
                                                zip_ref.extractall(temp_dir)
                                                print(f"成功解压ZIP文件到临时目录")
                                            return process_extracted_dir(temp_dir, zip_name, case, user)
                                        except Exception as direct_err:
                                            print(f"直接打开失败: {str(direct_err)}")
                                            raise
                                
                                # 解压临时文件
                                if os.path.exists(temp_zip) and os.path.getsize(temp_zip) > 0:
                                    print(f"临时ZIP文件大小: {os.path.getsize(temp_zip)} 字节")
                                    with zipfile.ZipFile(temp_zip, "r") as zip_ref:
                                        file_list = zip_ref.namelist()
                                        print(f"ZIP文件内容列表: {file_list}")
                                        zip_ref.extractall(temp_dir)
                                        print(f"成功解压临时ZIP文件到临时目录")
                                else:
                                    print(f"临时ZIP文件无效: {temp_zip}")
                                    raise FileNotFoundError(f"创建的临时ZIP文件无效")
                            except Exception as temp_err:
                                print(f"处理临时文件时出错: {str(temp_err)}")
                                # 列出临时目录内容
                                if os.path.exists(temp_dir):
                                    print(f"临时目录内容: {os.listdir(temp_dir)}")
                                raise
                except Exception as e:
                    print(f"处理ZIP文件时出错: {str(e)}")
                    raise

        # 处理完成的临时目录
        print(f"处理已解压的目录: {temp_dir}")
        if os.path.exists(temp_dir):
            print(f"临时目录内容: {os.listdir(temp_dir)}")
        
        # 处理解压后的目录，此函数可能返回None，表示内容直接放到根目录
        return process_extracted_dir(temp_dir, zip_name, case, user)


def process_extracted_dir(temp_dir, zip_name, case, user):
    """
    处理已解压的目录并创建对应的文件夹和文件
    
    参数:
        temp_dir: 解压缩的临时目录
        zip_name: ZIP文件名
        case: 案件对象
        user: 用户对象
        
    返回:
        CaseFileFolder: 创建的根文件夹对象，或None如果不创建根文件夹
    """
    from .models import CaseFileFolder
    
    print(f"处理提取的目录: {temp_dir}, ZIP文件名: {zip_name}")
    
    try:
        # 检查解压后的文件结构
        extracted_path = Path(temp_dir)
        if not extracted_path.exists():
            print(f"提取的目录不存在: {temp_dir}")
            raise FileNotFoundError(f"提取的目录不存在: {temp_dir}")
            
        root_contents = list(extracted_path.iterdir())
        print(f"根目录内容项数: {len(root_contents)}")
        
        for item in root_contents:
            print(f"  - {'目录' if item.is_dir() else '文件'}: {item.name}")
    
        # 直接处理根目录下的所有文件和文件夹，不创建额外的父文件夹
        # 这将把所有内容直接放到案件的根目录
        print(f"将内容直接放入案件根目录")
        
        file_count = 0
        for item in root_contents:
            if item.is_dir():
                # 为每个目录创建一个根级文件夹
                print(f"创建根级文件夹: {item.name}")
                folder = CaseFileFolder.objects.create(
                    case=case, name=item.name, parent=None
                )
                print(f"根级文件夹创建成功，ID: {folder.id}")
                
                # 递归处理文件夹内容
                file_count += process_directory(item, folder, case, user, extracted_path)
            elif item.is_file():
                # 处理根级文件（不属于任何文件夹）
                print(f"处理根级文件: {item.name}")
                file_type = get_file_type(item.name)
                
                try:
                    # 读取文件内容
                    with open(item, "rb") as f:
                        file_content = f.read()
                    
                    print(f"文件大小: {len(file_content)} 字节")
                    
                    # 创建Django文件对象
                    content_type = mimetypes.guess_type(item.name)[0] or 'application/octet-stream'
                    django_file = SimpleUploadedFile(
                        name=item.name,
                        content=file_content,
                        content_type=content_type,
                    )
                    
                    # 创建文件记录 - 没有父文件夹(folder=None)
                    file_obj = CaseFile.objects.create(
                        case=case,
                        folder=None,  # 放在根目录
                        file=django_file,
                        file_type=file_type,
                        original_filename=item.name,
                        uploaded_by=user,
                    )
                    
                    print(f"根级文件记录创建成功，ID: {file_obj.id}")
                    file_count += 1
                except Exception as file_err:
                    print(f"处理文件 {item.name} 时出错: {str(file_err)}")
                    
        print(f"处理完成，共创建 {file_count} 个文件")
        return None  # 不返回任何根文件夹，因为内容已经直接放入案件根目录
    except Exception as e:
        print(f"处理解压目录时出错: {str(e)}")
        raise


def process_directory(directory_path, parent_folder, case, user, root_path):
    """
    递归处理目录，创建文件夹和文件
    
    参数:
        directory_path: 当前处理的目录路径
        parent_folder: 父文件夹对象
        case: 案件对象
        user: 用户对象
        root_path: 解压缩的根路径
        
    返回:
        int: 创建的文件数量
    """
    from .models import CaseFile, CaseFileFolder
    
    file_count = 0
    print(f"处理目录: {directory_path}")
    
    try:
        for item in directory_path.iterdir():
            if item.is_dir():
                # 创建子文件夹
                print(f"创建子文件夹: {item.name}")
                subfolder = CaseFileFolder.objects.create(
                    case=case, name=item.name, parent=parent_folder
                )
                print(f"子文件夹创建成功，ID: {subfolder.id}")
                
                # 递归处理子文件夹
                subfiles = process_directory(item, subfolder, case, user, root_path)
                file_count += subfiles
                print(f"子文件夹 {item.name} 包含 {subfiles} 个文件")
            elif item.is_file():
                # 处理文件
                print(f"处理文件: {item.name}")
                file_type = get_file_type(item.name)
                
                try:
                    # 读取文件内容
                    with open(item, "rb") as f:
                        file_content = f.read()
                    
                    print(f"文件大小: {len(file_content)} 字节")
                    
                    # 创建Django文件对象
                    content_type = mimetypes.guess_type(item.name)[0] or 'application/octet-stream'
                    django_file = SimpleUploadedFile(
                        name=item.name,
                        content=file_content,
                        content_type=content_type,
                    )
                    
                    # 创建文件记录
                    file_obj = CaseFile.objects.create(
                        case=case,
                        folder=parent_folder,
                        file=django_file,
                        file_type=file_type,
                        original_filename=item.name,
                        uploaded_by=user,
                    )
                    
                    print(f"文件记录创建成功，ID: {file_obj.id}")
                    file_count += 1
                except Exception as file_err:
                    print(f"处理文件 {item.name} 时出错: {str(file_err)}")
                    # 继续处理其他文件
        
        return file_count
    except Exception as e:
        print(f"处理目录时出错: {str(e)}")
        # 返回已创建的文件数量
        return file_count


def convert_to_pdf(file_obj):
    """将文件转换为PDF"""
    file_path = file_obj.file.path
    file_type = file_obj.file_type
    temp_pdf_path = None

    print(f"开始转换文件: {file_obj.original_filename}, 类型: {file_type}")

    with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as tmp:
        temp_pdf_path = tmp.name

    try:
        if file_type in ["doc", "docx"]:
            # 使用docx2pdf转换Word文档
            print(f"转换Word文档: {file_path} -> {temp_pdf_path}")
            # docx2pdf.convert(file_path, temp_pdf_path)
            convert_with_libreoffice(file_path, temp_pdf_path)
        elif file_type in ["xls", "xlsx"]:
            # 使用openpyxl和reportlab转换Excel
            print(f"转换Excel文档: {file_path}")
            wb = load_workbook(filename=file_path)
            pdf = SimpleDocTemplate(temp_pdf_path, pagesize=A4)
            elements = []
            styles = getSampleStyleSheet()

            # 设置中文字体
            font_name = get_chinese_font_name()

            # 创建样式
            heading_style = styles["Heading1"]
            heading_style.fontName = font_name

            # 创建专用中文样式
            chinese_style = ParagraphStyle(
                "ChineseStyle",
                parent=styles["Normal"],
                fontName=font_name,
                fontSize=10,
                leading=12,
                alignment=TA_LEFT,
            )

            for sheet_name in wb.sheetnames:
                sheet = wb[sheet_name]
                elements.append(Paragraph(f"表格: {sheet_name}", heading_style))
                elements.append(Spacer(1, 12))

                # 简单的表格导出 - 更复杂的实现可以使用Table
                for row in sheet.rows:
                    row_values = []
                    for cell in row:
                        if cell.value is not None:
                            row_values.append(str(cell.value))
                    if row_values:
                        elements.append(
                            Paragraph(" | ".join(row_values), chinese_style)
                        )

                elements.append(PageBreak())

            # 确保至少有一个元素
            if not elements:
                elements.append(Paragraph("Excel文件为空", heading_style))

            # 如果最后一个元素是PageBreak，移除它
            if isinstance(elements[-1], PageBreak):
                elements.pop()

            pdf.build(elements)
        elif file_type in ["jpg", "jpeg", "png"]:
            # 使用img2pdf转换图片
            print(f"转换图片: {file_path}")
            try:
                with open(temp_pdf_path, "wb") as f:
                    f.write(img2pdf.convert(file_path))
            except Exception as img_error:
                print(f"使用img2pdf转换图片失败: {str(img_error)}，尝试使用PIL")
                # 如果img2pdf失败，尝试使用PIL
                img = PILImage.open(file_path)
                pdf_buffer = BytesIO()
                if img.mode != "RGB":
                    img = img.convert("RGB")
                img.save(pdf_buffer, format="PDF")
                pdf_buffer.seek(0)

                with open(temp_pdf_path, "wb") as f:
                    f.write(pdf_buffer.getvalue())
        elif file_type == "pdf":
            # 已经是PDF，只需复制
            print(f"复制PDF: {file_path} -> {temp_pdf_path}")
            import shutil

            shutil.copy(file_path, temp_pdf_path)
        else:
            # 其他类型的文件，创建一个只有文件名的PDF
            print(f"未知文件类型: {file_type}，创建简单PDF")
            pdf_buffer = BytesIO()
            c = canvas.Canvas(pdf_buffer, pagesize=A4)
            font_name = get_chinese_font_name()
            c.setFont(font_name, 16)
            c.drawString(50, 800, f"文件名: {file_obj.original_filename}")
            c.drawString(50, 770, f"文件类型: {file_type} (不支持直接预览)")
            c.save()
            pdf_buffer.seek(0)

            with open(temp_pdf_path, "wb") as f:
                f.write(pdf_buffer.getvalue())

        # 检查转换后的PDF是否有效
        try:
            # 使用PyPDF2验证
            pdf_reader = PdfReader(temp_pdf_path)
            page_count = len(pdf_reader.pages)
            print(f"成功转换PDF，页数: {page_count}")

            if page_count == 0:
                raise Exception("转换后的PDF没有页面")

            # 额外验证：检查文件大小
            file_size = os.path.getsize(temp_pdf_path)
            if file_size < 100:  # 文件太小，可能是无效的
                raise Exception(f"PDF文件太小 ({file_size} 字节)")

            return temp_pdf_path
        except Exception as e:
            print(f"PDF检查失败: {str(e)}")
            # 如果转换后的PDF无效，创建一个包含错误信息的PDF
            pdf_buffer = BytesIO()
            c = canvas.Canvas(pdf_buffer, pagesize=A4)
            font_name = get_chinese_font_name()
            c.setFont(font_name, 16)
            c.drawString(50, 800, f"文件名: {file_obj.original_filename}")
            c.drawString(50, 770, f"转换失败: {str(e)}")
            c.save()
            pdf_buffer.seek(0)

            # 覆盖原转换文件
            with open(temp_pdf_path, "wb") as f:
                f.write(pdf_buffer.getvalue())

            return temp_pdf_path
    except Exception as e:
        print(f"转换发生错误: {str(e)}")
        # 创建包含错误信息的PDF
        try:
            pdf_buffer = BytesIO()
            c = canvas.Canvas(pdf_buffer, pagesize=A4)
            font_name = get_chinese_font_name()
            c.setFont(font_name, 16)
            c.drawString(50, 800, f"文件名: {file_obj.original_filename}")
            c.drawString(50, 770, f"错误信息: {str(e)}")
            c.save()
            pdf_buffer.seek(0)

            with open(temp_pdf_path, "wb") as f:
                f.write(pdf_buffer.getvalue())

            return temp_pdf_path
        except Exception as inner_e:
            print(f"创建错误页面失败: {str(inner_e)}")
            if temp_pdf_path and os.path.exists(temp_pdf_path):
                os.unlink(temp_pdf_path)
            raise e


def merge_pdfs(pdf_file_list, output_filename, start_page_number=1):
    """合并多个PDF文件"""
    print(f"开始合并PDF文件，共 {len(pdf_file_list)} 个文件，起始页码: {start_page_number}")

    # 添加对空列表的检查
    if not pdf_file_list:
        print("文件列表为空，返回空白文档")
        # 处理空列表情况，创建一个只有标题的PDF
        buffer = BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        font_name = get_chinese_font_name()
        c.setFont(font_name, 16)
        c.drawString(50, 800, "空白文档 - 没有文件可合并")
        c.save()

        buffer.seek(0)

        # 创建HTTP响应
        response = HttpResponse(buffer.getvalue(), content_type="application/pdf")
        # 使用RFC 5987编码支持中文文件名
        filename_utf8 = urllib.parse.quote(output_filename)
        response["Content-Disposition"] = (
            f"attachment; filename=\"{filename_utf8}.pdf\"; filename*=UTF-8''{filename_utf8}.pdf"
        )

        return response

    # 创建 reportlab 文档
    buffer = BytesIO()

    # 设置页面边距
    page_width, page_height = A4
    left_margin = 50
    right_margin = 50
    top_margin = 50
    bottom_margin = 50

    # 创建带有自定义页面边距的文档
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        leftMargin=left_margin,
        rightMargin=right_margin,
        topMargin=top_margin,
        bottomMargin=bottom_margin,
        title=f"合并文档: {output_filename}",  # 添加标题
    )

    # 设置字体
    font_name = get_chinese_font_name()

    # 创建样式
    styles = getSampleStyleSheet()
    title_style = styles["Heading1"]
    title_style.alignment = TA_CENTER
    title_style.fontName = font_name  # 使用中文字体

    normal_style = styles["Normal"]
    normal_style.fontName = font_name  # 使用中文字体

    # 创建一个专用于中文内容的段落样式
    chinese_style = ParagraphStyle(
        "ChineseStyle",
        parent=normal_style,
        fontName=font_name,
        fontSize=12,
        leading=14,
        alignment=TA_LEFT,
    )
    
    # 创建目录项样式
    toc_style = ParagraphStyle(
        "TOCStyle",
        parent=chinese_style,
        fontName=font_name,
        fontSize=12,
        leading=18,  # 增加行间距
        alignment=TA_LEFT,
    )
    
    # 页脚样式（页码）
    footer_style = ParagraphStyle(
        "FooterStyle",
        parent=normal_style,
        fontName=font_name,
        fontSize=10,
        leading=12,
        alignment=TA_CENTER,
    )

    # 创建元素列表
    elements = []
    
    # 临时文件列表，用于清理
    temp_files = []
    has_valid_content = False
    
    # 禁用目录功能时不添加目录
    if not DISABLE_TOC:
        # 添加目录
        elements.append(Paragraph("目录", title_style))
        elements.append(Spacer(1, 20))
        
        # 记录每个文件的起始页码
        file_start_pages = {}
        # 目录后的起始页码，初始值为2（目录为第1页）
        current_page = 2

        try:
            # 目录内容
            toc_items = []
            for i, pdf_file in enumerate(pdf_file_list):
                try:
                    title = pdf_file.original_filename
                    print(f"处理文件 {i+1}/{len(pdf_file_list)}: {title}")

                    # 添加到目录，记录对应的起始页码
                    file_start_pages[title] = current_page
                    current_page += 1  # 每个文件至少占用一页
                    
                    # 尝试预先估计文件页数
                    try:
                        temp_pdf_path = convert_to_pdf(pdf_file)
                        temp_files.append(temp_pdf_path)
                        pdf_reader = PdfReader(temp_pdf_path)
                        page_count = len(pdf_reader.pages)
                        if page_count > 1:
                            current_page += page_count - 1  # 减1是因为已经计算了第一页
                    except Exception as e:
                        print(f"预估文件页数时出错: {str(e)}")
                        # 出错时按一页计算
                        pass
                    
                    # 添加目录项，包括页码
                    toc_items.append((title, current_page - 1))
                    # 去除文件后缀名
                    title_without_ext = os.path.splitext(title)[0]
                    # 创建带有点线的目录项
                    title_text = f"{i+1}. {title_without_ext}"
                    page_num = str(file_start_pages[title])
                    dot_count = max(3, 70 - len(title_text) - len(page_num))  # 根据文本长度动态调整点的数量
                    dots = "." * dot_count
                    toc_line = f"{title_text} {dots} {page_num}"
                    elements.append(Paragraph(toc_line, toc_style))
                except Exception as e:
                    print(f"添加目录项时出错: {str(e)}")
                    # 为失败的文件也添加页码
                    file_start_pages[pdf_file.original_filename] = current_page
                    current_page += 1  # 失败的文件按一页计算
                    # 去除文件后缀名
                    title_without_ext = os.path.splitext(pdf_file.original_filename)[0]
                    title_text = f"{i+1}. {title_without_ext} (处理失败)"
                    page_num = str(file_start_pages[pdf_file.original_filename])
                    dot_count = max(3, 70 - len(title_text) - len(page_num))
                    dots = "." * dot_count
                    toc_line = f"{title_text} {dots} {page_num}"
                    elements.append(Paragraph(toc_line, toc_style))

            # 添加页面分隔符，从目录转到内容页面
            elements.append(PageBreak())

            # 清理之前的临时文件，因为已经用于预估页数
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except Exception:
                        pass
            temp_files = []
        except Exception as e:
            print(f"处理目录时出错: {str(e)}")
            # 清理临时文件
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    try:
                        os.unlink(temp_file)
                    except Exception:
                        pass
            temp_files = []
            
        # 重置临时文件列表
        temp_files = []

    # 添加实际内容
    for i, pdf_file in enumerate(pdf_file_list):
        try:
            title = pdf_file.original_filename
            print(f"处理文件内容 {i+1}/{len(pdf_file_list)}: {title}")

            # 获取转换后的 PDF 路径
            temp_pdf_path = convert_to_pdf(pdf_file)
            temp_files.append(temp_pdf_path)

            # 检查转换后的 PDF 是否有效
            try:
                if POPPLER_INSTALLED:
                    # 如果已安装poppler，使用pdf2image处理
                    from pdf2image import convert_from_path

                    # 尝试将 PDF 页面转换为图像，添加超时设置
                    try:
                        # 使用线程超时替代信号
                        import threading
                        import time

                        class ConversionTimeoutError(Exception):
                            pass

                        class PDFConversionThread(threading.Thread):
                            def __init__(self, pdf_path, dpi):
                                threading.Thread.__init__(self)
                                self.pdf_path = pdf_path
                                self.dpi = dpi
                                self.images = None
                                self.exception = None

                            def run(self):
                                try:
                                    self.images = convert_from_path(
                                        self.pdf_path, dpi=self.dpi
                                    )
                                except Exception as e:
                                    self.exception = e

                        # 开始转换线程
                        print(f"开始转换PDF为图像: {temp_pdf_path}")
                        conversion_thread = PDFConversionThread(temp_pdf_path, 150)
                        conversion_thread.start()

                        # 等待转换完成，设置30秒超时
                        timeout = 30
                        conversion_thread.join(timeout)

                        # 检查线程是否仍在运行（超时）
                        if conversion_thread.is_alive():
                            raise ConversionTimeoutError("PDF转换超时")

                        # 检查线程是否发生异常
                        if conversion_thread.exception:
                            raise conversion_thread.exception

                        # 获取结果
                        images = conversion_thread.images

                        if images:
                            has_valid_content = True
                            for j, img in enumerate(images):
                                # 将图像保存到临时文件
                                img_temp = tempfile.NamedTemporaryFile(
                                    suffix=".png", delete=False
                                )
                                img_temp_path = img_temp.name
                                temp_files.append(img_temp_path)
                                img.save(img_temp_path, "PNG")

                                # 将图像添加到 PDF
                                img_reader = ImageReader(img_temp_path)
                                img_width, img_height = img.size

                                # 计算适合 A4 页面的图像大小
                                max_width = A4[0] - 120  # 页面宽度减去更大的边距
                                max_height = A4[1] - 180  # 页面高度减去更大的边距

                                # 为确保安全，使用更保守的尺寸
                                safe_max_width = max_width * 0.95
                                safe_max_height = max_height * 0.95

                                # 调整大小以适应页面
                                width = img_width
                                height = img_height

                                # 计算需要的缩放比例
                                if width > 0 and height > 0:  # 避免除零错误
                                    ratio_w = safe_max_width / width
                                    ratio_h = safe_max_height / height
                                    ratio = min(
                                        ratio_w, ratio_h
                                    )  # 取较小的比例确保两边都适合

                                    # 应用缩放
                                    width = width * ratio
                                    height = height * ratio
                                else:
                                    # 如果宽高为0，使用默认值
                                    width = safe_max_width
                                    height = safe_max_height

                                # 最终安全检查
                                if width > safe_max_width:
                                    width = safe_max_width
                                if height > safe_max_height:
                                    height = safe_max_height

                                # 添加图像到文档
                                try:
                                    print(
                                        f"添加图像，尺寸: {width} x {height}, 页面最大尺寸: {max_width} x {max_height}"
                                    )
                                    img_obj = Image(
                                        img_temp_path, width=width, height=height
                                    )
                                    elements.append(img_obj)
                                except Exception as img_err:
                                    print(
                                        f"添加图像失败: {str(img_err)}，尝试进一步缩小图像"
                                    )
                                    # 如果添加失败，使用固定的较小尺寸
                                    width = max_width * 0.8
                                    height = max_height * 0.8
                                    print(f"使用固定尺寸: {width} x {height}")
                                    img_obj = Image(
                                        img_temp_path, width=width, height=height
                                    )
                                    elements.append(img_obj)

                                elements.append(Spacer(1, 20))  # 增加间距

                                # 如果不是最后一页，添加页面分隔符
                                if j < len(images) - 1:
                                    elements.append(PageBreak())
                    except ConversionTimeoutError:
                        print(f"处理PDF超时: {temp_pdf_path}")
                        elements.append(
                            Paragraph(
                                f"处理文件超时 - 文件可能太大或格式复杂",
                                chinese_style,
                            )
                        )
                        # 使用PyPDF2作为备选
                        try:
                            pdf_reader = PdfReader(temp_pdf_path)
                            page_count = len(pdf_reader.pages)
                            elements.append(
                                Paragraph(f"PDF页数: {page_count}", chinese_style)
                            )
                        except Exception as pdf_err:
                            print(f"备选PDF处理也失败: {str(pdf_err)}")
                            pass
                    except Exception as e:
                        print(f"转换PDF为图像失败: {str(e)}")
                        elements.append(
                            Paragraph(
                                f"转换PDF为图像失败: {str(e)[:100]}", chinese_style
                            )
                        )
                else:
                    # 如果未安装poppler，使用PyPDF2直接提取PDF信息
                    print(f"Poppler未安装，使用PyPDF2直接处理PDF: {temp_pdf_path}")
                    has_valid_content = True

                    # 使用PyPDF2读取PDF
                    pdf_reader = PdfReader(temp_pdf_path)
                    page_count = len(pdf_reader.pages)

                    # 添加PDF信息到文档
                    elements.append(
                        Paragraph(f"PDF页数: {page_count}", chinese_style)
                    )
                    elements.append(
                        Paragraph(
                            "注意: 由于系统未安装Poppler工具，无法显示PDF预览图像。",
                            chinese_style,
                        )
                    )
                    elements.append(
                        Paragraph(
                            "建议安装poppler-utils包以获得完整功能。", chinese_style
                        )
                    )
                    elements.append(Spacer(1, 10))
            except Exception as e:
                print(f"处理文件 {title} 内容时出错: {str(e)}")
                elements.append(
                    Paragraph(f"处理文件内容失败: {str(e)[:200]}", chinese_style)
                )

            # 添加页面分隔符
            elements.append(PageBreak())
        except Exception as e:
            print(f"处理文件 {pdf_file.original_filename} 时出错: {str(e)}")
            elements.append(Paragraph(f"处理失败: {str(e)[:200]}", chinese_style))
            elements.append(PageBreak())

    # 如果没有有效内容，创建一个提示页面
    if not has_valid_content and len(elements) <= 2:  # 只有目录标题和一个空行
        print("没有有效内容，创建提示页面")
        elements = []
        elements.append(Paragraph("合并失败 - 没有有效的PDF内容", title_style))
        elements.append(Spacer(1, 20))
        elements.append(Paragraph("可能的原因:", chinese_style))
        elements.append(Paragraph("1. 所有文件转换失败", chinese_style))
        elements.append(Paragraph("2. 文件格式不受支持", chinese_style))
        elements.append(Paragraph("3. 文件内容为空", chinese_style))

    # 定义页面构建函数，用于添加页码
    def add_page_number(canvas, doc):
        """
        在每个页面添加页码的回调函数
        """
        canvas.saveState()
        canvas.setFont(font_name, 10)
        # 在页面底部中央添加页码，使用自定义起始页码
        page_num = canvas.getPageNumber() + start_page_number - 1
        text = f"第 {page_num} 页"
        canvas.drawCentredString(page_width/2, 30, text)
        canvas.restoreState()

    # 构建 PDF，添加页码
    try:
        doc.build(elements, onFirstPage=add_page_number, onLaterPages=add_page_number)
        buffer.seek(0)

        # 创建 HTTP 响应
        response = HttpResponse(buffer.getvalue(), content_type="application/pdf")
        # 使用RFC 5987编码支持中文文件名
        filename_utf8 = urllib.parse.quote(output_filename)
        response["Content-Disposition"] = (
            f"attachment; filename=\"{filename_utf8}.pdf\"; filename*=UTF-8''{filename_utf8}.pdf"
        )
        return response
    except Exception as e:
        print(f"生成PDF时发生错误: {str(e)}")
        # 如果生成失败，创建简单的错误页面
        error_buffer = BytesIO()
        c = canvas.Canvas(error_buffer, pagesize=A4)
        font_name = get_chinese_font_name()
        c.setFont(font_name, 16)
        c.drawString(50, 800, "PDF生成失败")
        c.setFont(font_name, 12)
        c.drawString(50, 770, f"文件名: {output_filename}")
        c.drawString(50, 740, f"错误信息: {str(e)[:200]}")
        c.save()
        error_buffer.seek(0)

        # 返回错误 PDF
        response = HttpResponse(
            error_buffer.getvalue(), content_type="application/pdf"
        )
        # 使用RFC 5987编码支持中文文件名
        filename_utf8 = urllib.parse.quote(output_filename)
        response["Content-Disposition"] = (
            f"attachment; filename=\"{filename_utf8}.pdf\"; filename*=UTF-8''{filename_utf8}.pdf"
        )
        return response
    finally:
        # 清理所有临时文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                    print(f"清理临时文件: {temp_file}")
                except Exception as e:
                    print(f"清理临时文件失败: {temp_file}, 错误: {str(e)}")
                    pass


def convert_with_libreoffice(input_path, output_path):
    """
    使用LibreOffice转换Word文档到PDF
    
    params:
        input_path: 输入的Word文档路径
        output_path: 输出的PDF文件路径
    """
    # 获取输出目录
    output_dir = os.path.dirname(output_path)
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 使用系统临时目录
    import tempfile
    temp_dir = tempfile.mkdtemp()
    
    try:
        # 使用临时目录作为输出目录
        command = [
            "libreoffice",
            "--headless",
            "--convert-to",
            "pdf",
            "--outdir",
            temp_dir,
            input_path,
        ]

        # 执行命令并捕获输出
        print(f"执行转换命令: {command}")
        result = subprocess.run(command, capture_output=True, text=True, check=False, timeout=60)
        
        # 检查转换结果
        if result.returncode != 0:
            print(f"LibreOffice转换错误: {result.stderr}")
            return False, None
            
        # 转换后的PDF文件会保留原文件名但扩展名改为pdf
        output_filename = os.path.splitext(os.path.basename(input_path))[0] + ".pdf"
        temp_pdf_path = os.path.join(temp_dir, output_filename)
        
        # 检查文件是否存在并复制到目标路径
        if os.path.exists(temp_pdf_path):
            shutil.copy(temp_pdf_path, output_path)
            print(f"成功转换文件: {temp_pdf_path} -> {output_path}")
            return True, output_path
        else:
            print(f"转换后的PDF文件不存在: {temp_pdf_path}")
            return False, None
    except Exception as e:
        print(f"LibreOffice转换失败: {str(e)}")
        return False, None
    finally:
        # 清理临时目录
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir, ignore_errors=True)