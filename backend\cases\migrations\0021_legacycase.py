# Generated by Django 5.0.3 on 2025-05-08 14:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0020_case_is_sensitive'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LegacyCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contract_number', models.CharField(max_length=50, verbose_name='合同编号')),
                ('filing_date', models.DateField(verbose_name='收案日期')),
                ('client', models.CharField(max_length=200, verbose_name='委托人/当事人')),
                ('opposing_party', models.CharField(blank=True, max_length=200, null=True, verbose_name='对方当事人')),
                ('criminal', models.Char<PERSON>ield(blank=True, max_length=200, null=True, verbose_name='犯罪嫌疑人')),
                ('case_cause', models.CharField(max_length=200, verbose_name='案由')),
                ('court', models.CharField(blank=True, max_length=200, null=True, verbose_name='受理机关')),
                ('trial_level', models.CharField(blank=True, max_length=50, null=True, verbose_name='审级')),
                ('case_type', models.CharField(choices=[('民事', '民事案件'), ('刑事', '刑事案件'), ('行政', '行政案件')], default='民事', max_length=10, verbose_name='案件类型')),
                ('fee_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='收费金额')),
                ('is_fee_compliant', models.BooleanField(default=True, verbose_name='是否符合收费标准')),
                ('invoice_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='发票号码')),
                ('is_conflict', models.BooleanField(default=False, verbose_name='是否利益冲突')),
                ('is_sensitive', models.BooleanField(default=False, verbose_name='是否重大敏感案件')),
                ('closing_date', models.DateField(blank=True, null=True, verbose_name='结案日期')),
                ('archive_date', models.DateField(blank=True, null=True, verbose_name='归档日期')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='approved_legacy_cases', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
                ('lawyer', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='legacy_cases', to=settings.AUTH_USER_MODEL, verbose_name='承办律师')),
            ],
            options={
                'verbose_name': '历史案件',
                'verbose_name_plural': '历史案件',
                'ordering': ['-filing_date'],
            },
        ),
    ]
