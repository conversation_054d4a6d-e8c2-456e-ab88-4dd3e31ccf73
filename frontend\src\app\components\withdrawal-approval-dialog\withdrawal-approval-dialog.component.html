<h2 mat-dialog-title>审批提款申请</h2>

<form [formGroup]="form" (ngSubmit)="onSubmit()">
  <mat-dialog-content>
    <div class="withdrawal-info">
      <h3>申请信息</h3>
      <p><strong>申请人：</strong>{{ data.withdrawal.requester.first_name || data.withdrawal.requester.username }}</p>
      <p><strong>案件：</strong>{{ data.withdrawal.case.case_number }} - {{ data.withdrawal.case.case_cause }}</p>
      <p><strong>申请金额：</strong>¥{{ data.withdrawal.amount.toFixed(2) }}</p>
    </div>

    <div class="approval-form">
      <h3>审批信息</h3>
      
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>应扣税费（¥）</mat-label>
        <input matInput 
               formControlName="deduction_tax" 
               type="number" 
               step="0.01" 
               min="0" 
               [max]="data.withdrawal.amount">
        <mat-error *ngIf="form.get('deduction_tax')?.hasError('min')">
          应扣税费不能为负数
        </mat-error>
        <mat-error *ngIf="form.get('deduction_tax')?.hasError('max')">
          应扣税费不能超过申请金额
        </mat-error>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>请他应扣项目</mat-label>
        <textarea matInput 
                  formControlName="other_deductions" 
                  placeholder="请输入其他应扣项目说明"
                  rows="3"></textarea>
      </mat-form-field>

      <mat-form-field appearance="outline" class="full-width">
        <mat-label>备注说明</mat-label>
        <textarea matInput 
                  formControlName="remarks" 
                  placeholder="请输入备注说明"
                  rows="3"></textarea>
      </mat-form-field>

      <div class="final-amount">
        <strong>实际提取金额：¥{{ finalAmount.toFixed(2) }}</strong>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button type="button" (click)="onCancel()">取消</button>
    <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid">
      确认审批
    </button>
  </mat-dialog-actions>
</form> 