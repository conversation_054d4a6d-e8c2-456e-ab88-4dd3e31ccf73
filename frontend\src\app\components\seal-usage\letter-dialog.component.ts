import { Component, OnInit, Inject } from '@angular/core';
import { <PERSON><PERSON>uilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgIf, NgFor } from '@angular/common';
import { CaseService, LetterRecord, LetterRecordCreateDto } from '../../services/case.service';
import { AuthService } from '../../services/auth.service';
import { CaseSearchComponent } from '../shared/case-search.component';
import { Case } from '../../interfaces/case.interface';
import { FirmInfoService } from '../../services/firm-info.service';

interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
}

@Component({
  selector: 'app-letter-dialog',
  standalone: true,
  imports: [
    NgIf, NgFor, ReactiveFormsModule,
    MatDialogModule, MatButtonModule, MatFormFieldModule,
    MatInputModule, MatDatepickerModule, MatNativeDateModule,
    MatSelectModule, MatIconModule, MatSnackBarModule,
    MatTooltipModule, CaseSearchComponent
  ],
  template: `
    <h2 mat-dialog-title>{{ isEditMode ? '编辑函件记录' : '新增函件记录' }}</h2>
    
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div mat-dialog-content>
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>日期</mat-label>
            <input matInput [matDatepicker]="picker" formControlName="date" required>
            <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-datepicker #picker></mat-datepicker>
            <mat-error *ngIf="form.get('date')?.hasError('required')">
              日期不能为空
            </mat-error>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>函件类型</mat-label>
            <mat-select formControlName="letterType" required>
              <mat-option *ngFor="let type of letterTypes" [value]="type">{{ type }}</mat-option>
            </mat-select>
            <mat-error *ngIf="form.get('letterType')?.hasError('required')">
              函件类型不能为空
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <app-case-search
            label="对应案件号"
            placeholder="搜索案件号、案由或当事人姓名（可选）"
            appearance="outline"
            width="100%"
            class="full-width"
            (caseNumberSelected)="onCaseNumberSelected($event)"
            (caseSelected)="onCaseSelected($event)">
          </app-case-search>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>委托人</mat-label>
            <input matInput formControlName="clientName" placeholder="请输入委托人" required>
            <mat-error *ngIf="form.get('clientName')?.hasError('required')">
              委托人/当事人不能为空
            </mat-error>
            <mat-error *ngIf="form.get('clientName')?.hasError('maxlength')">
              委托人/当事人不能超过200个字符
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>数量</mat-label>
            <input matInput type="number" formControlName="quantity" placeholder="请输入数量" required>
            <mat-error *ngIf="form.get('quantity')?.hasError('required')">
              数量不能为空
            </mat-error>
            <mat-error *ngIf="form.get('quantity')?.hasError('min')">
              数量必须大于0
            </mat-error>
          </mat-form-field>
          
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>致函单位</mat-label>
            <input matInput formControlName="recipientUnit" placeholder="请输入致函单位" required>
            <mat-error *ngIf="form.get('recipientUnit')?.hasError('required')">
              致函单位不能为空
            </mat-error>
            <mat-error *ngIf="form.get('recipientUnit')?.hasError('maxlength')">
              致函单位不能超过200个字符
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>使用律师</mat-label>
            <input matInput formControlName="lawyerName" placeholder="请输入使用律师姓名" required>
            <mat-error *ngIf="form.get('lawyerName')?.hasError('required')">
              使用律师不能为空
            </mat-error>
            <mat-error *ngIf="form.get('lawyerName')?.hasError('maxlength')">
              使用律师姓名不能超过100个字符
            </mat-error>
          </mat-form-field>
        </div>
        
        <div class="form-row">
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>备注</mat-label>
            <textarea matInput formControlName="remarks" placeholder="请输入备注" rows="3"></textarea>
          </mat-form-field>
        </div>
      </div>
      
      <div mat-dialog-actions align="end">
        <button mat-button type="button" [mat-dialog-close]="false">取消</button>
        <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid">保存</button>
      </div>
    </form>
  `,
  styles: [`
    .form-row {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
    }
    .full-width {
      width: 100%;
    }
    .half-width {
      width: calc(50% - 8px);
    }

    mat-form-field {
      margin-bottom: 0;
    }
    @media (max-width: 600px) {
      .form-row {
        flex-direction: column;
      }
      .half-width {
        width: 100%;
      }
      .case-search-wrapper {
        flex-direction: row;
      }
    }
  `]
})
export class LetterDialogComponent implements OnInit {
  form: FormGroup;
  isEditMode: boolean = false;
  users: User[] = [];
  selectedCase: Case | null = null;
  letterTypes: string[] = [];

  constructor(
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<LetterDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: LetterRecord | null,
    private caseService: CaseService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private firmInfoService: FirmInfoService
  ) {
    this.isEditMode = !!data;
    this.form = this.createForm();
  }

  ngOnInit(): void {
    this.initializeLetterTypes();
    this.loadUsers();
    if (this.data) {
      this.populateForm();
    } else {
      // 新增时设置默认值
      this.setDefaultValues();
    }
  }

  /**
   * 根据律师事务所初始化函件类型
   */
  initializeLetterTypes(): void {
    const firmName = this.firmInfoService.firmName;
    
    if (firmName === '广东承诺律师事务所雷州分所') {
      // 雷州分所的函件类型
      this.letterTypes = [
        '广承雷民函',
        '广承雷刑函',
        '广承雷行函',
        '广承雷刑会',
        '广承雷律调',
        '广承雷律师函',
        '广承雷意见'
      ];
    } else {
      // 总所的函件类型
      this.letterTypes = [
        '广承民函',
        '广承刑函',
        '广承行函',
        '广承刑会',
        '广承律调',
        '广承律师函',
        '广承意见'
      ];
    }
  }

  createForm(): FormGroup {
    return this.fb.group({
      date: ['', Validators.required],
      letterType: ['', Validators.required],
      caseNumber: ['', [Validators.maxLength(100)]],
      clientName: ['', [Validators.required, Validators.maxLength(200)]],
      quantity: [1, [Validators.required, Validators.min(1)]],
      recipientUnit: ['', [Validators.required, Validators.maxLength(200)]],
      lawyerName: ['', [Validators.required, Validators.maxLength(100)]],
      remarks: ['']
    });
  }

  loadUsers(): void {
    this.authService.getAllUsers().subscribe({
      next: (users) => {
        this.users = users;
      },
      error: (error) => {
        console.error('获取用户列表失败', error);
      }
    });
  }

  setDefaultValues(): void {
    // 设置默认日期为今天
    this.form.patchValue({
      date: new Date(),
      quantity: 1,
      letterType: this.letterTypes[0] || '' // 设置第一个函件类型为默认值
    });

    // 设置当前用户为默认使用律师
    this.authService.getCurrentUser().subscribe(user => {
      if (user) {
        const displayName = this.getUserDisplayName(user);
        this.form.patchValue({
          lawyerName: displayName
        });
      }
    });
  }

  populateForm(): void {
    if (this.data) {
      this.form.patchValue({
        date: new Date(this.data.date),
        letterType: this.data.letter_type,
        caseNumber: this.data.case_number || '',
        clientName: this.data.client_name,
        quantity: this.data.quantity,
        recipientUnit: this.data.recipient_unit,
        lawyerName: this.data.lawyer_name,
        remarks: this.data.remarks || ''
      });
    }
  }

  onSubmit(): void {
    if (this.form.valid) {
      const formData = this.form.value;
      const letterData: LetterRecordCreateDto = {
        date: this.formatDate(formData.date),
        letter_type: formData.letterType,
        case_number: formData.caseNumber || undefined,
        client_name: formData.clientName,
        quantity: formData.quantity,
        recipient_unit: formData.recipientUnit,
        lawyer_name: formData.lawyerName,
        remarks: formData.remarks || undefined
      };

      if (this.isEditMode && this.data?.id) {
        // 编辑模式
        this.caseService.updateLetterRecord(this.data.id, letterData).subscribe({
          next: () => {
            this.snackBar.open('函件记录更新成功', '关闭', {
              duration: 3000
            });
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('更新函件记录失败', error);
            this.snackBar.open('更新函件记录失败: ' + (error.error?.detail || '未知错误'), '关闭', {
              duration: 3000
            });
          }
        });
      } else {
        // 新增模式
        this.caseService.createLetterRecord(letterData).subscribe({
          next: () => {
            this.snackBar.open('函件记录创建成功', '关闭', {
              duration: 3000
            });
            this.dialogRef.close(true);
          },
          error: (error) => {
            console.error('创建函件记录失败', error);
            this.snackBar.open('创建函件记录失败: ' + (error.error?.detail || '未知错误'), '关闭', {
              duration: 3000
            });
          }
        });
      }
    }
  }

  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  getUserDisplayName(user: User): string {
    if (user.first_name || user.last_name) {
      return `${user.first_name || ''}${user.last_name || ''}`.trim();
    }
    return user.username;
  }

  onCaseSelected(caseItem: Case | null): void {
    this.selectedCase = caseItem;
    if (caseItem && caseItem.parties && caseItem.parties.length > 0) {
      // 查找委托人（is_client为true的当事人）
      const clients = caseItem.parties.filter(party => party.is_client);
      
      if (clients.length > 0) {
        // 收集所有委托人的姓名
        const clientNames: string[] = [];
        
        clients.forEach(client => {
          let name = '';
          if (client.natural_person?.name) {
            name = client.natural_person.name;
          } else if (client.legal_entity?.name) {
            name = client.legal_entity.name;
          }
          
          if (name && !clientNames.includes(name)) {
            clientNames.push(name);
          }
        });
        
        if (clientNames.length > 0) {
          // 如果有多个委托人，用顿号分隔
          const clientNameStr = clientNames.join('、');
          this.form.patchValue({
            clientName: clientNameStr
          });
          
          // 显示提示信息
          if (clientNames.length > 1) {
            this.snackBar.open(`已自动填入${clientNames.length}个委托人信息`, '关闭', {
              duration: 2000
            });
          }
        }
      } else {
        // 如果没有找到委托人，但有当事人，提示用户手动填写
        this.snackBar.open('该案件未找到委托人信息，请手动填写', '关闭', {
          duration: 3000
        });
      }
    }
  }

  onCaseNumberSelected(caseNumber: string): void {
    this.form.patchValue({
      caseNumber: caseNumber
    });
  }


} 