import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { WithdrawalRequest, WithdrawalRequestCreate, WithdrawalApprovalData } from '../interfaces/withdrawal.interface';

@Injectable({
  providedIn: 'root'
})
export class WithdrawalService {
  private apiUrl = '/api/finance/withdrawals/';

  constructor(private http: HttpClient) {}

  /**
   * 获取提款申请列表
   */
  getWithdrawals(): Observable<WithdrawalRequest[]> {
    return this.http.get<WithdrawalRequest[]>(this.apiUrl);
  }

  /**
   * 获取单个提款申请详情
   */
  getWithdrawal(id: number): Observable<WithdrawalRequest> {
    return this.http.get<WithdrawalRequest>(`${this.apiUrl}${id}/`);
  }

  /**
   * 创建提款申请
   */
  createWithdrawal(withdrawal: WithdrawalRequestCreate): Observable<WithdrawalRequest> {
    return this.http.post<WithdrawalRequest>(this.apiUrl, withdrawal);
  }

  /**
   * 更新提款申请
   */
  updateWithdrawal(id: number, withdrawal: Partial<WithdrawalRequest>): Observable<WithdrawalRequest> {
    return this.http.patch<WithdrawalRequest>(`${this.apiUrl}${id}/`, withdrawal);
  }

  /**
   * 删除提款申请
   */
  deleteWithdrawal(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}${id}/`);
  }

  /**
   * 审批通过提款申请
   */
  approveWithdrawal(id: number, approvalData: WithdrawalApprovalData): Observable<any> {
    return this.http.post(`${this.apiUrl}${id}/approve/`, approvalData);
  }

  /**
   * 拒绝提款申请
   */
  rejectWithdrawal(id: number): Observable<any> {
    return this.http.post(`${this.apiUrl}${id}/reject/`, {});
  }

  /**
   * 获取案件的提款状态
   */
  getCaseWithdrawalStatus(caseId: number): Observable<{has_pending_request: boolean}> {
    return this.http.get<{has_pending_request: boolean}>(`${this.apiUrl}case/${caseId}/status/`);
  }

  /**
   * 获取律师在特定案件中的剩余可提取金额
   */
  getCaseAvailableAmount(caseId: number): Observable<{available_amount: number, has_available_amount: boolean}> {
    return this.http.get<{available_amount: number, has_available_amount: boolean}>(`${this.apiUrl}case/${caseId}/available_amount/`);
  }

  /**
   * 打印提款申请单
   */
  printWithdrawal(id: number): Observable<WithdrawalRequest> {
    return this.http.post<WithdrawalRequest>(`${this.apiUrl}${id}/print/`, {});
  }

  /**
   * 打开提款申请打印窗口
   */
  openPrintWindow(withdrawal: WithdrawalRequest): void {
    const printData = encodeURIComponent(JSON.stringify(withdrawal));
    const printUrl = `/api/finance/templates/finance/print_withdrawal.html?data=${printData}`;
    
    // 打开新窗口进行打印
    const printWindow = window.open(printUrl, '_blank', 'width=800,height=600');
    
    if (printWindow) {
      printWindow.focus();
      
      // 打印窗口关闭时的处理
      printWindow.onbeforeunload = () => {
        // 可以在这里添加一些清理逻辑
      };
    } else {
      alert('无法打开打印窗口，请检查浏览器的弹窗设置');
    }
  }
} 