# Generated by Django 5.0.3 on 2025-05-24 01:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0024_alter_legalentity_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SealUsageRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(help_text='记录年份', verbose_name='年份')),
                ('sequence_number', models.IntegerField(help_text='当年序号', verbose_name='序号')),
                ('date', models.DateField(verbose_name='日期')),
                ('matter', models.CharField(max_length=200, verbose_name='事由')),
                ('document_name', models.CharField(max_length=200, verbose_name='文件名称')),
                ('quantity', models.IntegerField(default=1, verbose_name='数量')),
                ('seal_type', models.CharField(choices=[('公章', '公章'), ('合同章', '合同章'), ('财务章', '财务章'), ('法人章', '法人章'), ('其他', '其他')], default='公章', max_length=20, verbose_name='用章种类')),
                ('recipient_unit', models.CharField(max_length=200, verbose_name='受送单位')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_printed_at', models.DateTimeField(blank=True, null=True, verbose_name='最后打印时间')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_seal_records', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
                ('operator', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='operated_seal_records', to=settings.AUTH_USER_MODEL, verbose_name='盖章经办人')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='seal_usage_records', to=settings.AUTH_USER_MODEL, verbose_name='使用人')),
            ],
            options={
                'verbose_name': '公章使用登记',
                'verbose_name_plural': '公章使用登记',
                'ordering': ['-year', '-sequence_number'],
                'unique_together': {('year', 'sequence_number')},
            },
        ),
    ]
