# Generated by Django 5.0.3 on 2025-06-05 18:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('finance', '0004_withdrawalrequest_financerecord_withdrawal_request'),
    ]

    operations = [
        migrations.AddField(
            model_name='financerecord',
            name='office_expense',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='finance_record', to='finance.officeexpense', verbose_name='关联办公费用'),
        ),
        migrations.AlterField(
            model_name='financerecord',
            name='account_type',
            field=models.CharField(choices=[('case_income', '案件收入'), ('lawyer_payment', '律师缴费'), ('lawyer_withdrawal', '律师提款'), ('lawyer_task_expense', '律师任务开支'), ('office_expense', '办公费用'), ('other_expense', '其他支出')], max_length=50, verbose_name='账目类型'),
        ),
    ]
