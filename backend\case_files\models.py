import os
import uuid
from django.db import models
from django.contrib.auth.models import User
from cases.models import Case

def case_file_path(instance, filename):
    """为案件文件生成路径"""
    # 使用案件ID和UUID创建唯一路径
    # 如果文件夹存在且有路径，则使用文件夹路径，否则直接存储在案件目录下
    if instance.folder and instance.folder.path:
        return f'case_files/{instance.case.id}/{instance.folder.path}/{filename}'
    else:
        return f'case_files/{instance.case.id}/{filename}'

class CaseFileFolder(models.Model):
    """案件文件夹模型"""
    case = models.ForeignKey(
        Case, 
        on_delete=models.CASCADE,
        related_name='folders',
        verbose_name='所属案件'
    )
    name = models.CharField(max_length=255, verbose_name='文件夹名称')
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='children',
        verbose_name='父文件夹'
    )
    path = models.CharField(max_length=1000, verbose_name='路径', help_text='从根目录到当前文件夹的路径')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '案件文件夹'
        verbose_name_plural = '案件文件夹'
        unique_together = ['case', 'parent', 'name']
        
    def __str__(self):
        return f"{self.case.case_number} - {self.path}/{self.name}"
        
    def save(self, *args, **kwargs):
        # 生成路径
        if self.parent:
            self.path = f"{self.parent.path}/{self.parent.name}" if self.parent.path else self.parent.name
        else:
            self.path = ""
        super().save(*args, **kwargs)

class CaseFile(models.Model):
    """案件文件模型"""
    FILE_TYPES = (
        ('doc', 'Word文档(doc)'),
        ('docx', 'Word文档(docx)'),
        ('xls', 'Excel表格(xls)'),
        ('xlsx', 'Excel表格(xlsx)'),
        ('pdf', 'PDF文档'),
        ('jpg', 'JPG图片'),
        ('jpeg', 'JPEG图片'),
        ('png', 'PNG图片'),
        ('zip', 'ZIP压缩文件'),
        ('other', '其他文件'),
    )
    
    case = models.ForeignKey(
        Case, 
        on_delete=models.CASCADE,
        related_name='files',
        verbose_name='所属案件'
    )
    folder = models.ForeignKey(
        CaseFileFolder,
        on_delete=models.CASCADE,
        related_name='files',
        verbose_name='所属文件夹',
        null=True,
        blank=True
    )
    file = models.FileField(upload_to=case_file_path, verbose_name='文件')
    file_type = models.CharField(max_length=10, choices=FILE_TYPES, verbose_name='文件类型')
    original_filename = models.CharField(max_length=255, verbose_name='原始文件名')
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='uploaded_case_files',
        verbose_name='上传者'
    )
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name='上传时间')
    order = models.IntegerField(default=0, verbose_name='排序', help_text='用于PDF合并时的顺序')
    
    class Meta:
        verbose_name = '案件文件'
        verbose_name_plural = '案件文件'
        ordering = ['order', 'uploaded_at']
        
    def __str__(self):
        return self.original_filename
        
    def filename(self):
        return os.path.basename(self.file.name)
