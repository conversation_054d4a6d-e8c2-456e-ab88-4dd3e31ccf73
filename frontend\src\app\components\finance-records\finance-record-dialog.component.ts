import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Observable, debounceTime, distinctUntilChanged, switchMap, startWith, of } from 'rxjs';

import { FinanceService } from '../../services/finance.service';
import { FinanceR<PERSON>ord, FINANCE_ACCOUNT_TYPES } from '../../interfaces/finance.interface';
import { User, Case } from '../../interfaces/case.interface';
import { getUserDisplayName } from '../../utils/user.utils';

@Component({
  selector: 'app-finance-record-dialog',
  template: `
    <h2 mat-dialog-title>{{ data.mode === 'create' ? '创建财务记录' : '编辑财务记录' }}</h2>
    <mat-dialog-content>
      <form [formGroup]="recordForm" class="record-form">
        <mat-form-field appearance="fill">
          <mat-label>交易日期</mat-label>
          <input matInput [matDatepicker]="picker" formControlName="transaction_date" required>
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="fill">
          <mat-label>账目类型</mat-label>
          <mat-select formControlName="account_type" required>
            <mat-option *ngFor="let type of accountTypes" [value]="type.value">
              {{ type.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="fill">
          <mat-label>律师</mat-label>
          <mat-select formControlName="lawyer_id" required>
            <mat-option *ngFor="let lawyer of data.lawyers" [value]="lawyer.id">
              {{ getUserDisplayName(lawyer) }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="fill">
          <mat-label>关联案件（可选）</mat-label>
          <input matInput
                 placeholder="输入案件编号或案由搜索"
                 formControlName="case_search"
                 [matAutocomplete]="caseAuto">
          <mat-autocomplete #caseAuto="matAutocomplete" 
                           [displayWith]="displayCaseFn.bind(this)"
                           (optionSelected)="onCaseSelected($event)">
            <mat-option *ngFor="let caseItem of filteredCases$ | async" [value]="caseItem">
              <div class="case-option">
                <div class="case-number">{{ caseItem.case_number }}</div>
                <div class="case-cause">{{ caseItem.case_cause }}</div>
              </div>
            </mat-option>
            <mat-option *ngIf="(filteredCases$ | async)?.length === 0" [value]="null" disabled>
              <em>无匹配案件</em>
            </mat-option>
          </mat-autocomplete>
          <button matSuffix mat-icon-button type="button" 
                  *ngIf="selectedCase" 
                  (click)="clearCaseSelection()"
                  matTooltip="清除案件选择">
            <mat-icon>clear</mat-icon>
          </button>
        </mat-form-field>

        <mat-form-field appearance="fill">
          <mat-label>金额</mat-label>
          <input matInput type="number" formControlName="display_amount" required min="0" step="0.01" placeholder="请输入金额">
          <span matSuffix>元</span>
        </mat-form-field>

        <mat-form-field appearance="fill">
          <mat-label>用途</mat-label>
          <input matInput formControlName="purpose" required placeholder="请输入用途">
        </mat-form-field>

        <mat-form-field appearance="fill">
          <mat-label>备注</mat-label>
          <textarea matInput formControlName="remarks" rows="3" placeholder="可选备注信息"></textarea>
        </mat-form-field>
      </form>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button [mat-dialog-close]="null">取消</button>
      <button mat-raised-button color="primary" 
              [disabled]="!recordForm.valid"
              (click)="onSubmit()">
        {{ data.mode === 'create' ? '创建' : '保存' }}
      </button>
    </mat-dialog-actions>
  `,
  styles: [`
    .record-form {
      display: flex;
      flex-direction: column;
      gap: 16px;
      min-width: 500px;
    }
    
    mat-form-field {
      width: 100%;
    }

    .case-option {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .case-number {
      font-weight: 500;
      color: #1976d2;
    }

    .case-cause {
      font-size: 0.9em;
      color: #666;
    }
  `],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatAutocompleteModule
  ]
})
export class FinanceRecordDialogComponent {
  recordForm: FormGroup;
  accountTypes = FINANCE_ACCOUNT_TYPES;
  getUserDisplayName = getUserDisplayName;
  
  filteredCases$: Observable<Case[]>;
  selectedCase: Case | null = null;

  constructor(
    public dialogRef: MatDialogRef<FinanceRecordDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private financeService: FinanceService
  ) {
    const today = new Date().toISOString().split('T')[0];
    
    this.recordForm = this.fb.group({
      transaction_date: [data.record?.transaction_date || today, Validators.required],
      account_type: [data.record?.account_type || '', Validators.required],
      lawyer_id: [data.record?.lawyer?.id || '', Validators.required],
      case_search: [''],
      case_id: [data.record?.case?.id || null],
      display_amount: [data.record?.display_amount || '', [Validators.required, Validators.min(0)]],
      purpose: [data.record?.purpose || '', Validators.required],
      remarks: [data.record?.remarks || '']
    });

    // 如果是编辑模式且有关联案件，设置初始值
    if (data.record?.case) {
      this.selectedCase = data.record.case;
      this.recordForm.patchValue({
        case_search: this.displayCaseFn(data.record.case)
      });
    }

    // 设置案件搜索
    this.filteredCases$ = this.recordForm.get('case_search')!.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(value => {
        if (typeof value === 'string' && value.length >= 2) {
          return this.financeService.searchCases(value);
        }
        return of([]);
      })
    );
  }

  displayCaseFn(caseItem: Case | null): string {
    if (!caseItem) return '';
    const caseNumber = caseItem.case_number || '暂无编号';
    const caseCause = caseItem.case_cause || '案由未设';
    return `${caseNumber} - ${caseCause}`;
  }

  onCaseSelected(event: any): void {
    this.selectedCase = event.option.value;
    this.recordForm.patchValue({
      case_id: this.selectedCase?.id || null
    });
  }

  clearCaseSelection(): void {
    this.selectedCase = null;
    this.recordForm.patchValue({
      case_search: '',
      case_id: null
    });
  }

  onSubmit(): void {
    if (this.recordForm.valid) {
      const formValue = { ...this.recordForm.value };
      // 移除搜索字段，只保留case_id
      delete formValue.case_search;
      this.dialogRef.close(formValue);
    }
  }
} 