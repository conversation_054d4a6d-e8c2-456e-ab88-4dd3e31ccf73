import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class LegacyCaseService {
  private apiUrl = `${environment.apiUrl}/legacy-cases/`;

  constructor(private http: HttpClient) { }

  importCsv(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('file', file);
    return this.http.post(`${this.apiUrl}import_csv/`, formData);
  }

  getLegacyCases(params: any = {}): Observable<any> {
    return this.http.get(this.apiUrl, { params });
  }
} 