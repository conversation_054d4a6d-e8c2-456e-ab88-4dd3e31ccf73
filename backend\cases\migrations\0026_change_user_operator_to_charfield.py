# Generated manually

from django.db import migrations, models
import django.db.models.deletion


def copy_user_data_to_charfield(apps, schema_editor):
    """将现有的外键数据复制到新的字符串字段"""
    SealUsageRecord = apps.get_model('cases', 'SealUsageRecord')
    
    # 使用指定的数据库
    db_alias = schema_editor.connection.alias
    
    for record in SealUsageRecord.objects.using(db_alias).all():
        # 复制使用人数据
        if hasattr(record, 'user') and record.user:
            if record.user.last_name or record.user.first_name:
                record.user_name = f"{record.user.last_name or ''}{record.user.first_name or ''}".strip()
            else:
                record.user_name = record.user.username
        
        # 复制经办人数据
        if hasattr(record, 'operator') and record.operator:
            if record.operator.last_name or record.operator.first_name:
                record.operator_name = f"{record.operator.last_name or ''}{record.operator.first_name or ''}".strip()
            else:
                record.operator_name = record.operator.username
        
        record.save(using=db_alias)


def reverse_copy_user_data(apps, schema_editor):
    """反向操作：从字符串字段恢复到外键（这个操作会丢失数据）"""
    # 这个操作无法完全恢复，因为我们无法从姓名反推出用户ID
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0025_sealusagerecord'),
    ]

    operations = [
        # 1. 添加新的字符串字段
        migrations.AddField(
            model_name='sealusagerecord',
            name='user_name',
            field=models.CharField(default='', max_length=100, verbose_name='使用人'),
        ),
        migrations.AddField(
            model_name='sealusagerecord',
            name='operator_name',
            field=models.CharField(default='', max_length=100, verbose_name='盖章经办人'),
        ),
        
        # 2. 复制数据
        migrations.RunPython(copy_user_data_to_charfield, reverse_copy_user_data),
        
        # 3. 删除旧的外键字段
        migrations.RemoveField(
            model_name='sealusagerecord',
            name='user',
        ),
        migrations.RemoveField(
            model_name='sealusagerecord',
            name='operator',
        ),
    ] 