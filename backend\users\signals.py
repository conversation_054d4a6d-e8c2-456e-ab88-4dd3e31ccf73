from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import UserProfile

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, using=None, **kwargs):
    """当用户创建时，自动创建对应的UserProfile"""
    if created:
        # 如果没有指定数据库，尝试从数据库路由器获取当前数据库
        if using is None:
            try:
                from law_firm.database_router import get_current_db_alias
                using = get_current_db_alias()
            except ImportError:
                using = 'default'
        
        # 使用正确的数据库创建UserProfile
        UserProfile.objects.using(using).get_or_create(user=instance)

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, created, using=None, **kwargs):
    """保存用户时，确保UserProfile存在并保存"""
    if not created:
        # 只在用户更新时处理，创建时已经由create_user_profile处理了
        # 如果没有指定数据库，尝试从数据库路由器获取当前数据库
        if using is None:
            try:
                from law_firm.database_router import get_current_db_alias
                using = get_current_db_alias()
            except ImportError:
                using = 'default'
        
        # 使用get_or_create确保不会重复创建
        profile, created_now = UserProfile.objects.using(using).get_or_create(user=instance)
        if not created_now:
            # 如果profile已存在，保存它以触发任何更新逻辑
            profile.save(using=using) 