import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialog } from '@angular/material/dialog';
import { WithdrawalService } from '../../services/withdrawal.service';
import { WithdrawalRequest } from '../../interfaces/withdrawal.interface';
import { getUserDisplayName } from '../../utils/user.utils';
import { WithdrawalApprovalDialogComponent } from '../withdrawal-approval-dialog/withdrawal-approval-dialog.component';

@Component({
  selector: 'app-withdrawal-list',
  templateUrl: './withdrawal-list.component.html',
  styleUrls: ['./withdrawal-list.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class WithdrawalListComponent implements OnInit {
  withdrawals: WithdrawalRequest[] = [];
  loading = false;
  error: string | null = null;

  // 导出工具函数供模板使用
  getUserDisplayName = getUserDisplayName;

  constructor(
    private withdrawalService: WithdrawalService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadWithdrawals();
  }

  /**
   * 加载提款申请列表
   */
  loadWithdrawals(): void {
    this.loading = true;
    this.error = null;
    
    this.withdrawalService.getWithdrawals().subscribe({
      next: (data: WithdrawalRequest[]) => {
        this.withdrawals = data;
        this.loading = false;
      },
      error: (error: any) => {
        console.error('加载提款申请失败:', error);
        this.error = '加载提款申请失败，请稍后重试';
        this.loading = false;
      }
    });
  }

  /**
   * 审批通过提款申请
   */
  approveWithdrawal(withdrawal: WithdrawalRequest): void {
    if (!withdrawal.can_approve) {
      return;
    }

    // 打开审批对话框
    const dialogRef = this.dialog.open(WithdrawalApprovalDialogComponent, {
      width: '600px',
      data: { withdrawal: withdrawal }
    });

    dialogRef.afterClosed().subscribe(approvalData => {
      if (approvalData) {
        // 用户确认审批，调用API
        this.withdrawalService.approveWithdrawal(withdrawal.id, approvalData).subscribe({
          next: (response: any) => {
            alert('申请已批准');
            this.loadWithdrawals(); // 重新加载列表
          },
          error: (error: any) => {
            console.error('审批失败:', error);
            alert('审批失败：' + (error.error?.error || '未知错误'));
          }
        });
      }
    });
  }

  /**
   * 拒绝提款申请
   */
  rejectWithdrawal(withdrawal: WithdrawalRequest): void {
    if (!withdrawal.can_approve) {
      return;
    }

    if (confirm(`确定要拒绝 ${this.getUserDisplayName(withdrawal.requester)} 的提款申请吗？`)) {
      this.withdrawalService.rejectWithdrawal(withdrawal.id).subscribe({
        next: (response: any) => {
          alert('申请已拒绝');
          this.loadWithdrawals(); // 重新加载列表
        },
        error: (error: any) => {
          console.error('拒绝失败:', error);
          alert('操作失败：' + (error.error?.error || '未知错误'));
        }
      });
    }
  }

  /**
   * 删除提款申请
   */
  deleteWithdrawal(withdrawal: WithdrawalRequest): void {
    if (!withdrawal.can_delete) {
      return;
    }

    if (confirm(`确定要删除这个提款申请吗？`)) {
      this.withdrawalService.deleteWithdrawal(withdrawal.id).subscribe({
        next: () => {
          alert('申请已删除');
          this.loadWithdrawals(); // 重新加载列表
        },
        error: (error: any) => {
          console.error('删除失败:', error);
          alert('删除失败：' + (error.error?.error || '未知错误'));
        }
      });
    }
  }

  /**
   * 打印提款申请单
   */
  printWithdrawal(withdrawal: WithdrawalRequest): void {
    if (withdrawal.status !== 'APPROVED') {
      alert('只有已批准的申请才能打印');
      return;
    }

    this.withdrawalService.printWithdrawal(withdrawal.id).subscribe({
      next: (data: WithdrawalRequest) => {
        this.withdrawalService.openPrintWindow(data);
      },
      error: (error: any) => {
        console.error('打印准备失败:', error);
        alert('打印准备失败：' + (error.error?.error || '未知错误'));
      }
    });
  }

  /**
   * 获取状态显示样式
   */
  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING':
        return 'status-pending';
      case 'APPROVED':
        return 'status-approved';
      case 'REJECTED':
        return 'status-rejected';
      default:
        return '';
    }
  }

  /**
   * 格式化日期显示
   */
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }
} 