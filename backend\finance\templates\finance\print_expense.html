<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>办公费用审批表打印</title>
    <style>
        body {
            font-family: Sim<PERSON><PERSON>, <PERSON><PERSON>, sans-serif;
            margin: 20px;
        }
        h1 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 24px;
        }
        .company-name {
            text-align: center;
            font-size: 18px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 14px;
        }
        th, td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            font-size: 14px;
        }
        .signature {
            display: flex;
            gap: 50px;
            margin-top: 50px;
        }
        .signature-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .signature-line {
            width: 150px;
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
        }
        @media print {
            .no-print {
                display: none;
            }
            body {
                margin: 0;
                padding: 15px;
            }
            button {
                display: none;
            }
            @page {
                size: A4;
                margin: 1cm;
            }
        }
    </style>
</head>
<body>
    <div class="company-name">{{ firm_name }}</div>
    <h1>办公费用审批表</h1>
    
    <table>
        <thead>
            <tr>
                <th width="12%">费用发生日期</th>
                <th width="10%">申请人</th>
                <th width="28%">用途</th>
                <th width="10%">金额(元)</th>
                <th width="25%">说明</th>
                <th width="15%">审批人</th>
            </tr>
        </thead>
        <tbody id="expenseTableBody">
            <!-- 数据将通过JavaScript填充 -->
        </tbody>
        <tfoot>
            <tr>
                <td colspan="3" style="text-align: right;"><strong>合计金额：</strong></td>
                <td id="totalAmount" style="font-weight: bold;"></td>
                <td colspan="2"></td>
            </tr>
        </tfoot>
    </table>
    
    <div class="signature">
        <div class="signature-item">
            <div class="signature-line"></div>
            <div>申请人签名</div>
        </div>
        <div class="signature-item">
            <div class="signature-line"></div>
            <div>审批人签名</div>
        </div>
        <div class="signature-item">
            <div class="signature-line"></div>
            <div>财务签名</div>
        </div>
    </div>
    
    <div class="footer">
        <p>打印日期: <span id="printDate"></span></p>
        <p>本表格一式两份，一份交财务，一份自行保管</p>
    </div>
    
    <div class="no-print" style="margin-top: 20px; text-align: center;">
        <button onclick="window.print()" style="padding: 8px 16px; margin-right: 10px; background-color: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">打印</button>
        <button onclick="window.close()" style="padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
    </div>
    
    <script>
        // 从URL参数获取数据
        const urlParams = new URLSearchParams(window.location.search);
        const expensesData = JSON.parse(decodeURIComponent(urlParams.get('data')));
        
        // 填充表格
        const tableBody = document.getElementById('expenseTableBody');
        let totalAmount = 0;
        
        expensesData.forEach(expense => {
            const row = document.createElement('tr');
            
            // 格式化日期
            const expenseDate = new Date(expense.expense_date);
            const formattedDate = `${expenseDate.getFullYear()}-${(expenseDate.getMonth() + 1).toString().padStart(2, '0')}-${expenseDate.getDate().toString().padStart(2, '0')}`;
            
            // 累加总金额
            totalAmount += parseFloat(expense.amount);
            
            // 处理用户显示名称
            function getUserDisplayName(user) {
                if (!user) return '未知用户';
                if (user.last_name || user.first_name) {
                    return `${user.last_name || ''}${user.first_name || ''}`.trim();
                }
                return user.username || `用户ID: ${user.id}`;
            }
            
            row.innerHTML = `
                <td>${formattedDate}</td>
                <td>${getUserDisplayName(expense.applicant)}</td>
                <td>${expense.purpose}</td>
                <td>${parseFloat(expense.amount).toFixed(2)}</td>
                <td>${expense.description || ''}</td>
                <td>${expense.approver ? getUserDisplayName(expense.approver) : ''}</td>
            `;
            tableBody.appendChild(row);
        });
        
        // 设置总金额
        document.getElementById('totalAmount').textContent = totalAmount.toFixed(2);
        
        // 设置打印日期
        const now = new Date();
        document.getElementById('printDate').textContent = `${now.getFullYear()}-${(now.getMonth() + 1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')}`;
        
        // 自动打印功能
        window.onload = function() {
            // 延迟一秒后自动弹出打印对话框
            setTimeout(function() {
                window.print();
            }, 1000);
        };
    </script>
</body>
</html> 