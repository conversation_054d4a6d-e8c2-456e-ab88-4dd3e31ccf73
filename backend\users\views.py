from rest_framework import viewsets, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.contrib.auth.models import User
from django.db import models
from .models import UserProfile
from .serializers import UserSerializer, UserProfileSerializer

class UserViewSet(viewsets.ModelViewSet):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        # 对于列表操作，所有认证用户都可以查看所有用户（用于添加协作律师等功能）
        if self.action == 'list':
            base_queryset = User.objects.filter(is_active=True)
            
            # 检查是否请求显示所有用户（包括隐藏的用户）
            show_all_users = self.request.query_params.get('allusers', '').lower() == 'true'
            
            if not show_all_users:
                # 只显示未隐藏的用户（is_hidden=False 或 profile不存在的用户）
                base_queryset = base_queryset.filter(
                    models.Q(profile__is_hidden=False) | 
                    models.Q(profile__isnull=True)
                )
            
            return base_queryset.order_by('username')
        # 对于其他操作（详情、更新、删除），管理员可以操作所有用户，普通用户只能操作自己
        elif self.request.user.is_staff:
            return User.objects.all()
        else:
            return User.objects.filter(id=self.request.user.id)

class UserProfileViewSet(viewsets.ModelViewSet):
    queryset = UserProfile.objects.all()
    serializer_class = UserProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_staff:
            return UserProfile.objects.all()
        return UserProfile.objects.filter(user=self.request.user)

@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def current_user(request):
    """
    获取当前登录用户的信息
    """
    user = request.user    
    # 确保用户的profile存在
    try:
        profile = user.profile
        # 如果用户是管理员或超级用户，确保有审批权限
        if user.is_staff or user.is_superuser:
            if not profile.can_approve_cases:
                profile.can_approve_cases = True
                profile.save()
    except UserProfile.DoesNotExist:
        # 如果profile不存在，创建一个新的
        profile = UserProfile.objects.create(
            user=user,
            can_approve_cases=user.is_staff or user.is_superuser  # 如果用户是管理员或超级用户，给予审批权限
        )
    
    # 确保从数据库重新获取最新的用户信息
    user = User.objects.select_related('profile').get(id=user.id)
    serializer = UserSerializer(user)
    return Response(serializer.data)
