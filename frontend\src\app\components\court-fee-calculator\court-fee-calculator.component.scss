.court-fee-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .court-fee-title {
    text-align: center;
    margin-bottom: 20px;
    color: #3f51b5;
    font-weight: 500;
  }
  
  .court-fee-card {
    margin-bottom: 30px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    
    &.standard-card {
      margin-top: 40px;
    }
  }
  
  .case-type-selector {
    margin-bottom: 20px;
  }
  
  .case-form {
    margin-bottom: 30px;
    
    h2 {
      color: #3f51b5;
      font-size: 1.5rem;
      margin-bottom: 15px;
      font-weight: 500;
    }
  }
  
  .form-row {
    margin-bottom: 20px;
  }
  
  .full-width {
    width: 100%;
  }
  
  .property-radio {
    display: flex;
    flex-direction: row;
    margin-bottom: 15px;
    
    .mat-radio-button {
      margin-right: 20px;
    }
  }
  
  .result-section {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 8px;
    
    h3 {
      color: #3f51b5;
      font-size: 1.3rem;
      margin-bottom: 15px;
      font-weight: 500;
    }
    
    .mat-divider {
      margin-bottom: 15px;
    }
    
    .fee-result {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      background-color: #e3f2fd;
      border-radius: 5px;
      margin-bottom: 20px;
      
      .fee-label {
        font-weight: 500;
        font-size: 1.1rem;
      }
      
      .fee-value {
        font-weight: 700;
        font-size: 1.5rem;
        color: #f44336;
      }
    }
    
    .fee-notice {
      margin-top: 20px;
      padding: 15px;
      background-color: #fff3e0;
      border-radius: 5px;
      
      p {
        font-weight: 500;
        margin-bottom: 10px;
      }
      
      ol {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 5px;
          color: #795548;
        }
      }
    }
  }
  
  .standard-section {
    margin-bottom: 30px;
    
    h3 {
      color: #3f51b5;
      font-size: 1.3rem;
      margin-bottom: 15px;
      font-weight: 500;
    }
    
    h4 {
      color: #455a64;
      font-size: 1.1rem;
      margin: 15px 0 10px;
      font-weight: 500;
    }
    
    p {
      margin-bottom: 15px;
      line-height: 1.6;
    }
    
    ol, ul {
      padding-left: 20px;
      margin-bottom: 15px;
      
      li {
        margin-bottom: 5px;
        line-height: 1.6;
      }
    }
  }
} 