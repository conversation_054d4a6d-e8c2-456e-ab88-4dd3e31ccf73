import os
import tempfile
import zipfile
from django.http import HttpResponse
from django.conf import settings
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser
from .models import TemplatePackage, TemplateFile
from .serializers import TemplatePackageSerializer, TemplateFileSerializer, TemplateKeyValueSerializer
from docx import Document
from openpyxl import load_workbook

class TemplatePackageViewSet(viewsets.ModelViewSet):
    queryset = TemplatePackage.objects.all()
    serializer_class = TemplatePackageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return TemplatePackage.objects.filter(created_by=self.request.user)

    @action(detail=True, methods=['post'], parser_classes=[MultiPartParser, FormParser])
    def upload_file(self, request, pk=None):
        template = self.get_object()

        file_obj = request.FILES.get('file')
        if not file_obj:
            return Response({'error': '没有提供文件'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件类型
        filename = file_obj.name
        file_ext = filename.split('.')[-1].lower()

        if file_ext not in ['docx', 'xlsx']:
            return Response({'error': '仅支持.docx和.xlsx文件'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查是否已存在同名文件
        existing_file = TemplateFile.objects.filter(
            template_package=template,
            original_filename=filename
        ).first()

        if existing_file:
            # 如果存在同名文件，删除旧文件并更新记录
            # 注意：这会自动删除文件系统中的旧文件
            existing_file.file.delete()
            existing_file.file = file_obj
            existing_file.save()
            serializer = TemplateFileSerializer(existing_file)
        else:
            # 如果不存在同名文件，创建新记录
            template_file = TemplateFile(
                template_package=template,
                file=file_obj,
                file_type=file_ext,
                original_filename=filename
            )
            template_file.save()
            serializer = TemplateFileSerializer(template_file)

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def generate_files(self, request, pk=None):
        """
        生成文档文件
        处理模板变量替换规则：
        1. 对于存在的模板变量，即使值为空也会进行替换（替换为空字符串）
        2. 对于不存在的模板变量，保留原文不做替换
        """
        template = self.get_object()
        serializer = TemplateKeyValueSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        replacements = serializer.validated_data['replacements']
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, f"{template.name}_filled.zip")

        # 创建zip文件
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            for template_file in template.files.all():
                file_path = template_file.file.path
                output_filename = template_file.original_filename

                # 根据文件类型处理
                if template_file.file_type == 'docx':
                    filled_file_path = self._fill_docx(file_path, replacements, temp_dir, output_filename)
                elif template_file.file_type == 'xlsx':
                    filled_file_path = self._fill_xlsx(file_path, replacements, temp_dir, output_filename)
                else:
                    continue

                if filled_file_path:
                    zipf.write(filled_file_path, os.path.basename(filled_file_path))

        # 返回zip文件
        with open(zip_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/zip')
            response['Content-Disposition'] = f'attachment; filename="{template.name}_filled.zip"'
            return response

    def _fill_docx(self, file_path, replacements, temp_dir, output_filename):
        """填充Word文档模板"""
        doc = Document(file_path)

        # 替换段落中的占位符，保留原来的字体格式
        for paragraph in doc.paragraphs:
            self._replace_in_paragraph(paragraph, replacements)

        # 替换表格中的占位符，保留原来的字体格式
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        self._replace_in_paragraph(paragraph, replacements)

        output_path = os.path.join(temp_dir, output_filename)
        doc.save(output_path)
        return output_path

    def _replace_in_paragraph(self, paragraph, replacements):
        """在段落中替换文本并保留原格式"""
        for key, value in replacements.items():
            placeholder = f"{{{{ {key} }}}}"
            # 确保 value 为字符串类型，处理 None 的情况
            replacement_value = str(value) if value is not None else ''            # 持续替换直到段落中不再包含该占位符
            while placeholder in paragraph.text:
                runs = paragraph.runs
                
                # 标记是否完成了一次替换
                replaced = False
                
                for i, run in enumerate(runs):
                    if placeholder in run.text:
                        # 保留原来的格式，仅替换文本内容
                        run.text = run.text.replace(placeholder, replacement_value)
                        replaced = True
                        break  # 跳出当前run循环，重新检查段落

                if not replaced:
                    # 处理复杂情况：占位符跨多个runs
                    # 1. 查找占位符的开始和结束位置
                    start_run_index = -1
                    end_run_index = -1
                    placeholder_len = len(placeholder)

                    # 构建完整段落文本和每个run的开始位置索引
                    full_text = ""
                    run_positions = []

                    for i, run in enumerate(runs):
                        run_positions.append(len(full_text))
                        full_text += run.text

                    # 查找占位符在全文中的位置
                    placeholder_pos = full_text.find(placeholder)
                    if placeholder_pos != -1:
                        placeholder_end = placeholder_pos + placeholder_len

                        # 确定占位符跨越哪些runs
                        for i, pos in enumerate(run_positions):
                            if start_run_index == -1 and pos > placeholder_pos:
                                start_run_index = i - 1
                            if end_run_index == -1 and pos > placeholder_end:
                                end_run_index = i - 1
                                break

                        # 如果占位符在最后一个run中结束
                        if end_run_index == -1:
                            end_run_index = len(runs) - 1

                        # 替换文本
                        if start_run_index == end_run_index:
                            # 如果实际上占位符在单个run中（罕见情况）
                            start_pos_in_run = placeholder_pos - run_positions[start_run_index]
                            runs[start_run_index].text = (
                                runs[start_run_index].text[:start_pos_in_run] +
                                replacement_value +
                                runs[start_run_index].text[start_pos_in_run + placeholder_len:]
                            )
                        else:
                            # 占位符跨多个runs
                            # 计算在第一个run中的开始位置
                            start_pos_in_run = placeholder_pos - run_positions[start_run_index]

                            # 处理第一个run - 保留开始部分，添加替换值
                            runs[start_run_index].text = (
                                runs[start_run_index].text[:start_pos_in_run] + replacement_value
                            )

                            # 清空中间的runs
                            for i in range(start_run_index + 1, end_run_index):
                                runs[i].text = ""

                            # 处理最后一个run - 只保留占位符后面的部分
                            # 计算占位符在最后一个run中结束的位置
                            end_pos_in_last_run = placeholder_end - run_positions[end_run_index]
                            if end_pos_in_last_run < len(runs[end_run_index].text):
                                # 保留占位符后面的文本
                                runs[end_run_index].text = runs[end_run_index].text[end_pos_in_last_run:]
                            else:
                                # 占位符正好在最后一个run末尾结束
                                runs[end_run_index].text = ""

    def _fill_xlsx(self, file_path, replacements, temp_dir, output_filename):
        """填充Excel表格模板"""
        wb = load_workbook(file_path)

        for sheet_name in wb.sheetnames:
            sheet = wb[sheet_name]
            for row in sheet.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, str):
                        for key, value in replacements.items():
                            placeholder = f"{{{{ {key} }}}}"
                            # 确保 value 为字符串类型，处理 None 的情况
                            replacement_value = str(value) if value is not None else ''
                            if placeholder in cell.value:
                                cell.value = cell.value.replace(placeholder, replacement_value)

        output_path = os.path.join(temp_dir, output_filename)
        wb.save(output_path)
        return output_path

    @action(detail=True, methods=['delete'], url_path='files/(?P<file_id>[^/.]+)')
    def delete_file(self, request, pk=None, file_id=None):
        """删除模板文件"""
        template = self.get_object()

        try:
            file = TemplateFile.objects.get(id=file_id, template_package=template)
        except TemplateFile.DoesNotExist:
            return Response({'error': '文件不存在'}, status=status.HTTP_404_NOT_FOUND)

        # 删除文件
        file_name = file.original_filename
        file.delete()  # 这会同时删除数据库记录和文件系统中的文件

        return Response({'message': f'文件 {file_name} 已成功删除'}, status=status.HTTP_200_OK)
