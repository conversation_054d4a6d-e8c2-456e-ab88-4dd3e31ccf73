from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db import transaction
from .models import WithdrawalRequest, FinanceRecord
from .serializers import WithdrawalRequestSerializer
from cases.models import Case


class WithdrawalRequestViewSet(viewsets.ModelViewSet):
    serializer_class = WithdrawalRequestSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """根据用户权限返回不同的查询集"""
        user = self.request.user
        
        # 如果是行政人员，可以看到所有申请
        if hasattr(user, 'profile') and user.profile.has_case_admin_approval_permission():
            return WithdrawalRequest.objects.all()
        else:
            # 普通用户只能看到自己的申请
            return WithdrawalRequest.objects.filter(requester=user)
    
    def perform_create(self, serializer):
        """创建提款申请时设置申请人"""
        serializer.save(requester=self.request.user)
    
    def destroy(self, request, *args, **kwargs):
        """删除提款申请"""
        instance = self.get_object()
        
        # 检查权限：只有申请人可以删除自己的未审批申请
        if instance.requester != request.user:
            return Response(
                {'error': '您只能删除自己的申请'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if instance.status != 'PENDING':
            return Response(
                {'error': '只能删除未审批的申请'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """审批通过提款申请"""
        withdrawal = self.get_object()
        
        # 检查权限
        if not (hasattr(request.user, 'profile') and 
                request.user.profile.has_case_admin_approval_permission()):
            return Response(
                {'error': '您没有审批权限'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if withdrawal.status != 'PENDING':
            return Response(
                {'error': '该申请已被处理'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取审批时的额外参数
        deduction_tax = request.data.get('deduction_tax', 0)
        other_deductions = request.data.get('other_deductions', '')
        remarks = request.data.get('remarks', withdrawal.remarks)
        
        # 验证扣税金额
        try:
            deduction_tax = float(deduction_tax) if deduction_tax else 0
            if deduction_tax < 0:
                return Response(
                    {'error': '应扣税费不能为负数'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            if deduction_tax > float(withdrawal.amount):
                return Response(
                    {'error': '应扣税费不能超过申请金额'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except (ValueError, TypeError):
            return Response(
                {'error': '应扣税费格式不正确'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            with transaction.atomic():
                # 批准申请并传递额外参数
                if withdrawal.approve(request.user, deduction_tax, other_deductions, remarks):
                    return Response({
                        'message': '申请已批准',
                        'withdrawal': WithdrawalRequestSerializer(withdrawal, context={'request': request}).data
                    })
                else:
                    return Response(
                        {'error': '审批失败，请检查申请状态'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
        except Exception as e:
            return Response(
                {'error': f'处理失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """拒绝提款申请"""
        withdrawal = self.get_object()
        
        # 检查权限
        if not (hasattr(request.user, 'profile') and 
                request.user.profile.has_case_admin_approval_permission()):
            return Response(
                {'error': '您没有审批权限'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        if withdrawal.status != 'PENDING':
            return Response(
                {'error': '该申请已被处理'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        withdrawal.reject(request.user)
        
        return Response({
            'message': '申请已拒绝',
            'withdrawal': WithdrawalRequestSerializer(withdrawal, context={'request': request}).data
        })

    @action(detail=False, methods=['get'], url_path='case/(?P<case_id>[^/.]+)/status')
    def case_status(self, request, case_id=None):
        """获取特定案件的提款状态"""
        user = request.user
        
        # 检查当前用户是否有该案件的待审批提款申请
        has_pending_request = WithdrawalRequest.objects.filter(
            case_id=case_id,
            requester=user,
            status='PENDING'
        ).exists()
        
        return Response({
            'has_pending_request': has_pending_request
        })
    
    @action(detail=False, methods=['get'], url_path='case/(?P<case_id>[^/.]+)/available_amount')
    def case_available_amount(self, request, case_id=None):
        """获取特定案件的可提取金额"""
        user = request.user
        
        try:
            serializer = WithdrawalRequestSerializer(context={'request': request})
            max_amount = serializer._calculate_max_withdrawal_amount(
                Case.objects.get(id=case_id),
                user
            )
            
            return Response({
                'available_amount': float(max_amount),
                'has_available_amount': max_amount > 0
            })
        except Case.DoesNotExist:
            return Response(
                {'error': '案件不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'error': f'计算失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=['post'])
    def print(self, request, pk=None):
        """打印提款申请单"""
        withdrawal = self.get_object()
        
        # 检查是否已审批（只有已审批的申请才能打印）
        if withdrawal.status != 'APPROVED':
            return Response(
                {'error': '只有已批准的申请才能打印'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 返回提款申请数据用于打印
        serializer = WithdrawalRequestSerializer(withdrawal, context={'request': request})
        return Response(serializer.data) 