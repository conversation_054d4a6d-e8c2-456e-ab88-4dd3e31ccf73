.withdrawal-list {
  padding: 20px;
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h2 {
      margin: 0;
      color: #333;
    }
    
    .refresh-btn {
      padding: 8px 16px;
      background-color: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      
      &:hover {
        background-color: #0056b3;
      }
    }
  }
  
  .loading {
    text-align: center;
    padding: 40px;
    color: #666;
  }
  
  .error {
    background-color: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 20px;
  }
  
  .empty-state {
    text-align: center;
    padding: 40px;
    color: #666;
    
    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }
  }
  
  .withdrawal-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #eee;
    }
    
    th {
      background-color: #f8f9fa;
      font-weight: 600;
      color: #333;
    }
    
    tr:hover {
      background-color: #f8f9fa;
    }
    
    .amount {
      font-weight: 600;
      color: #28a745;
    }
    
    .deduction-tax {
      font-weight: 600;
      color: #dc3545;
    }
    
    .final-amount {
      font-weight: 700;
      color: #155724;
      background-color: #d4edda;
      padding: 8px;
      border-radius: 4px;
    }
    
    .other-deductions {
      max-width: 150px;
      word-wrap: break-word;
      font-size: 12px;
    }
    
    .remarks {
      max-width: 200px;
      word-wrap: break-word;
      font-size: 12px;
    }
    
    .status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      
      &.status-pending {
        background-color: #fff3cd;
        color: #856404;
      }
      
      &.status-approved {
        background-color: #d4edda;
        color: #155724;
      }
      
      &.status-rejected {
        background-color: #f8d7da;
        color: #721c24;
      }
    }
    
    .actions {
      display: flex;
      gap: 8px;
      
      .btn {
        padding: 4px 8px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        
        &.btn-approve {
          background-color: #28a745;
          color: white;
          
          &:hover {
            background-color: #218838;
          }
        }
        
        &.btn-reject {
          background-color: #dc3545;
          color: white;
          
          &:hover {
            background-color: #c82333;
          }
        }
        
        &.btn-print {
          background-color: #17a2b8;
          color: white;
          
          &:hover {
            background-color: #138496;
          }
        }
        
        &.btn-delete {
          background-color: #6c757d;
          color: white;
          
          &:hover {
            background-color: #5a6268;
          }
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .withdrawal-list {
    padding: 10px;
    
    .withdrawal-table {
      font-size: 14px;
      
      th, td {
        padding: 8px 4px;
      }
      
      .actions {
        flex-direction: column;
        gap: 4px;
        
        .btn {
          font-size: 11px;
          padding: 3px 6px;
        }
      }
    }
  }
} 