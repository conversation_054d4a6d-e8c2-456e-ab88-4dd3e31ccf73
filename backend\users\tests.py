from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth.models import User
from .models import UserProfile
from rest_framework import status
import uuid

class UserAPITests(APITestCase):
    def setUp(self):
        """
        测试前创建基础数据
        """
        # 使用唯一标识符创建唯一的用户名
        unique_id = str(uuid.uuid4())[:8]
        
        # 创建普通用户
        self.normal_user = User.objects.create_user(
            username=f'normal_user_{unique_id}',
            password='normal123',
            email=f'normal_{unique_id}@example.com'
        )
        
        # 创建管理员用户
        self.admin_user = User.objects.create_user(
            username=f'admin_user_{unique_id}',
            password='admin123',
            email=f'admin_{unique_id}@example.com',
            is_staff=True
        )
        
        # 创建超级用户
        self.super_user = User.objects.create_superuser(
            username=f'super_user_{unique_id}',
            password='super123',
            email=f'super_{unique_id}@example.com'
        )

        # 修改管理员用户的profile
        admin_profile = self.admin_user.profile
        admin_profile.can_approve_cases = True
        admin_profile.save()

        # 客户端
        self.client = APIClient()

    def test_normal_user_me(self):
        """测试普通用户的/me/接口"""
        self.client.force_authenticate(user=self.normal_user)
        response = self.client.get('/api/users/me/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], self.normal_user.username)
        self.assertEqual(response.data['profile']['can_approve_cases'], False)

    def test_admin_user_me(self):
        """测试管理员用户的/me/接口"""
        self.client.force_authenticate(user=self.admin_user)
        response = self.client.get('/api/users/me/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], self.admin_user.username)
        self.assertEqual(response.data['profile']['can_approve_cases'], True)

    def test_super_user_me(self):
        """测试超级用户的/me/接口"""
        self.client.force_authenticate(user=self.super_user)
        response = self.client.get('/api/users/me/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], self.super_user.username)
        self.assertEqual(response.data['profile']['can_approve_cases'], True)

    def test_auto_create_profile(self):
        """测试自动创建用户档案"""
        # 创建一个没有profile的新用户
        unique_id = str(uuid.uuid4())[:8]
        new_user = User.objects.create_user(
            username=f'new_user_{unique_id}',
            password='new123',
            email=f'new_{unique_id}@example.com'
        )
        
        self.client.force_authenticate(user=new_user)
        response = self.client.get('/api/users/me/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue('profile' in response.data)
        self.assertFalse(response.data['profile']['can_approve_cases'])
        
        # 验证数据库中确实创建了profile
        self.assertTrue(UserProfile.objects.filter(user=new_user).exists())

    def test_update_profile_permission(self):
        """测试更新用户档案的权限"""
        # 获取用户的profile
        profile = self.normal_user.profile
        profile.can_approve_cases = True
        profile.save()
        
        self.client.force_authenticate(user=self.normal_user)
        response = self.client.get('/api/users/me/')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['profile']['can_approve_cases'])

    def test_unauthenticated_access(self):
        """测试未认证用户访问"""
        response = self.client.get('/api/users/me/')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
