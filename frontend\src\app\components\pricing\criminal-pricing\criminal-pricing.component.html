<div class="pricing-calculator">
  <h1>刑事诉讼收费计算器</h1>
  
  <mat-card class="calculator-card">
    <mat-card-content>
      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>案件阶段</mat-label>
          <mat-select [value]="stage" (selectionChange)="updateStage($event.value)">
            <mat-option *ngFor="let s of stages" [value]="s.value">
              {{s.viewValue}}
            </mat-option>
          </mat-select>
          <mat-hint>请选择案件所处的阶段</mat-hint>
        </mat-form-field>
      </div>
      
      <div *ngIf="result" class="result-section">
        <h2>收费标准</h2>
        <mat-divider></mat-divider>
        
        <div class="fee-range">
          <div class="fee-item total-fee">
            <span class="fee-label">收费范围：</span>
            <span class="fee-value">{{ result.fee.min.toLocaleString() }}元 - {{ result.fee.max.toLocaleString() }}元</span>
          </div>
        </div>
        
        <h3>收费明细</h3>
        <mat-list>
          <mat-list-item *ngFor="let detail of result.details">
            {{ detail }}
          </mat-list-item>
        </mat-list>
      </div>
    </mat-card-content>
  </mat-card>
</div> 