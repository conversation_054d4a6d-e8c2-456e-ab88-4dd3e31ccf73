# Generated by Django 5.0.3 on 2025-06-22 17:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0036_add_non_litigation_client_party_type'),
    ]

    operations = [
        # 修改模型选项
        migrations.AlterModelOptions(
            name='letterrecord',
            options={'ordering': ['sequence_number', '-created_at'], 'verbose_name': '函件登记', 'verbose_name_plural': '函件登记'},
        ),
        # 添加letter_number字段
        migrations.AddField(
            model_name='letterrecord',
            name='letter_number',
            field=models.IntegerField(blank=True, help_text='函件编号，按年份和函件类型分组', null=True, verbose_name='函数编号'),
        ),
        # 修改sequence_number字段属性，允许null和blank
        migrations.AlterField(
            model_name='letterrecord',
            name='sequence_number',
            field=models.IntegerField(blank=True, help_text='审批后的数字序号', null=True, verbose_name='序号'),
        ),
        # 更新唯一约束
        migrations.AlterUniqueTogether(
            name='letterrecord',
            unique_together={('year', 'letter_number', 'letter_type')},
        ),
    ]
