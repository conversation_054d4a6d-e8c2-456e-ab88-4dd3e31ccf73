from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User, Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.urls import reverse
from django.utils.html import format_html
from .models import UserProfile

class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户档案'
    # 排除旧版审批属性，不允许显示和编辑
    exclude = ('can_approve_cases',)
    max_num = 1  # 确保只有一个UserProfile
    min_num = 1  # 确保至少有一个UserProfile

    # 显示相关字段并添加只读字段
    readonly_fields = ('get_finance_permission', 'created_at', 'updated_at')

    def get_finance_permission(self, obj):
        """显示财务权限状态"""
        if obj and obj.user:
            return obj.has_finance_permission()
        return False
    get_finance_permission.short_description = '财务管理权限'
    get_finance_permission.boolean = True

    def get_queryset(self, request):
        """确保返回正确的UserProfile查询集"""
        qs = super().get_queryset(request)
        return qs.select_related('user')

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """确保外键字段正确处理"""
        if db_field.name == "user":
            kwargs["queryset"] = User.objects.all()
        return super().formfield_for_foreignkey(db_field, request, **kwargs)

    def get_or_create_instance(self, request, obj=None):
        """确保UserProfile实例存在，避免重复创建"""
        if obj:
            try:
                return obj.profile
            except UserProfile.DoesNotExist:
                return UserProfile(user=obj)
        return UserProfile()

    def save_model(self, request, obj, form, change):
        """保存时确保不会创建重复的UserProfile"""
        if obj.user_id:
            # 使用get_or_create避免重复创建
            profile, created = UserProfile.objects.get_or_create(
                user=obj.user,
                defaults={
                    'is_hidden': getattr(obj, 'is_hidden', False)
                }
            )
            if not created:
                # 如果已存在，更新字段
                profile.is_hidden = getattr(obj, 'is_hidden', profile.is_hidden)
                profile.save()
            return profile
        else:
            super().save_model(request, obj, form, change)

class UserProfileAdmin(admin.ModelAdmin):
    """独立的UserProfile管理界面"""
    list_display = ('user', 'get_finance_permission', 'get_case_admin_permission', 
                    'get_case_director_permission', 'created_at', 'updated_at')
    list_filter = ('created_at', 'updated_at')
    search_fields = ('user__username', 'user__first_name', 'user__last_name')
    readonly_fields = ('get_finance_permission', 'get_case_admin_permission', 
                      'get_case_director_permission', 'created_at', 'updated_at')
    
    # 排除旧版字段
    exclude = ('can_approve_cases',)
    
    def get_finance_permission(self, obj):
        """显示财务权限状态"""
        return obj.has_finance_permission()
    get_finance_permission.short_description = '财务管理权限'
    get_finance_permission.boolean = True
    
    def get_case_admin_permission(self, obj):
        """显示行政审批权限状态"""
        return obj.has_case_admin_approval_permission()
    get_case_admin_permission.short_description = '行政审批权限'
    get_case_admin_permission.boolean = True
    
    def get_case_director_permission(self, obj):
        """显示主任审批权限状态"""
        return obj.has_case_director_approval_permission()
    get_case_director_permission.short_description = '主任审批权限'
    get_case_director_permission.boolean = True

class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'is_staff',
                    'get_case_admin_permission', 'get_case_director_permission',
                    'get_finance_permission', 'user_permissions_link', 'user_groups_link')

    def save_model(self, request, obj, form, change):
        """保存用户时确保UserProfile存在"""
        super().save_model(request, obj, form, change)
        # 确保UserProfile存在
        UserProfile.objects.get_or_create(user=obj)

    def save_related(self, request, form, formsets, change):
        """保存相关对象时处理UserProfile"""
        # 先确保UserProfile存在
        UserProfile.objects.get_or_create(user=form.instance)
        super().save_related(request, form, formsets, change)
    
    def get_case_admin_permission(self, obj):
        return obj.has_perm('cases.can_admin_approve_case')
    get_case_admin_permission.short_description = '行政审批权限'
    get_case_admin_permission.boolean = True
    
    def get_case_director_permission(self, obj):
        return obj.has_perm('cases.can_director_approve_case')
    get_case_director_permission.short_description = '主任审批权限'
    get_case_director_permission.boolean = True
    
    def get_finance_permission(self, obj):
        """显示财务权限状态"""
        return obj.has_perm('finance.can_manage_finance_records')
    get_finance_permission.short_description = '财务管理权限'
    get_finance_permission.boolean = True
    
    def user_permissions_link(self, obj):
        """提供直接链接到用户权限编辑页面"""
        url = reverse('admin:auth_user_change', args=[obj.pk]) + '#user_permissions-tab'
        return format_html('<a href="{}">编辑权限</a>', url)
    user_permissions_link.short_description = '用户权限'
    
    def user_groups_link(self, obj):
        """提供直接链接到用户组编辑页面"""
        url = reverse('admin:auth_user_change', args=[obj.pk]) + '#groups-tab'
        return format_html('<a href="{}">编辑组</a>', url)
    user_groups_link.short_description = '用户组'
    
    def save_model(self, request, obj, form, change):
        """保存用户模型时的额外操作"""
        super().save_model(request, obj, form, change)
        # 确保权限系统一致性，但不要自动迁移旧版属性
        # 这里可以添加任何需要在保存用户时执行的额外逻辑

# 自定义Group管理界面，添加更多信息
class GroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'get_permissions_count', 'get_users_count')
    filter_horizontal = ('permissions',)
    
    def get_permissions_count(self, obj):
        return obj.permissions.count()
    get_permissions_count.short_description = '权限数量'
    
    def get_users_count(self, obj):
        return obj.user_set.count()
    get_users_count.short_description = '用户数量'

# 自定义Permission管理界面
class PermissionAdmin(admin.ModelAdmin):
    list_display = ('name', 'codename', 'content_type', 'get_users_count')
    list_filter = ('content_type__app_label',)
    search_fields = ('name', 'codename')
    
    def get_users_count(self, obj):
        return User.objects.filter(user_permissions=obj).count()
    get_users_count.short_description = '直接授权用户数'

# 取消注册原来的模型
admin.site.unregister(User)
admin.site.unregister(Group)

# 使用自定义的Admin类重新注册
admin.site.register(User, UserAdmin)
admin.site.register(UserProfile, UserProfileAdmin)  # 注册独立的UserProfile管理
admin.site.register(Group, GroupAdmin)
admin.site.register(Permission, PermissionAdmin)
