.finance-report-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

// 权限警告样式
.permission-warning {
  margin-bottom: 20px;
  
  .warning-content {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #f57c00;
    font-size: 16px;
    
    mat-icon {
      color: #f57c00;
    }
  }
}

// 头部卡片样式
.header-card {
  margin-bottom: 20px;
  
  mat-card-title {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #1976d2;
  }
  
  .filter-form {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 16px;
    
    mat-form-field {
      min-width: 200px;
    }
    
    button {
      height: 40px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// 加载状态样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  gap: 16px;
  
  p {
    color: #666;
    font-size: 16px;
  }
}

// 总结区域样式
.summary-card {
  margin-bottom: 20px;
  
  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 16px;
  }
  
  .summary-item {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    background-color: #fafafa;
    
    .summary-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }
    
    .summary-value {
      font-size: 24px;
      font-weight: 600;
      
      &.primary {
        color: #1976d2;
      }
      
      &.success {
        color: #388e3c;
      }
      
      &.warning {
        color: #f57c00;
      }
      
      &.danger {
        color: #d32f2f;
      }
    }
    
    .summary-sub-text {
      font-size: 12px;
      font-weight: 400;
      margin-top: 4px;
    }
  }
  
  // 欠款警告样式
  .debt-alert {
    border: 2px solid #f44336 !important;
    background-color: #ffebee !important;
    
    .summary-label {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #d32f2f !important;
      
      .warning-icon {
        color: #f44336;
        font-size: 18px;
        width: 18px;
        height: 18px;
        animation: pulse-warning 2s infinite;
      }
    }
  }
}

// 表格卡片样式
.table-card {
  .table-container {
    overflow-x: auto;
    margin-top: 16px;
  }
  
  .finance-table {
    width: 100%;
    min-width: 1000px;
    
    th {
      font-weight: 600;
      background-color: #f5f5f5;
      border-bottom: 2px solid #e0e0e0;
    }
    
    td {
      border-bottom: 1px solid #e0e0e0;
      padding: 12px 8px;
    }
    
    // 欠款状态样式
    .debt-container {
      display: flex;
      align-items: center;
      gap: 8px;
      
      .warning-icon {
        color: #f44336;
        font-size: 20px;
        width: 20px;
        height: 20px;
        animation: pulse-warning 2s infinite;
      }
    }
    
    .positive-debt {
      .debt-amount {
        color: #388e3c;
        font-weight: 500;
      }
      
      .debt-container {
        background-color: #e8f5e8;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
    
    .negative-debt {
      .debt-amount {
        color: #d32f2f;
        font-weight: 600;
      }
      
      .debt-container {
        background-color: #ffebee;
        padding: 4px 8px;
        border-radius: 4px;
        border-left: 3px solid #f44336;
      }
      
      .debt-warning {
        background-color: #f44336;
        color: white;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 11px;
        font-weight: 600;
        text-transform: uppercase;
        animation: blink-warning 1.5s infinite;
      }
    }
    
    .zero-debt {
      .debt-amount {
        color: #666;
      }
      
      .debt-container {
        background-color: #f5f5f5;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
    
    .debt-status {
      font-size: 12px;
      margin-left: 4px;
    }
    
    // 警告动画
    @keyframes pulse-warning {
      0% { opacity: 1; }
      50% { opacity: 0.5; }
      100% { opacity: 1; }
    }
    
    @keyframes blink-warning {
      0% { background-color: #f44336; }
      50% { background-color: #e53935; }
      100% { background-color: #f44336; }
    }
  }
}

// 无数据样式
.no-data {
  text-align: center;
  padding: 40px;
  color: #666;
  
  mat-icon {
    font-size: 48px;
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }
  
  p {
    font-size: 16px;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .finance-report-container {
    padding: 10px;
  }
  
  .filter-form {
    flex-direction: column;
    align-items: stretch !important;
    gap: 16px !important;
    
    mat-form-field {
      min-width: auto;
    }
  }
  
  .summary-grid {
    grid-template-columns: 1fr !important;
  }
  
  .finance-table {
    min-width: 800px;
  }
} 