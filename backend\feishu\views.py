import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from django.shortcuts import redirect
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.views import APIView

from law_firm.jwt import CustomTokenObtainPairSerializer

from .models import FeishuSession

User = get_user_model()

LOGIN_PATH = "/login"


class FeishuCallback(APIView):
    def post(self, request: Request):
        """
        {
            "challenge": "ajls384kdjxxxx",  // 应用需要在响应中原样返回的值
            "token": "xxxxxx",              // 即 Verification Token
            "type": "url_verification"      // 表示这是一个验证请求
        }
        """
        data = request.data
        if data.get("type") == "url_verification":
            return Response({"challenge": data.get("challenge")})

    def get(self, request):
        code = request.query_params.get("code")

        # 获取用户访问令牌
        if code:
            app_id = settings.FEISHU_CONFIG["APP_ID"]
            app_secret = settings.FEISHU_CONFIG["APP_SECRET"]

            # 根据当前请求动态构造重定向URI，确保与前端保持一致
            scheme = "https" if request.is_secure() else "http"
            host = request.META.get("HTTP_X_FORWARDED_HOST") or request.get_host()
            redirect_uri = f"{scheme}://{host}/api/feishu/callback"

            print(f"Dynamic redirect_uri: {redirect_uri}")
            print(f"Request scheme: {scheme}, host: {host}")

            # 获取 tenant_access_token
            tenant_response = requests.post(
                "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal",
                json={"app_id": app_id, "app_secret": app_secret},
            )
            tenant_data = tenant_response.json()
            if tenant_data["code"] != 0:
                print(f"获取用户信息失败: http error {tenant_data['msg']}")

                return redirect(LOGIN_PATH)

            tenant_access_token = tenant_data["tenant_access_token"]
            # tenant_expire = tenant_data["expire"]

            # 使用 code 获取 user_access_token 以及 refresh_token
            oauth_token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
            headers = {
                "Authorization": f"Bearer {tenant_access_token}",
                "Content-Type": "application/json",
            }
            request_params = {
                "grant_type": "authorization_code",
                "code": code,
                "client_id": app_id,
                "client_secret": app_secret,
                "redirect_uri": redirect_uri,
            }
            response = requests.post(
                oauth_token_url, json=request_params, headers=headers
            )
            oauth_data = response.json()

            if response.status_code != 200:
                print(f"获取用户信息失败: http error {response.status_code}")

                # 参数错误，打印请求的参数
                if response.status_code == 400:
                    print(f"参数错误: {request_params}")

                return redirect(LOGIN_PATH)

            if oauth_data.get("code") != 0:
                err_code = oauth_data.get("code")
                err_msg = oauth_data.get("msg")
                print(f"获取用户信息失败: http error {err_code} {err_msg}")
                return redirect(LOGIN_PATH)

            """
            response

            ```json
            {
                "code": 0,
                "msg": "success",
                "data": {
                    "name": "zhangsan",
                    "en_name": "zhangsan",
                    "avatar_url": "www.feishu.cn/avatar/icon",
                    "avatar_thumb": "www.feishu.cn/avatar/icon_thumb",
                    "avatar_middle": "www.feishu.cn/avatar/icon_middle",
                    "avatar_big": "www.feishu.cn/avatar/icon_big",
                    "open_id": "ou-caecc734c2e3328a62489fe0648c4b98779515d3",
                    "union_id": "on-d89jhsdhjsajkda7828enjdj328ydhhw3u43yjhdj",
                    "email": "<EMAIL>",
                    "enterprise_email": "<EMAIL>",
                    "user_id": "5d9bdxxx",
                    "mobile": "+86130002883xx",
                    "tenant_key": "736588c92lxf175d",
                    "employee_no": "111222333"
                }
            }
            ```
            """
            # 获取用户详细信息
            user_detail_url = "https://open.feishu.cn/open-apis/authen/v1/user_info"
            headers = {"Authorization": f'Bearer {oauth_data["access_token"]}'}
            response = requests.get(user_detail_url, headers=headers).json()

            # handle error
            if response.get("code") != 0:
                code = response.get("code")
                msg = response.get("msg")
                print(f"获取用户信息失败: http error {code} {msg}")
                return redirect(LOGIN_PATH)

            user_data = response["data"]
            """
            {
                'avatar_big': 'https://s1-imfile.feishucdn.com/static-resource/v1/v3_00k2_b220de50-7d9a-4334-8903-3b3b0de2e05g~?image_size=640x640&cut_type=&quality=&format=image&sticker_format=.webp',
                'avatar_middle': 'https://s1-imfile.feishucdn.com/static-resource/v1/v3_00k2_b220de50-7d9a-4334-8903-3b3b0de2e05g~?image_size=240x240&cut_type=&quality=&format=image&sticker_format=.webp',
                'avatar_thumb': 'https://s3-imfile.feishucdn.com/static-resource/v1/v3_00k2_b220de50-7d9a-4334-8903-3b3b0de2e05g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp',
                'avatar_url': 'https://s3-imfile.feishucdn.com/static-resource/v1/v3_00k2_b220de50-7d9a-4334-8903-3b3b0de2e05g~?image_size=72x72&cut_type=&quality=&format=image&sticker_format=.webp',
                'email': '',
                'en_name': 'Alex',
                'mobile': '+8617000104149',
                'name': 'Alex',
                'open_id': 'ou_4fd49ca04d8d9052880f72987ddbc471',
                'tenant_key': '140b434f30dad75f',
                'union_id': 'on_7fd725ac5293af70d00da431550b8275'
            }
            """
            email = user_data["email"]
            mobile = user_data["mobile"]
            username = mobile if mobile else email
            if not username:
                print(f"获取用户信息失败: 没有邮箱或手机号")
                return redirect(LOGIN_PATH, error="没有邮箱或手机号，无法创建用户")

            # 创建或更新用户
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    "username": username,
                    "first_name": user_data.get("name", ""),
                    "email": email,
                    "is_staff": True,
                    "is_active": True,
                    "is_superuser": False,
                },
            )

            # 创建飞书会话
            feishu_session = FeishuSession.objects.create(
                access_token=oauth_data["access_token"],
                code=code,
                feishu_user_data=user_data,
                user=user,
                user_created=created,
            )

            # 使用自定义序列化器生成JWT令牌，包含更多用户信息
            refresh = CustomTokenObtainPairSerializer.get_token(user)
            access_token = refresh.access_token

            # 设置JWT令牌到响应的cookies和headers中
            response = redirect("/")

            # Set cookies for API calls (httpOnly)
            response.set_cookie(
                "access_token",
                str(access_token),
                max_age=60 * 60 * 24,  # 1 day
                httponly=True,
                samesite="Lax",
                secure=settings.DEBUG is False,
            )
            response.set_cookie(
                "refresh_token",
                str(refresh),
                max_age=60 * 60 * 24 * 7,  # 7 days
                httponly=True,
                samesite="Lax",
                secure=settings.DEBUG is False,
            )

            # Set non-httpOnly cookie for frontend access
            response.set_cookie(
                "auth_token",
                str(access_token),
                max_age=60 * 60 * 24,  # 1 day
                httponly=False,
                samesite="Lax",
                secure=settings.DEBUG is False,
            )

            # 登录成功，重定向到首页
            return response
