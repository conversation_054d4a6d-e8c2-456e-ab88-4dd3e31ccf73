from django.db import models
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType

class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    can_approve_cases = models.BooleanField(default=False, verbose_name='可以审批案件（旧版）', help_text='旧版权限设置，将逐步被新权限系统替代')
    is_hidden = models.BooleanField(default=False, verbose_name='隐藏用户', help_text='隐藏的用户在用户列表中不显示（除非明确请求）')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = '用户档案'
        verbose_name_plural = '用户档案'

    def __str__(self):
        return f"{self.user.username}的档案"

    def has_case_admin_approval_permission(self):
        """判断用户是否有行政审批权限"""
        return self.user.has_perm('cases.can_admin_approve_case')
    
    def has_case_director_approval_permission(self):
        """判断用户是否有主任审批权限"""
        return self.user.has_perm('cases.can_director_approve_case')
    
    def has_finance_permission(self):
        """判断用户是否有财务管理权限"""
        return self.user.has_perm('finance.can_manage_finance_records')

# 初始化创建权限
def create_permissions():
    from cases.models import Case
    content_type = ContentType.objects.get_for_model(Case)
    
    Permission.objects.get_or_create(
        codename='can_admin_approve_case',
        name='行政审批',
        content_type=content_type,
    )
    
    Permission.objects.get_or_create(
        codename='can_director_approve_case',
        name='主任审批',
        content_type=content_type,
    )

# 迁移旧权限到新权限系统
def migrate_approval_permissions(apps, schema_editor):
    UserProfile = apps.get_model('users', 'UserProfile')
    User = apps.get_model('auth', 'User')
    Permission = apps.get_model('auth', 'Permission')
    ContentType = apps.get_model('contenttypes', 'ContentType')
    
    # 获取新权限
    try:
        content_type = ContentType.objects.get(app_label='cases', model='case')
        admin_perm = Permission.objects.get(
            codename='can_admin_approve_case',
            content_type=content_type
        )
        director_perm = Permission.objects.get(
            codename='can_director_approve_case',
            content_type=content_type
        )
        
        # 迁移权限：所有有can_approve_cases=True的用户都获得这两个权限
        for profile in UserProfile.objects.filter(can_approve_cases=True):
            user = profile.user
            user.user_permissions.add(admin_perm)
            user.user_permissions.add(director_perm)
    except (ContentType.DoesNotExist, Permission.DoesNotExist):
        pass
