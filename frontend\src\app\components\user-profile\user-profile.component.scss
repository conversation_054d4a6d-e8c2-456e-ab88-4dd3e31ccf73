.profile-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

.profile-title {
  text-align: center;
  margin-bottom: 2rem;
  color: #3f51b5;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin: 3rem 0;
}

.error-message {
  color: #f44336;
  text-align: center;
  margin: 2rem 0;
  font-size: 1.2rem;
}

.info-section {
  margin: 1.5rem 0;
  
  p {
    margin: 0.8rem 0;
    font-size: 1rem;
  }
}

mat-card-actions {
  padding: 1rem;
  display: flex;
  justify-content: space-around;
}

/* 权限信息区域样式 */
.permissions-section {
  margin-top: 1.5rem;
  
  h3 {
    color: #3f51b5;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    font-weight: 500;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.5rem;
  }
}

.permission-chips {
  margin-bottom: 1.5rem;
  
  mat-chip-set {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  mat-chip {
    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

.permission-details {
  background-color: #f9f9f9;
  padding: 1rem;
  border-radius: 4px;
  
  p {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px dashed #eee;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .permission-status {
    font-weight: 500;
    
    &.has-permission {
      color: #4caf50;
    }
    
    &:not(.has-permission) {
      color: #f44336;
    }
  }
}

/* 权限提示信息 */
.permissions-note {
  margin-top: 1.5rem;
  padding: 1rem;
  background-color: #e3f2fd;
  border-radius: 4px;
  border-left: 4px solid #2196f3;
  
  p {
    display: flex;
    align-items: center;
    margin: 0;
    color: #0d47a1;
  }
  
  .info-icon {
    margin-right: 0.5rem;
    color: #2196f3;
  }
} 