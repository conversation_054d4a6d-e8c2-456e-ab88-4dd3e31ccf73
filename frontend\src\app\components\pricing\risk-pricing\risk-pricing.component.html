<div class="pricing-calculator">
  <h1>风险代理收费计算器</h1>
  
  <mat-card class="calculator-card">
    <mat-card-content>
      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>案件标的额（元）</mat-label>
          <input matInput type="number" min="1" [value]="amount" (input)="updateAmount($event)">
          <mat-hint>请输入案件的标的额</mat-hint>
        </mat-form-field>
      </div>
      
      <div *ngIf="result" class="result-section">
        <h2>收费标准</h2>
        <mat-divider></mat-divider>
        
        <div class="fee-range">
          <div class="fee-item">
            <span class="fee-label">风险代理最高比例：</span>
            <span class="fee-value">{{ result.maxPercentage }}%</span>
          </div>
          <div class="fee-item total-fee">
            <span class="fee-label">最高收费金额：</span>
            <span class="fee-value">{{ result.maxFee.toLocaleString() }}元</span>
          </div>
        </div>
        
        <div class="risk-notice">
          <p>根据相关规定，某些类型的案件不得采用风险代理方式，包括：</p>
          <ul>
            <li>刑事诉讼案件</li>
            <li>行政诉讼案件</li>
            <li>国家赔偿案件</li>
            <li>群体性诉讼案件</li>
            <li>婚姻继承案件</li>
            <li>请求给予社会保险待遇、最低生活保障待遇等案件</li>
          </ul>
        </div>
        
        <h3>收费明细</h3>
        <mat-list>
          <mat-list-item *ngFor="let detail of result.details">
            {{ detail }}
          </mat-list-item>
        </mat-list>
      </div>
    </mat-card-content>
  </mat-card>
</div> 