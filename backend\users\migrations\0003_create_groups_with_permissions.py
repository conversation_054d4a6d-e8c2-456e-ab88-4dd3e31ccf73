# Generated by Django 5.0.3 

from django.db import migrations


def create_groups_and_assign_permissions(apps, schema_editor):
    Group = apps.get_model('auth', 'Group')
    Permission = apps.get_model('auth', 'Permission')
    ContentType = apps.get_model('contenttypes', 'ContentType')
    
    # 使用指定的数据库
    db_alias = schema_editor.connection.alias
    
    # 获取权限
    try:
        # 确保ContentType存在
        content_type, created = ContentType.objects.using(db_alias).get_or_create(
            app_label='cases', 
            model='case',
            defaults={'model': 'case'}
        )
        if created:
            print(f"创建了ContentType: {content_type}")
            
        # 获取权限，如果不存在则创建
        admin_perm, created = Permission.objects.using(db_alias).get_or_create(
            codename='can_admin_approve_case',
            defaults={
                'name': '行政审批',
                'content_type': content_type,
            }
        )
        if created:
            print(f"创建了权限: {admin_perm.name}")
            
        director_perm, created = Permission.objects.using(db_alias).get_or_create(
            codename='can_director_approve_case',
            defaults={
                'name': '主任审批',
                'content_type': content_type,
            }
        )
        if created:
            print(f"创建了权限: {director_perm.name}")
        
        # 创建主任组
        director_group, created = Group.objects.using(db_alias).get_or_create(name='主任')
        if created:
            print("主任组已创建")
        else:
            print("主任组已存在")
        
        # 手动创建组权限关联，确保使用正确的数据库
        GroupPermission = Group.permissions.through
        director_relation, created = GroupPermission.objects.using(db_alias).get_or_create(
            group=director_group,
            permission=director_perm
        )
        if created:
            print("主任组权限已分配")
        else:
            print("主任组权限已存在")
        
        # 创建行政人员组
        admin_group, created = Group.objects.using(db_alias).get_or_create(name='行政人员')
        if created:
            print("行政人员组已创建")
        else:
            print("行政人员组已存在")
        
        # 手动创建组权限关联，确保使用正确的数据库
        admin_relation, created = GroupPermission.objects.using(db_alias).get_or_create(
            group=admin_group,
            permission=admin_perm
        )
        if created:
            print("行政人员组权限已分配")
        else:
            print("行政人员组权限已存在")
            
    except Exception as e:
        print(f"迁移创建组过程中出错: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")


def reverse_groups_and_permissions(apps, schema_editor):
    Group = apps.get_model('auth', 'Group')
    
    # 使用指定的数据库
    db_alias = schema_editor.connection.alias
    
    # 删除组
    try:
        director_deleted = Group.objects.using(db_alias).filter(name='主任').delete()
        admin_deleted = Group.objects.using(db_alias).filter(name='行政人员').delete()
        print(f"已删除主任组（{director_deleted[0]}个）和行政人员组（{admin_deleted[0]}个）")
    except Exception as e:
        print(f"回退迁移过程中出错: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_add_permissions_system'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.RunPython(
            create_groups_and_assign_permissions,
            reverse_groups_and_permissions
        ),
    ] 