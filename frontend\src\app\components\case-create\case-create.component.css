.container {
  max-width: 1000px;
  margin: 20px auto;
  padding: 0 20px;
}

.main-card {
  margin-bottom: 30px;
}

mat-card-header {
  margin-bottom: 20px;
}

mat-card-title {
  font-size: 24px;
  color: #3f51b5;
}

.form-section {
  margin-bottom: 30px;
}

.form-section h3 {
  color: #3f51b5;
  margin-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
  padding-bottom: 8px;
}

.full-width {
  width: 100%;
  margin-bottom: 15px;
}

.row {
  display: flex;
  gap: 16px;
  margin-bottom: 15px;
}

.row mat-form-field {
  flex: 1;
}

textarea {
  min-height: 100px;
}

mat-card-actions {
  padding: 16px;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

button {
  margin-left: 8px;
}

/* 当事人列表样式 */
.parties-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 20px;
}

.party-card {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  width: calc(50% - 8px);
  box-shadow: 0 1px 3px rgba(0,0,0,0.12);
  position: relative;
}

.party-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.party-name {
  font-weight: 500;
  margin-right: 8px;
  font-size: 16px;
}

.party-type-badge {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 12px;
  margin-right: 8px;
}

.party-type-badge.natural {
  background-color: #e3f2fd;
  color: #2196f3;
}

.party-type-badge.legal {
  background-color: #e8f5e9;
  color: #4caf50;
}

.party-info {
  font-size: 14px;
  color: #666;
}

.party-info p {
  margin: 4px 0;
}

.delete-btn {
  position: absolute;
  top: 8px;
  right: 8px;
}

.client-badge {
  background-color: #4caf50;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 8px;
}

.add-party-button {
  margin-top: 10px;
  margin-bottom: 20px;
}

.party-validation {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #fff3e0;
  border-radius: 4px;
  border-left: 4px solid #ff9800;
}

/* 自定义属性样式 */
.custom-properties-section {
  margin-top: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.custom-properties-section h3 {
  margin-top: 0;
  color: #3f51b5;
  font-size: 18px;
}

.custom-properties-list {
  margin-bottom: 20px;
}

.custom-property-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin-bottom: 8px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.property-name {
  font-weight: 500;
  margin-right: 16px;
  color: #3f51b5;
}

.property-value {
  flex: 1;
  color: #666;
}

.add-property-form {
  display: flex;
  gap: 16px;
  align-items: flex-start;
}

.add-property-form mat-form-field {
  flex: 1;
}

.add-property-form button {
  margin-top: 4px;
}

@media (max-width: 768px) {
  .parties-list {
    display: block;
  }
  
  .party-card {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .add-property-form {
    flex-direction: column;
    gap: 8px;
  }
  
  .add-property-form button {
    width: 100%;
  }
}

@media (max-width: 599px) {
  .row {
    flex-direction: column;
    gap: 0;
  }
  
  .container {
    padding: 0 10px;
  }
}

.party-role-selection {
  margin-top: 15px;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.party-type-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin: 10px 0;
  
  mat-radio-button {
    margin-bottom: 8px;
  }
}

@media (max-width: 599px) {
  .party-type-group {
    flex-direction: column;
    gap: 6px;
  }
}

/* 表单操作按钮样式 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
}

.is-client-checkbox {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
}

.is-client-checkbox mat-hint {
  font-size: 0.75rem;
  margin-top: 4px;
  color: rgba(0, 0, 0, 0.6);
}

/* 复选框字段样式 */
.checkbox-field {
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
}

.checkbox-field mat-checkbox {
  margin-bottom: 4px;
}

.checkbox-field .helper-text {
  font-size: 0.75rem;
  color: rgba(0, 0, 0, 0.6);
  margin-left: 32px; /* 与checkbox对齐 */
  margin-top: 4px;
  line-height: 1.4;
} 