# Python
*.py[cod]
__pycache__/
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
mediafiles/
static/

# Virtual Environment
venv/
ENV/
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Project specific
postgres_data/
# .env # keep it for all devs
.env.prod
tmp/

# Secrets
*.pem
*.key

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Frontend
frontend/node_modules/
frontend/dist/
frontend/.angular/
frontend/.nginx/
