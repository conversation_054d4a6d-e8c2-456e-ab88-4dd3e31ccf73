import { Component, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatDialogModule, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-case-create-dialog',
  templateUrl: './case-create-dialog.component.html',
  styleUrls: ['./case-create-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule
  ]
})
export class CaseCreateDialogComponent {
  caseForm!: FormGroup;

  constructor(
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<CaseCreateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.initForm();
  }

  private initForm(): void {
    // 创建表单
    this.caseForm = this.fb.group({
      // 基本信息
      case_cause: ['', [Validators.required, Validators.maxLength(200)]],
      case_type: ['', [Validators.required]],
      case_number: ['', [Validators.maxLength(50)]],
      is_sensitive: [false], // 是否为重大敏感案件
      is_risk_agency: [false], // 是否风险代理

      // 当事人信息
      client: ['', [Validators.required]],
      phone: ['', [Validators.required]],
      opposing_party: [''],

      // 案件信息
      accepting_authority: [''],
      contract_number: [''],
      court_case_number: [''],
      litigation_request: [''],
      litigation_amount: [''],
      agreed_attorney_fee: [''],
      agency_stage: [''],
      case_summary: ['', [Validators.required]],
      comments: ['']
    });

    // 如果有传入的数据，填充表单
    if (this.data) {
      const formData = {
        case_cause: this.data.case_cause,
        case_type: this.data.case_type,
        case_number: this.data.case_number,
        client: this.data.content['委托人'],
        phone: this.data.content['联系电话'],
        opposing_party: this.data.content['对方当事人'],
        accepting_authority: this.data.content['受理机关'],
        contract_number: this.data.content['委托合同编号'],
        court_case_number: this.data.content['法院案件编号'],
        litigation_request: this.data.content['诉讼请求'],
        litigation_amount: this.data.content['诉讼标的额'],
        agreed_attorney_fee: this.data.content['商定律师费'],
        agency_stage: this.data.content['代理阶段'],
        case_summary: this.data.content['案情摘要'],
        comments: this.data.content['备注']
      };
      this.caseForm.patchValue(formData);
    }
  }

  resetForm(): void {
    this.initForm();
  }

  onSubmit(): void {
    if (this.caseForm.valid) {
      const formValue = this.caseForm.value;

      // 构建包含content对象的数据结构
      const caseData = {
        case_cause: formValue.case_cause,
        case_type: formValue.case_type,
        is_sensitive: formValue.is_sensitive,
        is_risk_agency: formValue.is_risk_agency,
        content: {
          '案件类型': formValue.case_type,
          '委托人': formValue.client,
          '联系电话': formValue.phone,
          '对方当事人': formValue.opposing_party,
          '受理机关': formValue.accepting_authority,
          '委托合同编号': formValue.contract_number,
          '法院案件编号': formValue.court_case_number,
          '诉讼请求': formValue.litigation_request,
          '诉讼标的额': formValue.litigation_amount,
          '商定律师费': formValue.agreed_attorney_fee,
          '代理阶段': formValue.agency_stage,
          '案情摘要': formValue.case_summary,
          '备注': formValue.comments
        }
      };

      // 发送数据但不关闭对话框
      this.dialogRef.close({ data: caseData, keepOpen: true });

      // 显示提示消息
      this.snackBar.open('案件创建请求已发送', '确定', {
        duration: 3000,
        horizontalPosition: 'center',
        verticalPosition: 'bottom'
      });
    }
  }

  onCreateCase(caseData: any): void {
    // 这个方法将被父组件调用
    this.dialogRef.close({ data: caseData, keepOpen: true });
  }

  onCancel(): void {
    // 关闭对话框时重置表单
    this.resetForm();
    this.dialogRef.close({ keepOpen: false });
  }
}
