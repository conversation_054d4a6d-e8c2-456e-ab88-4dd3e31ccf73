.finance-management {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  
  .header {
    margin-bottom: 30px;
    
    h1 {
      margin: 0;
      color: #333;
      font-size: 28px;
      font-weight: 600;
    }
  }
  
  .tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 30px;
    
    .tab-button {
      padding: 12px 24px;
      border: none;
      background: none;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      color: #6c757d;
      border-bottom: 3px solid transparent;
      transition: all 0.3s ease;
      
      &:hover {
        color: #007bff;
        background-color: #f8f9fa;
      }
      
      &.active {
        color: #007bff;
        border-bottom-color: #007bff;
        background-color: #f8f9fa;
      }
    }
  }
  
  .tab-content {
    .tab-pane {
      animation: fadeIn 0.3s ease-in-out;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .finance-management {
    padding: 10px;
    
    .header {
      margin-bottom: 20px;
      
      h1 {
        font-size: 24px;
      }
    }
    
    .tabs {
      margin-bottom: 20px;
      
      .tab-button {
        padding: 10px 16px;
        font-size: 14px;
      }
    }
  }
} 