import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { PricingService, NonLitigationPricingResult } from '../../../services/pricing.service';

interface ServiceType {
  value: string;
  viewValue: string;
}

@Component({
  selector: 'app-non-litigation-pricing',
  templateUrl: './non-litigation-pricing.component.html',
  styleUrls: ['./non-litigation-pricing.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatRadioModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatListModule
  ]
})
export class NonLitigationPricingComponent {
  amount: number = 0;
  serviceType: string = 'yearlyConsult'; // 默认常年法律顾问
  isFinancialType: boolean = false; // 是否是涉及财产的服务类型
  result: NonLitigationPricingResult | null = null;
  
  serviceTypes: ServiceType[] = [
    { value: 'financial', viewValue: '涉及财产关系的案件' },
    { value: 'yearlyConsult', viewValue: '担任常年法律顾问' },
    { value: 'specialConsult', viewValue: '担任专项法律顾问' },
    { value: 'legalConsult', viewValue: '法律咨询服务' },
    { value: 'legalAnalysis', viewValue: '法律论证服务' },
    { value: 'legalTraining', viewValue: '法律培训服务' },
    { value: 'legalWitness', viewValue: '律师见证服务' },
    { value: 'legalOpinion', viewValue: '出具律师函、法律意见书等' },
    { value: 'legalDocument', viewValue: '起草、审查、修改合同或文件' },
    { value: 'legalProceeding', viewValue: '代书、代办公证等事项' },
    { value: 'legalNegotiation', viewValue: '参与谈判、调解等事务' },
  ];
  
  constructor(private pricingService: PricingService) {
    this.calculateFee();
  }
  
  calculateFee(): void {
    this.result = this.pricingService.calculateNonLitigationFee(this.amount, this.serviceType);
  }
  
  updateAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.amount = value > 0 ? value : 0;
    this.calculateFee();
  }
  
  updateServiceType(serviceType: string): void {
    this.serviceType = serviceType;
    this.isFinancialType = serviceType === 'financial';
    this.calculateFee();
  }
} 