<div class="finance-report-container">
  <!-- 权限提示 -->
  <div *ngIf="!hasAdminPermission" class="permission-warning">
    <mat-card>
      <mat-card-content>
        <div class="warning-content">
          <mat-icon>warning</mat-icon>
          <span>您需要行政人员权限才能访问财务报告</span>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- 主要内容 -->
  <div *ngIf="hasAdminPermission">
    <!-- 标题和筛选 -->
    <mat-card class="header-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>assessment</mat-icon>
          财务报告
        </mat-card-title>
        <mat-card-subtitle>事务所年度财务统计分析</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="filterForm" class="filter-form">
          <mat-form-field appearance="outline">
            <mat-label>选择年份</mat-label>
            <mat-select formControlName="year" (selectionChange)="loadReport()">
              <mat-option *ngFor="let year of yearOptions" [value]="year">
                {{ year }}年
              </mat-option>
            </mat-select>
          </mat-form-field>
          
          <button mat-raised-button color="primary" type="button" (click)="loadReport()">
            <mat-icon>refresh</mat-icon>
            刷新数据
          </button>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- 加载状态 -->
    <div *ngIf="isLoading" class="loading-container">
      <mat-spinner diameter="40"></mat-spinner>
      <p>正在加载财务报告...</p>
    </div>

    <!-- 报告内容 -->
    <div *ngIf="reportData && !isLoading">
      <!-- 总结区域 -->
      <mat-card class="summary-card">
        <mat-card-header>
          <mat-card-title>{{ reportData.year }}年度财务总结</mat-card-title>
        </mat-card-header>
        
        <mat-card-content>
          <div class="summary-grid">
            <div class="summary-item">
              <div class="summary-label">事务所当年总案件收入</div>
              <div class="summary-value primary">{{ formatCurrency(reportData.summary.total_case_income) }}</div>
            </div>
            
            <div class="summary-item">
              <div class="summary-label">归属于事务所的总收入 (12%)</div>
              <div class="summary-value success">{{ formatCurrency(reportData.summary.firm_income) }}</div>
            </div>
            
            <div class="summary-item">
              <div class="summary-label">办公费用支出总和</div>
              <div class="summary-value warning">{{ formatCurrency(reportData.summary.total_office_expense) }}</div>
            </div>
            
            <div class="summary-item" [ngClass]="{'debt-alert': reportData.summary.debt_lawyers_count > 0}">
              <div class="summary-label">
                <mat-icon *ngIf="reportData.summary.debt_lawyers_count > 0" class="warning-icon">warning</mat-icon>
                欠款律师情况
              </div>
              <div class="summary-value" [ngClass]="reportData.summary.debt_lawyers_count > 0 ? 'danger' : 'success'">
                {{ reportData.summary.debt_lawyers_count }}/{{ reportData.summary.total_lawyers_count }}
                <div class="summary-sub-text">
                  {{ reportData.summary.debt_lawyers_count > 0 ? '名律师有欠款' : '无律师欠款' }}
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- 律师财务数据表格 -->
      <mat-card class="table-card">
        <mat-card-header>
          <mat-card-title>律师财务明细</mat-card-title>
          <mat-card-subtitle>各律师的收入、支出及欠款情况</mat-card-subtitle>
        </mat-card-header>
        
        <mat-card-content>
          <div class="table-container">
            <table mat-table [dataSource]="reportData.lawyers" class="finance-table">

              <!-- 律师姓名列 -->
              <ng-container matColumnDef="lawyer_name">
                <th mat-header-cell *matHeaderCellDef>律师姓名</th>
                <td mat-cell *matCellDef="let lawyer">
                  <strong>{{ getUserDisplayName(lawyer.lawyer) }}</strong>
                </td>
              </ng-container>

              <!-- 案件收入列 -->
              <ng-container matColumnDef="case_income">
                <th mat-header-cell *matHeaderCellDef>案件收入</th>
                <td mat-cell *matCellDef="let lawyer">
                  {{ formatCurrency(lawyer.case_income) }}
                </td>
              </ng-container>

              <!-- 归属事务所收入列 -->
              <ng-container matColumnDef="firm_share">
                <th mat-header-cell *matHeaderCellDef>归属事务所收入 (12%)</th>
                <td mat-cell *matCellDef="let lawyer">
                  {{ formatCurrency(lawyer.firm_share) }}
                </td>
              </ng-container>

              <!-- 提款总和列 -->
              <ng-container matColumnDef="withdrawals">
                <th mat-header-cell *matHeaderCellDef>提款总和</th>
                <td mat-cell *matCellDef="let lawyer">
                  {{ formatCurrency(lawyer.withdrawals) }}
                </td>
              </ng-container>

              <!-- 缴款总和列 -->
              <ng-container matColumnDef="payments">
                <th mat-header-cell *matHeaderCellDef>缴款总和</th>
                <td mat-cell *matCellDef="let lawyer">
                  {{ formatCurrency(lawyer.payments) }}
                </td>
              </ng-container>

              <!-- 应缴费用总和列 -->
              <ng-container matColumnDef="task_expenses">
                <th mat-header-cell *matHeaderCellDef>应缴费用总和</th>
                <td mat-cell *matCellDef="let lawyer">
                  {{ formatCurrency(lawyer.task_expenses) }}
                </td>
              </ng-container>

              <!-- 欠款列 -->
              <ng-container matColumnDef="debt">
                <th mat-header-cell *matHeaderCellDef>欠款状况</th>
                <td mat-cell *matCellDef="let lawyer" 
                    [ngClass]="getDebtStatusClass(lawyer.debt)">
                  <div class="debt-container">
                    <mat-icon *ngIf="lawyer.debt < 0" class="warning-icon">warning</mat-icon>
                    <span class="debt-amount">
                      {{ formatCurrency(getAbsValue(lawyer.debt)) }}
                      <span class="debt-status">
                        <span *ngIf="lawyer.debt > 0">(盈余)</span>
                        <span *ngIf="lawyer.debt < 0" class="debt-warning">(欠款)</span>
                        <span *ngIf="lawyer.debt === 0">(平衡)</span>
                      </span>
                    </span>
                  </div>
                </td>
              </ng-container>

              <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
              <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
            </table>
          </div>
          
          <!-- 无数据提示 -->
          <div *ngIf="reportData.lawyers.length === 0" class="no-data">
            <mat-icon>info</mat-icon>
            <p>{{ reportData.year }}年暂无财务数据</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>
</div> 