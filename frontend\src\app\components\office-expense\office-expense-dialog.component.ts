import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NgIf, DatePipe } from '@angular/common';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { FinanceService } from '../../services/finance.service';
import { OfficeExpense, OfficeExpenseCreateDto } from '../../interfaces/office-expense.interface';

@Component({
  selector: 'app-office-expense-dialog',
  standalone: true,
  imports: [
    NgIf, DatePipe, ReactiveFormsModule,
    MatDialogModule, MatButtonModule, MatFormFieldModule,
    MatInputModule, MatDatepickerModule, MatNativeDateModule,
    MatIconModule, MatSnackBarModule
  ],
  template: `
    <h2 mat-dialog-title>{{ isEditMode ? '编辑费用' : '新增费用' }}</h2>
    
    <form [formGroup]="form" (ngSubmit)="onSubmit()">
      <div mat-dialog-content>
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>费用发生日期</mat-label>
          <input matInput [matDatepicker]="picker" formControlName="expenseDate" required>
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
          <mat-error *ngIf="form.get('expenseDate')?.hasError('required')">
            费用发生日期不能为空
          </mat-error>
        </mat-form-field>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>用途</mat-label>
          <input matInput formControlName="purpose" placeholder="请输入费用用途" required>
          <mat-error *ngIf="form.get('purpose')?.hasError('required')">
            用途不能为空
          </mat-error>
          <mat-error *ngIf="form.get('purpose')?.hasError('maxlength')">
            用途不能超过100个字符
          </mat-error>
        </mat-form-field>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>金额</mat-label>
          <input matInput type="number" formControlName="amount" placeholder="请输入费用金额" required>
          <mat-error *ngIf="form.get('amount')?.hasError('required')">
            金额不能为空
          </mat-error>
          <mat-error *ngIf="form.get('amount')?.hasError('min')">
            金额必须大于0
          </mat-error>
        </mat-form-field>
        
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>说明</mat-label>
          <textarea matInput formControlName="description" placeholder="请输入费用说明" rows="3"></textarea>
        </mat-form-field>
      </div>
      
      <div mat-dialog-actions align="end">
        <button mat-button type="button" [mat-dialog-close]="false">取消</button>
        <button mat-raised-button color="primary" type="submit" [disabled]="form.invalid">保存</button>
      </div>
    </form>
  `,
  styles: [`
    .full-width {
      width: 100%;
      margin-bottom: 15px;
    }
  `]
})
export class OfficeExpenseDialogComponent implements OnInit {
  form: FormGroup;
  isEditMode: boolean = false;
  
  constructor(
    private fb: FormBuilder,
    private financeService: FinanceService,
    private dialogRef: MatDialogRef<OfficeExpenseDialogComponent>,
    private snackBar: MatSnackBar,
    @Inject(MAT_DIALOG_DATA) public data: OfficeExpense | undefined
  ) {
    this.form = this.fb.group({
      expenseDate: [null, Validators.required],
      purpose: ['', [Validators.required, Validators.maxLength(100)]],
      amount: [null, [Validators.required, Validators.min(0.01)]],
      description: ['']
    });
  }
  
  ngOnInit(): void {
    if (this.data && this.data.id) {
      this.isEditMode = true;
      
      // 设置表单初始值
      this.form.patchValue({
        expenseDate: new Date(this.data.expense_date),
        purpose: this.data.purpose,
        amount: this.data.amount,
        description: this.data.description || ''
      });
    }
  }
  
  onSubmit(): void {
    if (this.form.invalid) return;
    
    const formValue = this.form.value;
    
    const expenseData: OfficeExpenseCreateDto = {
      expense_date: this.formatDate(formValue.expenseDate),
      purpose: formValue.purpose,
      amount: formValue.amount,
      description: formValue.description
    };
    
    if (this.isEditMode && this.data?.id) {
      // 更新费用
      this.financeService.updateOfficeExpense(this.data.id, expenseData).subscribe({
        next: (result) => {
          this.snackBar.open('费用更新成功', '关闭', { duration: 3000 });
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error('费用更新失败', error);
          this.snackBar.open('费用更新失败: ' + (error.error?.detail || '未知错误'), '关闭', {
            duration: 3000
          });
        }
      });
    } else {
      // 创建新费用
      this.financeService.createOfficeExpense(expenseData).subscribe({
        next: (result) => {
          this.snackBar.open('费用创建成功', '关闭', { duration: 3000 });
          this.dialogRef.close(result);
        },
        error: (error) => {
          console.error('费用创建失败', error);
          this.snackBar.open('费用创建失败: ' + (error.error?.detail || '未知错误'), '关闭', {
            duration: 3000
          });
        }
      });
    }
  }
  
  private formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
} 