import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { NavBarComponent } from './components/nav-bar/nav-bar.component';
import { DebtWarningComponent } from './components/shared/debt-warning/debt-warning.component';
import { AuthService } from './services/auth.service';
import { FirmInfoService } from './services/firm-info.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, NavBarComponent, DebtWarningComponent],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {

  constructor(
    private authService: AuthService,
    private firmInfoService: FirmInfoService
  ) {}

  ngOnInit() {
    // FirmInfoService会自动处理域名判断和浏览器标题设置
    
    if (this.authService.isLoggedIn()) {  // 假设您有这个方法检查是否已登录
      this.authService.startAuthStatusCheck();
    }
  }
}
