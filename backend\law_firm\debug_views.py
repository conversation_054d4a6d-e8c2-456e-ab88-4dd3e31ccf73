"""
调试视图 - 用于测试数据库路由功能
"""
from django.http import JsonResponse
from django.conf import settings
from .middleware import get_current_firm
from django.db import connections

def debug_database_info(request):
    """
    调试视图：显示当前请求使用的数据库信息
    """
    host = request.get_host()
    current_db = get_current_firm()
    request_db = getattr(request, 'firm_alias', 'not_set')
    
    # 测试数据库连接
    connection_info = {}
    for db_alias in settings.DATABASES.keys():
        try:
            conn = connections[db_alias]
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                connection_info[db_alias] = {
                    'status': 'connected',
                    'database_name': settings.DATABASES[db_alias]['NAME']
                }
        except Exception as e:
            connection_info[db_alias] = {
                'status': 'error',
                'error': str(e)
            }
    
    return JsonResponse({
        'domain': host,
        'current_db_from_router': current_db,
        'current_db_from_request': request_db,
        'databases_config': list(settings.DATABASES.keys()),
        'connection_status': connection_info,
        'middleware_working': hasattr(request, 'firm_alias'),
    }) 