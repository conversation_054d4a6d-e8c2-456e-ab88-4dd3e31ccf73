# 从Cloudflare的Cf-Visitor头中提取协议信息
# 如果存在Cf-Visitor头，则从JSON中提取scheme
map $http_cf_visitor $forwarded_scheme {
    default $scheme;
    "~*\"scheme\":\"(?<extracted_scheme>https?)\"" $extracted_scheme;
}

server {
    listen 80;
    server_name _; # 通配符，匹配所有域名

    client_max_body_size 90m;
    client_header_buffer_size 512k;
    large_client_header_buffers 4 512k;

    # 前端文件
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
        index index.html index.htm;
    }

    location /static {
        alias /var/www/staticfiles;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    location /media {
        alias /var/www/mediafiles;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # 管理员后台
    location /admin/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $forwarded_scheme;
    }

    # 后端API代理
    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $forwarded_scheme;
    }
}