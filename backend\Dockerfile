FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# debian chinese mirror
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖 - 添加了ReportLab和其他PDF处理包所需的系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    dos2unix \
    poppler-utils \
    libreoffice-writer \
    default-jre \
    python3-dev \
    libpq-dev \
    zlib1g-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install --no-cache-dir -r requirements.txt

# 复制项目文件
COPY . .

# 添加等待脚本
COPY wait-for-it.sh /wait-for-it.sh
# 跟Windows系统兼容
RUN dos2unix /wait-for-it.sh
RUN chmod +x /wait-for-it.sh

# 暴露端口
EXPOSE 8000