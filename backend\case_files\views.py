from django.shortcuts import render, get_object_or_404
from django.db.models import Q
from django.http import HttpResponse, FileResponse
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FormParser, JSONParser

from cases.models import Case
from .models import CaseFileFolder, CaseFile
from .serializers import (
    CaseFileFolderSerializer, 
    CaseFileFolderListSerializer,
    CaseFileSerializer,
    FileMergeRequestSerializer
)
from .utils import extract_zip, get_file_type, merge_pdfs

class CaseFileFolderViewSet(viewsets.ModelViewSet):
    queryset = CaseFileFolder.objects.all()
    serializer_class = CaseFileFolderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """只返回当前用户有权访问的案件的文件夹"""
        # 管理员可以查看所有文件夹
        if self.request.user.is_staff:
            return CaseFileFolder.objects.all()
        
        # 普通用户只能查看自己的案件的文件夹或者他们有审批权限的案件
        return CaseFileFolder.objects.filter(
            Q(case__lawyer=self.request.user) | 
            Q(case__approvals__approver=self.request.user)
        ).distinct()
    
    def get_serializer_class(self):
        """根据action返回适当的序列化器"""
        if self.action == 'list':
            return CaseFileFolderListSerializer
        return CaseFileFolderSerializer
    
    def create(self, request, *args, **kwargs):
        """创建文件夹"""
        # 验证用户是否有权限创建文件夹
        case_id = request.data.get('case')
        case = get_object_or_404(Case, id=case_id)
        
        if case.lawyer != request.user and not request.user.is_staff:
            return Response(
                {'error': '您没有权限在此案件中创建文件夹'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 确保数据中包含必要的字段
        data = request.data.copy()
        if 'parent' not in data or data['parent'] == '':
            data['parent'] = None
        if 'path' not in data or data['path'] == '':
            data['path'] = ""
        
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
    
    @action(detail=True, methods=['delete'])
    def delete_folder(self, request, pk=None):
        """删除文件夹及其所有内容"""
        folder = self.get_object()
        
        # 验证用户权限
        if folder.case.lawyer != request.user and not request.user.is_staff:
            return Response(
                {'error': '您没有权限删除此文件夹'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 递归删除所有子文件夹及文件
        # 注意：Django会自动处理related_name关系，所以不需要手动删除文件
        folder.delete()
        
        return Response(status=status.HTTP_204_NO_CONTENT)
    
    @action(detail=True, methods=['get'])
    def list_files(self, request, pk=None):
        """列出文件夹中的所有文件"""
        folder = self.get_object()
        files = folder.files.all()
        serializer = CaseFileSerializer(files, many=True, context={'request': request})
        return Response(serializer.data)

class CaseFileViewSet(viewsets.ModelViewSet):
    queryset = CaseFile.objects.all()
    serializer_class = CaseFileSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def get_queryset(self):
        """只返回当前用户有权访问的案件的文件"""
        # 管理员可以查看所有文件
        if self.request.user.is_staff:
            return CaseFile.objects.all()
        
        # 普通用户只能查看自己的案件的文件或者他们有审批权限的案件
        return CaseFile.objects.filter(
            Q(case__lawyer=self.request.user) | 
            Q(case__approvals__approver=self.request.user)
        ).distinct()
    
    @action(detail=False, methods=['get'])
    def case_root_files(self, request):
        """获取案件根目录下的文件（不属于任何文件夹的文件）"""
        case_id = request.query_params.get('case')
        if not case_id:
            return Response(
                {'error': '缺少案件ID参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证用户是否有权限访问此案件
        try:
            case = Case.objects.get(id=case_id)
            # 检查用户是否是案件律师、有审批记录，或者是管理员
            has_approval = case.approvals.filter(approver=request.user).exists()
            if case.lawyer != request.user and not has_approval and not request.user.is_staff:
                return Response(
                    {'error': '您没有权限访问此案件的文件'},
                    status=status.HTTP_403_FORBIDDEN
                )
        except Case.DoesNotExist:
            return Response(
                {'error': '案件不存在'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # 获取案件根目录下的文件（folder为null的文件）
        files = CaseFile.objects.filter(case=case_id, folder__isnull=True)
        serializer = self.get_serializer(files, many=True, context={'request': request})
        return Response(serializer.data)
    
    def create(self, request, *args, **kwargs):
        """上传文件"""
        # 获取必要参数
        case_id = request.data.get('case')
        folder_id = request.data.get('folder')
        file_obj = request.FILES.get('file')
        
        if not all([case_id, file_obj]):
            return Response(
                {'error': '缺少必要参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取案件
        case = get_object_or_404(Case, id=case_id)
        
        # 验证用户权限
        if case.lawyer != request.user and not request.user.is_staff:
            return Response(
                {'error': '您没有权限上传文件到此案件'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # 处理ZIP文件
        if file_obj.name.lower().endswith('.zip'):
            # 创建一个临时的文件记录以保存ZIP文件
            # 注意：我们需要先保存ZIP文件以获取其物理文件路径
            file_type = get_file_type(file_obj.name)
            folder = None
            if folder_id:
                folder = get_object_or_404(CaseFileFolder, id=folder_id)
            
            # 创建临时ZIP文件记录
            # 这将把文件保存到服务器的文件系统中
            zip_file_instance = CaseFile.objects.create(
                case=case,
                folder=folder,
                file=file_obj,
                file_type='zip',
                original_filename=file_obj.name,
                uploaded_by=request.user
            )
            
            try:
                # 解压ZIP文件
                # 这将提取ZIP文件中的内容并创建相应的文件夹和文件记录
                # 使用保存后的文件实例中的文件路径，而不是原始的file_obj
                root_folder = extract_zip(zip_file_instance.file, case, request.user)
                
                # 如果extract_zip返回None，表示文件已直接解压到根目录
                # 此时获取案件的所有根目录文件和文件夹
                if root_folder is None:
                    # 获取案件根目录下的所有文件夹
                    folders = CaseFileFolder.objects.filter(case=case, parent__isnull=True)
                    folders_serializer = CaseFileFolderSerializer(folders, many=True, context={'request': request})
                    
                    # 获取案件根目录下的所有文件
                    files = CaseFile.objects.filter(case=case, folder__isnull=True)
                    files_serializer = CaseFileSerializer(files, many=True, context={'request': request})
                    
                    # 返回包含文件和文件夹的响应
                    return Response({
                        'message': 'ZIP文件成功解压到根目录',
                        'folders': folders_serializer.data,
                        'files': files_serializer.data
                    }, status=status.HTTP_201_CREATED)
                else:
                    # 返回包含根文件夹的响应
                    serializer = CaseFileFolderSerializer(root_folder, context={'request': request})
                    return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                return Response(
                    {'error': f'解压ZIP文件失败: {str(e)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            finally:
                # 无论解压成功还是失败，都删除原始ZIP文件
                # 当调用delete()方法时，Django会删除数据库记录和关联的物理文件
                zip_file_instance.delete()  
        
        # 处理普通文件
        file_type = get_file_type(file_obj.name)
        
        # 获取文件夹（如果提供）
        folder = None
        if folder_id:
            folder = get_object_or_404(CaseFileFolder, id=folder_id)
        
        # 创建文件记录
        file_instance = CaseFile.objects.create(
            case=case,
            folder=folder,
            file=file_obj,
            file_type=file_type,
            original_filename=file_obj.name,
            uploaded_by=request.user
        )
        
        serializer = CaseFileSerializer(file_instance, context={'request': request})
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    
    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """下载文件"""
        file_obj = self.get_object()
        file_path = file_obj.file.path
        
        response = FileResponse(open(file_path, 'rb'))
        response['Content-Disposition'] = f'attachment; filename="{file_obj.original_filename}"'
        return response
    
    @action(detail=False, methods=['post'], parser_classes=[JSONParser])
    def merge_pdf(self, request):
        """合并多个文件为一个PDF"""
        serializer = FileMergeRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        file_ids = serializer.validated_data['file_ids']
        output_filename = serializer.validated_data['output_filename']
        start_page_number = serializer.validated_data.get('start_page_number', 1)
        
        # 获取文件对象
        file_list = []
        for file_id in file_ids:
            try:
                file_obj = CaseFile.objects.get(id=file_id)
                # 验证用户权限
                if (file_obj.case.lawyer != request.user and 
                    not request.user.is_staff):
                    return Response(
                        {'error': f'您没有权限访问文件 {file_obj.original_filename}'},
                        status=status.HTTP_403_FORBIDDEN
                    )
                file_list.append(file_obj)
            except CaseFile.DoesNotExist:
                return Response(
                    {'error': f'文件ID {file_id} 不存在'},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        if not file_list:
            return Response(
                {'error': '没有找到有效的文件'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 合并PDF
        try:
            response = merge_pdfs(file_list, output_filename, start_page_number)
            return response
        except Exception as e:
            return Response(
                {'error': f'合并PDF失败: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['put'])
    def update_order(self, request, pk=None):
        """更新文件的排序顺序"""
        file_obj = self.get_object()
        order = request.data.get('order')
        
        if order is None:
            return Response(
                {'error': '缺少order参数'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证用户权限
        if file_obj.case.lawyer != request.user and not request.user.is_staff:
            return Response(
                {'error': '您没有权限修改此文件'},
                status=status.HTTP_403_FORBIDDEN
            )
        
        file_obj.order = order
        file_obj.save()
        
        serializer = CaseFileSerializer(file_obj, context={'request': request})
        return Response(serializer.data)
