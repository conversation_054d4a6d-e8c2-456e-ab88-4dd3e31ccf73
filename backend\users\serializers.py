from rest_framework import serializers
from django.contrib.auth.models import User, Permission
from .models import UserProfile

class UserProfileSerializer(serializers.ModelSerializer):
    has_admin_approval_permission = serializers.SerializerMethodField()
    has_director_approval_permission = serializers.SerializerMethodField()
    has_finance_permission = serializers.SerializerMethodField()
    has_case_admin_approval_permission = serializers.SerializerMethodField()
    has_case_director_approval_permission = serializers.SerializerMethodField()
    
    class Meta:
        model = UserProfile
        fields = ['can_approve_cases', 'is_hidden', 'has_admin_approval_permission', 'has_director_approval_permission', 'has_finance_permission', 'has_case_admin_approval_permission', 'has_case_director_approval_permission']
    
    def get_has_admin_approval_permission(self, obj):
        return obj.has_case_admin_approval_permission()
    
    def get_has_director_approval_permission(self, obj):
        return obj.has_case_director_approval_permission()
    
    def get_has_finance_permission(self, obj):
        return obj.has_finance_permission()
    
    def get_has_case_admin_approval_permission(self, obj):
        return obj.has_case_admin_approval_permission()
    
    def get_has_case_director_approval_permission(self, obj):
        return obj.has_case_director_approval_permission()

class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer()
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'profile', 'permissions']
        read_only_fields = ['id']
    
    def get_permissions(self, obj):
        """获取用户权限的简化列表"""
        return list(obj.get_all_permissions())