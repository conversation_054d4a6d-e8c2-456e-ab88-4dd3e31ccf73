import { Component, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LegacyCaseService } from '../../services/legacy-case.service';
import { NzMessageService, NzMessageModule } from 'ng-zorro-antd/message';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { finalize } from 'rxjs/operators';

@Component({
  selector: 'app-legacy-case-import',
  templateUrl: './legacy-case-import.component.html',
  styleUrls: ['./legacy-case-import.component.scss'],
  standalone: true,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CommonModule,
    NzButtonModule,
    NzIconModule,
    NzCardModule,
    NzDividerModule,
    NzMessageModule
  ]
})
export class LegacyCaseImportComponent {
  isUploading = false;
  selectedFile: File | null = null;
  
  constructor(
    private legacyCaseService: LegacyCaseService,
    private message: NzMessageService
  ) {}

  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const file = input.files[0];
      
      console.log('onFileSelected - 文件信息:', {
        name: file.name,
        size: file.size,
        type: file.type
      });
      
      // 检查文件扩展名
      const isCsv = file.name?.toLowerCase().endsWith('.csv');
      if (!isCsv) {
        this.message.error('只能上传CSV文件！');
        this.selectedFile = null;
        return;
      }
      
      this.selectedFile = file;
    }
  }

  handleUpload(): void {
    if (!this.selectedFile) {
      this.message.error('请先选择要导入的CSV文件！');
      return;
    }

    console.log('handleUpload - 文件信息:', {
      name: this.selectedFile.name,
      size: this.selectedFile.size,
      type: this.selectedFile.type
    });

    // 直接上传文件，不在前端进行日期验证
    this.uploadFile(this.selectedFile);
  }

  private uploadFile(file: File): void {
    this.isUploading = true;
    this.legacyCaseService.importCsv(file)
      .pipe(finalize(() => this.isUploading = false))
      .subscribe({
        next: (response) => {
          this.message.success(response.message);
          this.selectedFile = null;
        },
        error: (error) => {
          console.error('上传文件错误:', error);
          this.message.error(error.error?.error || '导入失败，请检查文件格式是否正确！');
        }
      });
  }
}