import threading
from datetime import datetime
from django.db import transaction
from law_firm.middleware import get_current_firm
from .models import CaseNumber

def get_case_type_code(case_type: str) -> str:
    """
    根据案件类型获取对应的类型代号
    """
    type_map = {
        '民事案件': '民',
        '刑事案件': '刑',
        '行政案件': '行',
        '仲裁案件': '民',
        '其他案件': '非诉'
    }
    return type_map.get(case_type, '非诉')

def get_or_create_case_number(type_code: str, year: int) -> CaseNumber:
    """
    获取或创建案件编号记录
    如果不存在，则根据规则创建新记录
    """
    try:
        return CaseNumber.objects.get(type_code=type_code, year=year)
    except CaseNumber.DoesNotExist:
        # 尝试获取上一年的记录
        try:
            last_year_record = CaseNumber.objects.get(
                type_code=type_code,
                year=year - 1
            )
            start_number = last_year_record.start_number
        except CaseNumber.DoesNotExist:
            start_number = 1
        
        return CaseNumber.objects.create(
            type_code=type_code,
            year=year,
            start_number=start_number,
            last_used_number=start_number - 1
        )

@transaction.atomic
def generate_case_number(case_type: str) -> str:
    """
    生成案件编号
    格式：（{年份}）广承律{类型代号}代{顺序编号}号 或 （{年份}）广承雷律{类型代号}代{顺序编号}号
    """
    current_year = datetime.now().year
    type_code = get_case_type_code(case_type)
    
    # 获取当前律师事务所别名，决定前缀
    firm_alias = get_current_firm()
    
    # 获取或创建案件编号记录
    case_number_record = get_or_create_case_number(type_code, current_year)
    
    # 生成新的顺序编号
    new_sequence = case_number_record.last_used_number + 1
    
    # 更新记录
    case_number_record.last_used_number = new_sequence
    case_number_record.save()
    
    # 格式化顺序号为4位数字
    formatted_sequence = f"{new_sequence:04d}"
    
    firm_prefix = "广承雷" if firm_alias == "firm2" else "广承"
    return f"（{current_year}）{firm_prefix}律{type_code}代{formatted_sequence}号" 