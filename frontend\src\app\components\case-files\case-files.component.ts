import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatDialog, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatListModule } from '@angular/material/list';
import { MatCheckboxModule, MatCheckboxChange } from '@angular/material/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CommonModule } from '@angular/common';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { MatTreeModule, MatTreeNestedDataSource } from '@angular/material/tree';
import { NestedTreeControl } from '@angular/cdk/tree';
import { CaseFilesService, CaseFileFolder, CaseFile } from '../../services/case-files.service';
import { CreateFolderDialogComponent } from './create-folder-dialog/create-folder-dialog.component';
import JSZip from 'jszip';
import { saveAs } from 'file-saver';

/**
 * 树节点数据接口
 */
interface FileTreeNode {
  name: string;
  id: number;
  isFolder: boolean;
  children?: FileTreeNode[];
  parent?: FileTreeNode;
  file?: CaseFile;
  folder?: CaseFileFolder;
  level: number;
  expanded?: boolean;
}

@Component({
  selector: 'app-case-files',
  templateUrl: './case-files.component.html',
  styleUrls: ['./case-files.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatButtonModule,
    MatIconModule,
    MatFormFieldModule,
    MatInputModule,
    MatListModule,
    MatCheckboxModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSnackBarModule,
    MatTooltipModule,
    DragDropModule,
    MatTreeModule
  ]
})
export class CaseFilesComponent implements OnInit {
  @Input() caseId!: number;
  
  // 数据状态
  folders: CaseFileFolder[] = [];
  currentFolder: CaseFileFolder | null = null;
  parentFolders: CaseFileFolder[] = [];
  rootFiles: CaseFile[] = [];
  
  // UI状态
  uploadForm: FormGroup;
  selectedFiles: CaseFile[] = [];
  mergeForm: FormGroup;
  isLoading = false;
  fileToUpload: File | null = null;
  viewMode: 'list' | 'grid' = 'list';
  
  // 搜索相关
  searchTerm: string = '';
  filteredFiles: CaseFile[] = [];
  filteredFolders: CaseFileFolder[] = [];
  
  // 树状结构相关
  treeControl = new NestedTreeControl<FileTreeNode>((node) => node.children);
  dataSource = new MatTreeNestedDataSource<FileTreeNode>();
  treeData: FileTreeNode[] = [];
  
  constructor(
    private caseFilesService: CaseFilesService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {
    this.uploadForm = this.fb.group({
      file: [null, [Validators.required]]
    });
    
    this.mergeForm = this.fb.group({
      output_filename: ['', [Validators.required]],
      start_page_number: [1, [Validators.required, Validators.min(1)]]
    });
  }
  
  ngOnInit(): void {
    this.loadRootFolders();
  }
  
  /**
   * 加载根文件夹和文件
   */
  loadRootFolders(): void {
    this.isLoading = true;
    this.rootFiles = [];
    
    this.caseFilesService.getFolders(this.caseId).subscribe({
      next: (folders) => {
        this.folders = folders;
        this.currentFolder = null;
        this.parentFolders = [];
        
        this.caseFilesService.getCaseRootFiles(this.caseId).subscribe({
          next: (files) => {
            this.rootFiles = files;
            this.filteredFiles = [...this.rootFiles];
            this.filteredFolders = [...this.folders];
            this.isLoading = false;
            this.buildFileTree();
          },
          error: (error) => {
            console.error('加载根目录文件失败:', error);
            this.isLoading = false;
          }
        });
      },
      error: (error) => {
        this.showError('加载文件夹失败');
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 构建文件树结构
   */
  buildFileTree(): void {
    this.treeData = [];
    
    // 添加根文件夹
    this.folders.forEach(folder => {
      this.fetchFolderContents(folder);
    });
    
    // 添加根文件
    this.rootFiles.forEach(file => {
      this.treeData.push({
        name: file.original_filename,
        id: file.id,
        isFolder: false,
        file: file,
        level: 0
      });
    });
    
    // 更新数据源
    this.dataSource.data = this.treeData;
  }
  
  /**
   * 递归获取文件夹内容
   */
  fetchFolderContents(folder: CaseFileFolder, parent?: FileTreeNode, level: number = 0): void {
    this.caseFilesService.getFolder(folder.id).subscribe({
      next: (folderDetail) => {
        // 创建当前文件夹节点
        const folderNode: FileTreeNode = {
          name: folder.name,
          id: folder.id,
          isFolder: true,
          folder: folderDetail,
          children: [],
          level: level,
          expanded: false
        };

        if (parent) {
          folderNode.parent = parent;
          if (!parent.children) {
            parent.children = [];
          }
          parent.children.push(folderNode);
        } else {
          this.treeData.push(folderNode);
        }
        
        // 添加子文件夹
        if (folderDetail.children && folderDetail.children.length > 0) {
          folderDetail.children.forEach(childFolder => {
            this.fetchFolderContents(childFolder, folderNode, level + 1);
          });
        }
        
        // 添加文件
        if (folderDetail.files && folderDetail.files.length > 0) {
          folderDetail.files.forEach(file => {
            if (!folderNode.children) {
              folderNode.children = [];
            }
            folderNode.children.push({
              name: file.original_filename,
              id: file.id,
              isFolder: false,
              file: file,
              parent: folderNode,
              level: level + 1
            });
          });
        }
        
        // 更新数据源
        this.dataSource.data = [...this.treeData];
      },
      error: (error) => {
        console.error(`获取文件夹 ${folder?.id} (${folder?.name}) 内容失败:`, error);
      }
    });
  }
  
  /**
   * 打开文件夹
   */
  openFolder(folder: CaseFileFolder): void {
    this.isLoading = true;
    this.caseFilesService.getFolder(folder.id).subscribe({
      next: (folderDetail) => {
        if (this.currentFolder) {
          this.parentFolders.push(this.currentFolder);
        }
        this.currentFolder = folderDetail;
        this.folders = folderDetail.children || [];
        this.rootFiles = folderDetail.files || [];
        this.filteredFiles = [...this.rootFiles];
        this.filteredFolders = [...this.folders];
        this.searchTerm = '';
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('打开文件夹失败');
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 导航到指定的文件夹
   */
  navigateToFolder(folder: CaseFileFolder): void {
    const index = this.parentFolders.findIndex(f => f.id === folder.id);
    if (index >= 0) {
      // 如果是在面包屑中点击的某个父文件夹
      this.parentFolders = this.parentFolders.slice(0, index);
      this.openFolder(folder);
    } else {
      // 否则就是普通打开文件夹
      this.openFolder(folder);
    }
  }
  
  /**
   * 返回上一级目录
   */
  goBack(): void {
    if (this.parentFolders.length > 0) {
      const parent = this.parentFolders.pop();
      if (parent) {
        this.openFolder(parent);
      }
    } else {
      this.loadRootFolders();
    }
  }
  
  /**
   * 检查节点是否有子节点
   */
  hasChild(_: number, node: FileTreeNode): boolean {
    return node.isFolder && !!node.children && node.children.length > 0;
  }
  
  /**
   * 节点点击处理
   */
  nodeClick(node: FileTreeNode): void {
    if (node.isFolder) {
      // 展开/折叠文件夹
      if (this.treeControl.isExpanded(node)) {
        this.treeControl.collapse(node);
      } else {
        this.treeControl.expand(node);
      }
    } else if (node.file) {
      // 文件点击：预览或下载
      this.viewFile(node.file);
    }
  }
  
  /**
   * 切换视图模式（列表/网格）
   */
  switchViewMode(mode: 'list' | 'grid'): void {
    this.viewMode = mode;
  }

  /**
   * 根据搜索词过滤文件和文件夹
   */
  onSearchInput(): void {
    this.filterItems();
  }

  /**
   * 清除搜索
   */
  clearSearch(): void {
    this.searchTerm = '';
    this.filterItems();
  }

  /**
   * 根据当前搜索条件过滤文件和文件夹
   */
  filterItems(): void {
    if (!this.searchTerm.trim()) {
      // 没有搜索词时显示全部
      this.filteredFiles = this.rootFiles;
      this.filteredFolders = this.folders;
      return;
    }

    const term = this.searchTerm.toLowerCase().trim();
    
    // 过滤文件
    this.filteredFiles = this.rootFiles.filter(file => {
      return file.original_filename.toLowerCase().includes(term);
    });
    
    // 过滤文件夹
    this.filteredFolders = this.folders.filter(folder => {
      return folder.name.toLowerCase().includes(term);
    });
  }

  /**
   * 获取文件类型显示名称
   */
  getFileTypeDisplay(fileType: string): string {
    const typeMap: {[key: string]: string} = {
      'pdf': 'PDF文档',
      'doc': 'Word文档',
      'docx': 'Word文档',
      'xls': 'Excel表格',
      'xlsx': 'Excel表格',
      'jpg': '图片',
      'jpeg': '图片',
      'png': '图片',
      'txt': '文本文档',
      'zip': '压缩文件'
    };
    
    return typeMap[fileType] || fileType.toUpperCase();
  }

  /**
   * 格式化文件大小显示
   */
  formatFileSize(bytes?: number): string {
    if (bytes === undefined || bytes === null) return '-';
    
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 B';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    if (i === 0) return bytes + ' ' + sizes[i];
    
    return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + sizes[i];
  }

  /**
   * 显示创建文件夹对话框
   */
  showCreateFolderDialog(): void {
    const dialogRef = this.dialog.open(CreateFolderDialogComponent, {
      width: '400px'
    });

    dialogRef.afterClosed().subscribe(folderName => {
      if (folderName) {
        this.createFolder(folderName);
      }
    });
  }

  /**
   * 创建新文件夹
   */
  createFolder(folderName: string): void {
    this.isLoading = true;
    const parentId = this.currentFolder ? this.currentFolder.id : null;
    
    this.caseFilesService.createFolder({
      case: this.caseId,
      name: folderName,
      parent: parentId
    }).subscribe({
      next: (folder) => {
        this.showSuccess('文件夹创建成功');
        // 如果在当前文件夹中，直接添加到列表
        if (parentId === null && !this.currentFolder) {
          this.folders.push(folder);
          this.filteredFolders.push(folder);
        } else if (this.currentFolder && this.currentFolder.id === parentId) {
          this.folders.push(folder);
          this.filteredFolders.push(folder);
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('创建文件夹失败');
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 删除文件夹
   */
  deleteFolder(folder: CaseFileFolder): void {
    if (!confirm(`确定要删除文件夹 "${folder.name}" 及其中的所有文件吗？此操作不可撤销！`)) {
      return;
    }
    
    this.isLoading = true;
    this.caseFilesService.deleteFolder(folder.id).subscribe({
      next: () => {
        this.showSuccess('文件夹删除成功');
        // 从当前列表中移除
        this.folders = this.folders.filter(f => f.id !== folder.id);
        this.filteredFolders = this.filteredFolders.filter(f => f.id !== folder.id);
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('删除文件夹失败');
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 文件选择处理
   */
  onFileSelected(event: any): void {
    const element = event.currentTarget;
    if (element.files && element.files.length > 0) {
      this.fileToUpload = element.files[0];
      console.log('选择的文件:', this.fileToUpload!.name, this.fileToUpload!.type, this.fileToUpload!.size);
    }
  }
  
  /**
   * 上传文件
   */
  uploadFile(): void {
    if (!this.fileToUpload) {
      this.showError('请先选择文件');
      return;
    }
    
    this.isLoading = true;
    
    // 如果有当前文件夹则使用，否则传递null表示上传到根目录
    const folderId = this.currentFolder ? this.currentFolder.id : null;
    
    this.caseFilesService.uploadFile(this.fileToUpload, this.caseId, folderId).subscribe({
      next: (file) => {
        this.showSuccess('文件上传成功');
        
        // 添加到当前文件列表 - 检查返回类型是否为 CaseFile
        if ('file_url' in file) {
          this.rootFiles.push(file as CaseFile);
          this.filteredFiles.push(file as CaseFile);
        } else {
          // 如果返回的是文件夹，则刷新文件列表
          this.loadRootFolders();
        }
        
        // 重置文件输入
        this.fileToUpload = null;
        const fileInput = document.getElementById('file-upload') as HTMLInputElement;
        if (fileInput) {
          fileInput.value = '';
        }
        
        this.isLoading = false;
      },
      error: (error) => {
        console.error('上传文件失败:', error);
        let errorMessage = '文件上传失败';
        
        // 尝试从错误响应中获取更具体的错误信息
        if (error.error && error.error.error) {
          errorMessage = error.error.error;
        }
        
        this.showError(errorMessage);
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 删除文件
   */
  deleteFile(file: CaseFile): void {
    if (!confirm(`确定要删除文件 "${file.original_filename}" 吗？此操作不可撤销！`)) {
      return;
    }
    
    this.isLoading = true;
    this.caseFilesService.deleteFile(file.id).subscribe({
      next: () => {
        this.showSuccess('文件删除成功');
        
        // 从选中的文件列表中移除
        this.selectedFiles = this.selectedFiles.filter(selectedFile => selectedFile.id !== file.id);
        
        // 从当前文件列表中移除
        this.rootFiles = this.rootFiles.filter(f => f.id !== file.id);
        this.filteredFiles = this.filteredFiles.filter(f => f.id !== file.id);
        
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('删除文件失败');
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 下载文件
   */
  downloadFile(file: CaseFile): void {
    this.isLoading = true;
    this.caseFilesService.downloadFile(file.id).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = file.original_filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('下载文件失败');
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 批量下载选中的文件
   */
  downloadSelected(): void {
    if (this.selectedFiles.length === 0) {
      this.showError('请先选择要下载的文件');
      return;
    }
    
    // 如果只选择了一个文件，直接下载
    if (this.selectedFiles.length === 1) {
      this.downloadFile(this.selectedFiles[0]);
      return;
    }
    
    this.isLoading = true;
    
    // 创建一个压缩包
    const zip = new JSZip();
    const downloads: Promise<void>[] = [];
    
    // 为每个文件创建下载任务
    this.selectedFiles.forEach(file => {
      const task = this.caseFilesService.downloadFile(file.id).toPromise()
        .then(blob => {
          if (blob) {
            zip.file(file.original_filename, blob);
          }
        })
        .catch(error => {
          console.error(`下载文件 ${file.original_filename} 失败:`, error);
        });
      
      downloads.push(task as Promise<void>);
    });
    
    // 等待所有下载完成
    Promise.all(downloads)
      .then(() => {
        return zip.generateAsync({ type: 'blob' });
      })
      .then(content => {
        saveAs(content, `案件文件-${new Date().toISOString().slice(0, 10)}.zip`);
        this.isLoading = false;
      })
      .catch(error => {
        console.error('创建压缩包失败:', error);
        this.showError('批量下载失败');
        this.isLoading = false;
      });
  }
  
  /**
   * 查看文件
   */
  viewFile(file: CaseFile): void {
    // 如果是图片或PDF，可以直接在新标签页打开
    if (file.file_type === 'jpg' || file.file_type === 'jpeg' || file.file_type === 'png' || file.file_type === 'pdf') {
      window.open(file.file_url, '_blank');
    } else {
      // 其他文件类型则下载
      this.downloadFile(file);
    }
  }
  
  /**
   * 切换文件选择状态
   */
  toggleFileSelection(file: CaseFile): void {
    const index = this.selectedFiles.findIndex(f => f.id === file.id);
    if (index > -1) {
      this.selectedFiles.splice(index, 1);
    } else {
      this.selectedFiles.push(file);
    }
  }
  
  /**
   * 切换所有文件的选择状态
   */
  toggleAllFiles(event: MatCheckboxChange): void {
    if (event.checked) {
      // 全选 - 但只选择当前过滤后的文件
      this.selectedFiles = [...this.filteredFiles];
    } else {
      // 全不选
      this.selectedFiles = [];
    }
  }
  
  /**
   * 检查是否所有文件都已选择
   */
  areAllFilesSelected(): boolean {
    return this.filteredFiles.length > 0 && 
           this.filteredFiles.every(file => this.isFileSelected(file));
  }
  
  /**
   * 检查是否有部分文件被选择
   */
  areSomeFilesSelected(): boolean {
    const selectedCount = this.filteredFiles.filter(file => this.isFileSelected(file)).length;
    return selectedCount > 0 && selectedCount < this.filteredFiles.length;
  }
  
  /**
   * 检查文件是否已选择
   */
  isFileSelected(file: CaseFile): boolean {
    return this.selectedFiles.some(f => f.id === file.id);
  }
  
  /**
   * 在树视图中处理文件选择/取消选择
   */
  toggleFileSelectionInTree(event: MatCheckboxChange, node: FileTreeNode): void {
    if (!node.isFolder && node.file) {
      this.toggleFileSelection(node.file);
    }
  }
  
  /**
   * 在树视图中检查文件是否已选择
   */
  isFileSelectedInTree(node: FileTreeNode): boolean {
    if (!node.isFolder && node.file) {
      return this.isFileSelected(node.file);
    }
    return false;
  }
  
  /**
   * 处理拖放重新排序
   */
  onDrop(event: CdkDragDrop<CaseFile[]>): void {
    moveItemInArray(this.selectedFiles, event.previousIndex, event.currentIndex);
  }
  
  /**
   * 合并PDF
   */
  mergeToPdf(): void {
    if (this.mergeForm.invalid || this.selectedFiles.length === 0) {
      return;
    }
    
    const request = {
      file_ids: this.selectedFiles.map(file => file.id),
      output_filename: this.mergeForm.value.output_filename,
      start_page_number: this.mergeForm.value.start_page_number
    };
    
    this.isLoading = true;
    this.caseFilesService.mergePdf(request).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${request.output_filename}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        this.showSuccess('PDF合并成功');
        this.isLoading = false;
      },
      error: (error) => {
        this.showError('PDF合并失败');
        this.isLoading = false;
      }
    });
  }
  
  /**
   * 清除所有选中的文件
   */
  clearSelectedFiles(): void {
    this.selectedFiles = [];
  }
  
  /**
   * 显示成功消息
   */
  private showSuccess(message: string): void {
    this.snackBar.open(message, '关闭', {
      duration: 3000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom'
    });
  }
  
  /**
   * 显示错误消息
   */
  private showError(message: string): void {
    this.snackBar.open(message, '关闭', {
      duration: 5000,
      horizontalPosition: 'center',
      verticalPosition: 'bottom',
      panelClass: ['error-snackbar']
    });
  }
}
