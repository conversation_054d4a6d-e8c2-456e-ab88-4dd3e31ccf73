<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函件登记表</title>
    <style>
        @media print {
            body {
                margin: 0;
                padding: 20px;
                font-family: "SimSun", serif;
                font-size: 12px;
                line-height: 1.4;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-before: always;
            }
        }
        
        @media screen {
            body {
                margin: 0;
                padding: 20px;
                font-family: "SimSun", serif;
                font-size: 12px;
                line-height: 1.4;
                background-color: #f5f5f5;
            }
            .print-container {
                background-color: white;
                padding: 20px;
                margin: 0 auto;
                max-width: 210mm;
                min-height: 297mm;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
        }
        
        .company-name {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        h1 {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            margin: 20px 0;
        }
        
        .year-info {
            text-align: center;
            font-size: 14px;
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        
        th, td {
            border: 1px solid #000;
            padding: 4px 2px;
            text-align: center;
            vertical-align: middle;
            word-wrap: break-word;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            height: 30px;
        }
        
        td {
            height: 25px;
            min-height: 25px;
        }
        
        .col-seq { width: 6%; }
        .col-letter-number { width: 12%; }
        .col-date { width: 9%; }
        .col-case-number { width: 11%; }
        .col-client { width: 13%; }
        .col-quantity { width: 5%; }
        .col-recipient { width: 13%; }
        .col-lawyer { width: 9%; }
        .col-approver { width: 9%; }
        .col-remarks { width: 13%; }
        
        .empty-row td {
            height: 25px;
            border: 1px solid #000;
        }
        
        .page-info {
            text-align: center;
            margin-bottom: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="company-name">{{ firm_name }}</div>
        <h1 id="tableTitle">函件登记表</h1>
        <div class="year-info" id="yearInfo"></div>
        <div class="page-info" id="pageInfo"></div>
        
        <table>
            <thead>
                <tr>
                    <th class="col-seq">序号</th>
                    <th class="col-letter-number">函数编号</th>
                    <th class="col-date">日期</th>
                    <th class="col-case-number">对应案件号</th>
                    <th class="col-client">委托人/当事人</th>
                    <th class="col-quantity">数量</th>
                    <th class="col-recipient">致函单位</th>
                    <th class="col-lawyer">使用律师</th>
                    <th class="col-approver">审批人</th>
                    <th class="col-remarks">备注</th>
                </tr>
            </thead>
            <tbody id="letterTableBody">
                <!-- 数据将通过JavaScript填充 -->
            </tbody>
        </table>
        
        <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()" style="padding: 8px 16px; margin-right: 10px; background-color: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer;">打印</button>
            <button onclick="window.close()" style="padding: 8px 16px; background-color: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
        </div>
    </div>

    <script>
        // 从URL参数获取数据
        const urlParams = new URLSearchParams(window.location.search);
        const recordsData = urlParams.get('records');
        const year = urlParams.get('year');
        const letterType = urlParams.get('letter_type') || '函件';
        const currentPage = parseInt(urlParams.get('current_page')) || 1;
        const totalPages = parseInt(urlParams.get('total_pages')) || 1;

        // 设置标题
        document.getElementById('tableTitle').textContent = `${year}年度广东承诺律师事务所函件登记表（${letterType}）`;
        
        // 设置年份信息
        if (year) {
            document.getElementById('yearInfo').textContent = `${year}年度`;
        }
        
        // 设置页码信息
        if (totalPages > 1) {
            document.getElementById('pageInfo').textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
        }

        // 填充表格数据
        if (recordsData) {
            try {
                const records = JSON.parse(decodeURIComponent(recordsData));
                const tbody = document.getElementById('letterTableBody');
                
                // 按sequence_number排序（已审批的在前，按序号排序；未审批的在后，按创建时间排序）
                records.sort((a, b) => {
                    if (a.sequence_number && b.sequence_number) {
                        return a.sequence_number - b.sequence_number;
                    } else if (a.sequence_number && !b.sequence_number) {
                        return -1;
                    } else if (!a.sequence_number && b.sequence_number) {
                        return 1;
                    } else {
                        return new Date(b.created_at) - new Date(a.created_at);
                    }
                });

                records.forEach((record, index) => {
                    const row = document.createElement('tr');
                    
                    // 序号 - 显示行数（从1开始）
                    const seqCell = document.createElement('td');
                    seqCell.textContent = index + 1;
                    row.appendChild(seqCell);
                    
                    // 函数编号
                    const letterNumberCell = document.createElement('td');
                    letterNumberCell.textContent = record.letter_number ? `（${record.year}）${record.letter_type}${record.letter_number}号` : '';
                    row.appendChild(letterNumberCell);
                    
                    // 日期
                    const dateCell = document.createElement('td');
                    dateCell.textContent = record.date;
                    row.appendChild(dateCell);
                    
                    // 对应案件号
                    const caseNumberCell = document.createElement('td');
                    caseNumberCell.textContent = record.case_number || '';
                    row.appendChild(caseNumberCell);
                    
                    // 委托人/当事人
                    const clientCell = document.createElement('td');
                    clientCell.textContent = record.client_name || '';
                    row.appendChild(clientCell);
                    
                    // 数量
                    const quantityCell = document.createElement('td');
                    quantityCell.textContent = record.quantity || '';
                    row.appendChild(quantityCell);
                    
                    // 致函单位
                    const recipientCell = document.createElement('td');
                    recipientCell.textContent = record.recipient_unit || '';
                    row.appendChild(recipientCell);
                    
                    // 使用律师
                    const lawyerCell = document.createElement('td');
                    lawyerCell.textContent = record.lawyer_name || '';
                    row.appendChild(lawyerCell);
                    
                    // 审批人（打印时留空）
                    const approverCell = document.createElement('td');
                    approverCell.textContent = '';
                    row.appendChild(approverCell);
                    
                    // 备注
                    const remarksCell = document.createElement('td');
                    remarksCell.textContent = record.remarks || '';
                    row.appendChild(remarksCell);
                    
                    tbody.appendChild(row);
                });
                
            } catch (error) {
                console.error('解析记录数据失败:', error);
            }
        }
    </script>
</body>
</html> 