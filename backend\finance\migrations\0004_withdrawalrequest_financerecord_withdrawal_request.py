# Generated by Django 5.0.3 on 2025-06-02 17:46

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0030_add_agreed_lawyer_fee'),
        ('finance', '0003_financerecord_case'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WithdrawalRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12, verbose_name='申请金额')),
                ('status', models.CharField(choices=[('PENDING', '未审批'), ('APPROVED', '已批准'), ('REJECTED', '已拒绝')], default='PENDING', max_length=20, verbose_name='状态')),
                ('remarks', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='申请时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('approver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_withdrawals', to=settings.AUTH_USER_MODEL, verbose_name='审批人')),
                ('case', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to='cases.case', verbose_name='关联案件')),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawal_requests', to=settings.AUTH_USER_MODEL, verbose_name='申请人')),
            ],
            options={
                'verbose_name': '提款申请',
                'verbose_name_plural': '提款申请',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='financerecord',
            name='withdrawal_request',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='finance_record', to='finance.withdrawalrequest', verbose_name='关联提款申请'),
        ),
    ]
