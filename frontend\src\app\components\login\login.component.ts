import { CommonModule } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatIconModule } from '@angular/material/icon';

import { environment } from '../../../environments/environment';
import { AuthService } from '../../services/auth.service';
import { FeishuService } from '../../services/feishu.service';

declare global {
  interface Window {
    QRLogin: any;
  }
}

// ENUM for login method
enum LoginMethod {
  Normal = 'normal',
  FeishuQrcode = 'feishu_qrcode'
}

const GLOBAL = {
  isFeishuScriptLoaded: false,
  loginMethodPriority: [LoginMethod.FeishuQrcode, LoginMethod.Normal],

  feishuQRLoginObj: undefined,  // feishu QRLogin object
  feishuQRLogin: undefined,  // feishu QRLogin function from script
}

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatIconModule,
  ]
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  isLoading = false;
  loginMethod: LoginMethod = LoginMethod.Normal;
  availableLoginMethods: LoginMethod[] = [LoginMethod.Normal];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar,
    private feishuService: FeishuService
  ) {
    this.loginForm = this.fb.group({
      username: ['', Validators.required],
      password: ['', Validators.required]
    });
  }

  ngOnInit(): void {
    // 加载飞书登录脚本
    if (!GLOBAL.isFeishuScriptLoaded) {
      if (!window?.document) {
        console.error('window.document is not available');
        return;
      }

      const elm = window.document.createElement('script');
      elm.src = 'https://lf-package-cn.feishucdn.com/obj/feishu-static/lark/passport/qrcode/LarkSSOSDKWebQRCode-1.0.3.js';
      elm.onload = () => {
        GLOBAL.isFeishuScriptLoaded = true;
        console.info('Feishu script loaded');

        GLOBAL.feishuQRLogin = window?.QRLogin;
        console.log('GLOBAL.feishuQRLogin', GLOBAL.feishuQRLogin);

        if (GLOBAL.feishuQRLogin) {
          this.availableLoginMethods.push(LoginMethod.FeishuQrcode);

          this.changeLoginMethod(LoginMethod.FeishuQrcode);
        }
      };
      window.document.body.appendChild(elm)
    } else {
      if (GLOBAL.feishuQRLogin) {
        this.availableLoginMethods.push(LoginMethod.FeishuQrcode);

        this.changeLoginMethod(LoginMethod.FeishuQrcode);
      }
    }
  }

  isSupportedLoginMethod(method: string) {
    return this.availableLoginMethods.includes(method as LoginMethod);
  }
  isLoginMethod(method: string) {
    return this.loginMethod === method;
  }

  changeLoginMethod(method: LoginMethod) {
    if (!this.isSupportedLoginMethod(method)) return
    if (this.isLoginMethod(method)) return

    this.loginMethod = method;
    console.info('Login method changed to', method);

    // FIXME this is a workaround, should be refactored
    if (method == LoginMethod.FeishuQrcode) {
      setTimeout(() =>
        this.createFeishuQrcode(), 100)
    }
  }

  doNormalLogin() {
    if (this.loginForm.valid) {
      this.isLoading = true;
      const { username, password } = this.loginForm.value;
      this.authService.login(username, password).subscribe({
        next: () => {
          this.router.navigate(['/cases']);
        },
        error: (error) => {
          this.snackBar.open('登录失败: ' + (error.error?.detail || '未知错误'), '关闭', {
            duration: 3000
          });
          this.isLoading = false;
        }
      });
    }
  }

  createFeishuQrcode() {
    const func: any = GLOBAL.feishuQRLogin;
    if (!func) {
      console.error('Feishu QRLogin function is not available');
      return
    }

    // 根据当前域名动态生成重定向URI
    const currentOrigin = window.location.origin;
    const redirectURI = `${currentOrigin}/api/feishu/callback`;
    console.log('redirectURI', redirectURI);
    const redirect_uri = encodeURIComponent(redirectURI);
    const state = Math.random().toString(36).substring(2);
    const goto = "https://passport.feishu.cn/suite/passport/oauth/authorize?"
      + `client_id=${environment.feishuClientID}&`
      + `redirect_uri=${redirect_uri}&`
      + `response_type=code&state=${state}`;

    const obj = func({
      goto,

      id: "feishuLoginQrcodePic",
      width: "300",
      height: "300",
      style: "width:300px;height:300px"//可选的，二维码html标签的style属性
    });

    GLOBAL.feishuQRLoginObj = obj;

    if (typeof window.addEventListener != 'undefined') {
      window.addEventListener('message', (event) => {

        if (!obj.matchOrigin(event.origin) || !obj.matchData(event.data)) {
          console.error(`Invalid message from ${event.origin} with data ${event.data}`);
          return
        }

        const code = event.data.tmp_code;
        console.info('Feishu QRCode login received code', code);

        // 跳转到飞书的服务器上
        window.location.href = goto + "&code=" + code;
      }, false);
    }

    console.info('Feishu QRCode refreshed');
  }
}
