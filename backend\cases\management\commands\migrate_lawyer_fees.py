from django.core.management.base import BaseCommand
from cases.models import Case
from decimal import Decimal
import re


class Command(BaseCommand):
    help = '将案件content中的商定律师费迁移到agreed_lawyer_fee字段'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要迁移的数据，不实际执行迁移',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # 查找需要迁移的案件
        cases_to_migrate = Case.objects.filter(
            agreed_lawyer_fee__isnull=True,
            content__has_key='商定律师费'
        )
        
        self.stdout.write(f'找到 {cases_to_migrate.count()} 个需要迁移的案件')
        
        migrated_count = 0
        failed_count = 0
        
        for case in cases_to_migrate:
            lawyer_fee_str = case.content.get('商定律师费', '')
            
            if not lawyer_fee_str:
                continue
                
            try:
                # 移除非数字字符，提取数字
                fee_match = re.search(r'[\d.]+', str(lawyer_fee_str))
                if fee_match:
                    lawyer_fee = Decimal(fee_match.group())
                    
                    self.stdout.write(
                        f'案件 {case.id} ({case.case_number}): '
                        f'商定律师费 "{lawyer_fee_str}" -> {lawyer_fee}'
                    )
                    
                    if not dry_run:
                        case.agreed_lawyer_fee = lawyer_fee
                        case.save(update_fields=['agreed_lawyer_fee'])
                        
                        # 触发付款状态检查
                        case.check_and_update_payment_status()
                    
                    migrated_count += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f'案件 {case.id} ({case.case_number}): '
                            f'无法解析商定律师费 "{lawyer_fee_str}"'
                        )
                    )
                    failed_count += 1
                    
            except (ValueError, TypeError) as e:
                self.stdout.write(
                    self.style.ERROR(
                        f'案件 {case.id} ({case.case_number}): '
                        f'解析商定律师费 "{lawyer_fee_str}" 时出错: {e}'
                    )
                )
                failed_count += 1
        
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    f'试运行完成：将迁移 {migrated_count} 个案件，{failed_count} 个失败'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'迁移完成：成功迁移 {migrated_count} 个案件，{failed_count} 个失败'
                )
            ) 