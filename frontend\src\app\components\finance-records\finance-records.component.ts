import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatSortModule } from '@angular/material/sort';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatChipsModule } from '@angular/material/chips';
import { MatTooltipModule } from '@angular/material/tooltip';

import { FinanceService } from '../../services/finance.service';
import { AuthService } from '../../services/auth.service';
import { FinanceRecord, FinanceRecordCreate, FINANCE_ACCOUNT_TYPES } from '../../interfaces/finance.interface';
import { User } from '../../interfaces/case.interface';
import { getUserDisplayName } from '../../utils/user.utils';
import { FinanceRecordDialogComponent } from './finance-record-dialog.component';

@Component({
  selector: 'app-finance-records',
  templateUrl: './finance-records.component.html',
  styleUrls: ['./finance-records.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatChipsModule,
    MatTooltipModule
  ]
})
export class FinanceRecordsComponent implements OnInit {
  records: FinanceRecord[] = [];
  filteredRecords: FinanceRecord[] = [];
  lawyers: User[] = [];
  isLoading = false;
  hasFinancePermission = false;
  
  displayedColumns = ['transaction_date', 'lawyer', 'case', 'account_type', 'purpose', 'display_amount', 'created_by', 'created_at', 'actions'];
  filterForm: FormGroup;
  accountTypes = FINANCE_ACCOUNT_TYPES;
  getUserDisplayName = getUserDisplayName;

  constructor(
    private financeService: FinanceService,
    private authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      lawyer_id: [''],
      account_type: [''],
      start_date: [''],
      end_date: [''],
      keyword: ['']
    });
  }

  ngOnInit(): void {
    this.checkPermissions();
    this.loadRecords();
    this.loadLawyers();
    this.filterForm.valueChanges.subscribe(() => {
      this.applyFilters();
    });
  }

  checkPermissions(): void {
    this.authService.user$.subscribe(user => {
      this.hasFinancePermission = !!(user?.profile?.has_finance_permission);
    });
  }

  loadRecords(): void {
    this.isLoading = true;
    this.financeService.getFinanceRecords().subscribe({
      next: (records) => {
        this.records = records;
        this.filteredRecords = records;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('加载财务记录失败:', error);
        this.snackBar.open('加载财务记录失败', '关闭', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  loadLawyers(): void {
    this.authService.getAllUsers().subscribe({
      next: (users) => {
        this.lawyers = users;
      },
      error: (error) => {
        console.error('加载律师列表失败:', error);
      }
    });
  }

  applyFilters(): void {
    const filters = this.filterForm.value;
    this.isLoading = true;
    this.financeService.getFinanceRecords(filters).subscribe({
      next: (records) => {
        this.filteredRecords = records;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('筛选失败:', error);
        this.snackBar.open('筛选失败', '关闭', { duration: 3000 });
        this.isLoading = false;
      }
    });
  }

  resetFilters(): void {
    this.filterForm.reset();
    this.filteredRecords = this.records;
  }

  openCreateDialog(): void {
    if (!this.hasFinancePermission) {
      this.snackBar.open('您没有财务管理权限', '关闭', { duration: 3000 });
      return;
    }
    const dialogRef = this.dialog.open(FinanceRecordDialogComponent, {
      width: '600px',
      data: { lawyers: this.lawyers, mode: 'create' }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.createRecord(result);
      }
    });
  }

  createRecord(recordData: FinanceRecordCreate): void {
    this.financeService.createFinanceRecord(recordData).subscribe({
      next: () => {
        this.snackBar.open('财务记录创建成功', '关闭', { duration: 3000 });
        this.loadRecords();
      },
      error: (error) => {
        console.error('创建记录失败:', error);
        this.snackBar.open('创建记录失败', '关闭', { duration: 3000 });
      }
    });
  }

  deleteRecord(record: FinanceRecord): void {
    if (!this.hasFinancePermission) {
      this.snackBar.open('您没有财务管理权限', '关闭', { duration: 3000 });
      return;
    }
    if (confirm(`确定要删除这条财务记录吗？\n${record.purpose} - ${record.display_amount}元`)) {
      this.financeService.deleteFinanceRecord(record.id).subscribe({
        next: () => {
          this.snackBar.open('财务记录删除成功', '关闭', { duration: 3000 });
          this.loadRecords();
        },
        error: (error) => {
          console.error('删除记录失败:', error);
          this.snackBar.open('删除记录失败', '关闭', { duration: 3000 });
        }
      });
    }
  }

  getAccountTypeDisplay(accountType: string): string {
    const type = this.accountTypes.find(t => t.value === accountType);
    return type ? type.label : accountType;
  }

  getAccountTypeClass(record: FinanceRecord): string {
    return record.is_income ? 'income-chip' : 'expense-chip';
  }

  formatAmount(record: FinanceRecord): string {
    try {
      if (!record) return '¥0.00';
      let displayAmount: number;
      if (record.display_amount !== undefined && record.display_amount !== null) {
        displayAmount = record.display_amount;
      } else if (record.amount !== undefined && record.amount !== null) {
        displayAmount = Math.abs(record.amount);
      } else {
        return '¥0.00';
      }
      const prefix = record.is_income ? '+' : '-';
      return `${prefix}¥${displayAmount.toFixed(2)}`;
    } catch (error) {
      console.error('Error formatting amount:', error, record);
      return '¥0.00';
    }
  }

  formatDate(dateString: string): string {
    try {
      if (!dateString) return '';
      return new Date(dateString).toLocaleDateString('zh-CN');
    } catch (error) {
      console.error('Error formatting date:', error, dateString);
      return '';
    }
  }
} 