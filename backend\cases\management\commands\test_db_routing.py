"""
测试数据库路由功能的管理命令
"""
from django.core.management.base import BaseCommand
from django.conf import settings
from law_firm.database_router import set_current_db_alias, get_current_db_alias
from cases.models import NaturalPerson
from django.contrib.auth.models import User

class Command(BaseCommand):
    help = '测试数据库路由功能'

    def add_arguments(self, parser):
        parser.add_argument(
            '--database',
            type=str,
            help='指定要测试的数据库别名',
            choices=['default', 'firm2'],
            default='default'
        )

    def handle(self, *args, **options):
        database = options['database']
        
        self.stdout.write(f"测试数据库路由功能，目标数据库: {database}")
        
        # 设置数据库别名
        set_current_db_alias(database)
        
        # 检查当前数据库别名
        current_db = get_current_db_alias()
        self.stdout.write(f"当前数据库别名: {current_db}")
        
        # 测试查询
        try:
            # 测试自然人查询
            person_count = NaturalPerson.objects.count()
            self.stdout.write(f"自然人总数 (通过路由器): {person_count}")
            
            # 直接指定数据库查询
            person_count_direct = NaturalPerson.objects.using(database).count()
            self.stdout.write(f"自然人总数 (直接指定): {person_count_direct}")
            
            # 测试用户查询
            user_count = User.objects.count()
            self.stdout.write(f"用户总数 (通过路由器): {user_count}")
            
            user_count_direct = User.objects.using(database).count()
            self.stdout.write(f"用户总数 (直接指定): {user_count_direct}")
            
            if person_count == person_count_direct and user_count == user_count_direct:
                self.stdout.write(
                    self.style.SUCCESS(f'✅ 数据库路由功能正常工作，正在使用数据库: {database}')
                )
            else:
                self.stdout.write(
                    self.style.ERROR('❌ 数据库路由功能可能存在问题')
                )
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ 数据库连接或查询失败: {e}')
            )
        
        # 显示所有配置的数据库
        self.stdout.write("\n配置的数据库:")
        for db_alias, db_config in settings.DATABASES.items():
            self.stdout.write(f"  {db_alias}: {db_config['NAME']}")
        
        self.stdout.write(f"\n测试完成!") 