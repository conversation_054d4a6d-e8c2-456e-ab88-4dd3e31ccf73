import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit, Inject } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialog, MatDialogModule, MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { Observable, of, Subject } from 'rxjs';
import { catchError, delay, map, retryWhen, take, tap, switchMap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { CaseFilesComponent } from '../case-files/case-files.component';
import { Case, CaseLawyerCollaboration, CaseStatus, User, PartyType } from '../../interfaces/case.interface';
import { AuthService } from '../../services/auth.service';
import { CaseService } from '../../services/case.service';
import { WithdrawalService } from '../../services/withdrawal.service';
import { WithdrawalRequestCreate, WithdrawalRequestStatus } from '../../interfaces/withdrawal.interface';
import { getUserDisplayName } from '../../utils/user.utils';
import { PartyDialogComponent } from '../shared/party-dialog/party-dialog.component';
import { getPartyTypeDisplay, getPartyTypeText } from '../../utils/party.utils';
import { WithdrawalDialogComponent } from '../withdrawal-dialog/withdrawal-dialog.component';

// 添加协作律师对话框组件
@Component({
  selector: 'app-add-lawyer-dialog',
  templateUrl: './add-lawyer-dialog.component.html',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    FormsModule
  ]
})
export class AddLawyerDialogComponent {
  selectedLawyerId: number | null = null;
  feeSharePercentage: number = 0;
  remarks: string = '';
  availableLawyers: User[] = [];
  getUserDisplayName = getUserDisplayName;

  constructor() {}
}

// 添加当事人对话框组件
@Component({
  selector: 'app-add-party-dialog',
  templateUrl: './add-party-dialog.component.html',
  styleUrls: ['./add-party-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatIconModule,
    MatAutocompleteModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule,
    FormsModule,
    MatProgressSpinnerModule
  ]
})
export class AddPartyDialogComponent implements OnInit, OnDestroy {
  searchTerm: string = '';
  filteredParties: any[] = [];
  legacyParties: any[] = []; // 添加历史案件当事人数组
  selectedParty: any = null;
  partyType: string = PartyType.PLAINTIFF;
  isClient: boolean = false;
  internalNumber: string = '';
  remarks: string = '';
  isLoading: boolean = false;
  isLoadingLegacy: boolean = false; // 添加历史搜索加载状态
  caseType: string = '民事案件';

  private searchTimeout: any; // 用于防抖

  // 在模板中使用枚举和工具函数
  PartyType = PartyType;
  getPartyTypeDisplay = getPartyTypeDisplay;

  constructor(
    private http: HttpClient,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    if (data && data.caseType) {
      this.caseType = data.caseType;
    }
  }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }

  searchParties() {
    // 清除之前的定时器
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    if (!this.searchTerm || this.searchTerm.trim().length === 0) {
      this.filteredParties = [];
      this.legacyParties = []; // 清空历史搜索结果
      return;
    }

    // 添加防抖机制，300ms 后执行搜索
    this.searchTimeout = setTimeout(() => {
      this.performSearch();
    }, 300);
  }

  private performSearch() {
    this.isLoading = true;
    // 对搜索词进行更好的处理：去除首尾空格，但保留中间的空格
    const searchTerm = this.searchTerm.trim();
    const lowerSearchTerm = searchTerm.toLowerCase();

    // 添加调试信息
    console.log('搜索词：', searchTerm, '长度：', searchTerm.length);

    this.http.get<any[]>(`${environment.apiUrl}/natural-persons/`).pipe(
      switchMap(naturalPersons => {
        // 改进过滤逻辑：处理姓名中可能的空格和特殊字符
        const filteredNaturalPersons = naturalPersons.filter(person => {
          if (!person.name) return false;

          const personName = person.name.trim().toLowerCase();
          const personIdNum = person.id_number ? person.id_number.trim().toLowerCase() : '';
          const personPhone = person.phone_number ? person.phone_number.trim() : '';

          // 检查是否匹配：优先精确匹配，然后模糊匹配
          const nameMatch = personName === lowerSearchTerm || personName.includes(lowerSearchTerm);
          const idMatch = personIdNum && (personIdNum === lowerSearchTerm || personIdNum.includes(lowerSearchTerm));
          const phoneMatch = personPhone && personPhone.includes(searchTerm);

          return nameMatch || idMatch || phoneMatch;
        });

        console.log('自然人搜索结果数量：', filteredNaturalPersons.length);

        const naturalResults = filteredNaturalPersons.map(person => ({
          id: person.id,
          name: person.name,
          type: 'natural' as const,
          info: person.id_number || person.phone_number || ''
        }));

        return this.http.get<any[]>(`${environment.apiUrl}/legal-entities/`).pipe(
          map(legalEntities => {
            // 改进单位过滤逻辑
            const filteredLegalEntities = legalEntities.filter(entity => {
              if (!entity.name) return false;

              const entityName = entity.name.trim().toLowerCase();
              const repName = entity.representative_name ? entity.representative_name.trim().toLowerCase() : '';

              // 检查是否匹配
              const nameMatch = entityName === lowerSearchTerm || entityName.includes(lowerSearchTerm);
              const repMatch = repName && (repName === lowerSearchTerm || repName.includes(lowerSearchTerm));

              return nameMatch || repMatch;
            });

            console.log('单位搜索结果数量：', filteredLegalEntities.length);

            const legalResults = filteredLegalEntities.map(entity => ({
              id: entity.id,
              name: entity.name,
              type: 'legal' as const,
              info: `代表人: ${entity.representative_name || '未知'}`
            }));

            // 合并结果并按匹配度排序
            const allResults = [...naturalResults, ...legalResults];

            // 排序：精确匹配优先，然后按名称长度排序
            allResults.sort((a, b) => {
              const aExact = a.name.toLowerCase() === lowerSearchTerm;
              const bExact = b.name.toLowerCase() === lowerSearchTerm;

              if (aExact && !bExact) return -1;
              if (!aExact && bExact) return 1;

              return a.name.length - b.name.length;
            });

            return allResults;
          })
        );
      }),
      catchError(error => {
        console.error('搜索当事人出错：', error);
        return of([]);
      })
    ).subscribe({
      next: (results) => {
        console.log('最终搜索结果：', results);
        this.filteredParties = results;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('搜索请求失败：', error);
        this.filteredParties = [];
        this.isLoading = false;
      }
    });

    // 搜索历史案件当事人
    if (searchTerm.length >= 2) {
      this.isLoadingLegacy = true;
      // 使用encodeURIComponent确保中文正确编码
      const encodedSearchTerm = encodeURIComponent(searchTerm);
      this.http.get<any[]>(`${environment.apiUrl}/legacy-cases/search_party/?q=${encodedSearchTerm}`).subscribe({
        next: (results) => {
          this.legacyParties = results;
          this.isLoadingLegacy = false;
        },
        error: (error) => {
          console.error('搜索历史案件当事人出错：', error);
          this.legacyParties = [];
          this.isLoadingLegacy = false;
        }
      });
    } else {
      this.legacyParties = [];
    }
  }

  // 直接从列表选择当事人
  selectPartyDirectly(party: any) {
    this.selectedParty = party;
    // 选择当事人后清空搜索结果
    this.searchTerm = '';
    this.filteredParties = [];
    this.legacyParties = [];

    // 清除搜索定时器
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  }
}

// 编辑当事人与案件关联信息的对话框组件
@Component({
  selector: 'app-edit-party-relation-dialog',
  templateUrl: './edit-party-relation-dialog.component.html',
  styleUrls: ['./edit-party-relation-dialog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    MatButtonModule
  ]
})
export class EditPartyRelationDialogComponent {
  partyData: any;
  caseType: string = '民事案件';
  partyType: string = '';
  isClient: boolean = false;
  internalNumber: string = '';
  remarks: string = '';

  // 在模板中使用枚举
  PartyType = PartyType;

  constructor(
    public dialogRef: MatDialogRef<EditPartyRelationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    if (data) {
      this.partyData = data.partyData;
      this.caseType = data.caseType || '民事案件';

      // 初始化表单值
      if (this.partyData) {
        this.partyType = this.partyData.party_type;
        this.isClient = this.partyData.is_client || false;
        this.internalNumber = this.partyData.internal_number || '';
        this.remarks = this.partyData.remarks || '';
      }
    }
  }

  // 获取当事人名称
  getPartyName(): string {
    if (this.partyData.natural_person) {
      return this.partyData.natural_person.name || '未知';
    } else if (this.partyData.legal_entity) {
      return this.partyData.legal_entity.name || '未知';
    }
    return '未知';
  }

  // 获取当事人类型
  getEntityType(): string {
    if (this.partyData.natural_person) {
      return 'natural';
    } else if (this.partyData.legal_entity) {
      return 'legal';
    }
    return '';
  }
}

// 编辑协作详情对话框组件
@Component({
  selector: 'app-edit-collaboration-dialog',
  templateUrl: './edit-collaboration-dialog.component.html',
  standalone: true,
  imports: [
    CommonModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    FormsModule
  ]
})
export class EditCollaborationDialogComponent {
  feeSharePercentage: number = 0;
  lawyerName: string = '';
  remarks: string = '';

  constructor(
    public dialogRef: MatDialogRef<EditCollaborationDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.feeSharePercentage = (data.feeShareRatio || 0) * 100;
    this.lawyerName = data.lawyerName || '';
    this.remarks = data.remarks || '';
  }
}

@Component({
  selector: 'app-case-detail',
  templateUrl: './case-detail.component.html',
  styleUrls: ['./case-detail.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    ReactiveFormsModule,

    MatAutocompleteModule,
    MatButtonModule,
    MatCardModule,
    MatCheckboxModule,
    MatDialogModule,
    MatFormFieldModule,
    MatIconModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatSelectModule,
    MatSnackBarModule,
    MatTooltipModule,
    PartyDialogComponent,
    CaseFilesComponent,
    AddPartyDialogComponent,
    EditPartyRelationDialogComponent,
    EditCollaborationDialogComponent,
    WithdrawalDialogComponent,
    MatTabsModule
  ]
})
export class CaseDetailComponent implements OnInit, OnDestroy {
  case: Case | null = null;
  isLoading = true;
  error: string | null = null;
  canApproveCases$!: Observable<boolean>;
  approvalComment: string = '';
  isOwner = false;
  isCollaborator = false; // 添加协作律师标识
  currentUser: any = null; // 添加当前用户属性
  private destroy$ = new Subject<void>();

  // 协作律师相关
  collaborations: CaseLawyerCollaboration[] = [];
  availableLawyers: User[] = [];
  collaboratorsLoading = false;
  collaboratorsError: string | null = null;

  // 历史案件当事人记录相关
  partyHistoryRecords: Map<string, any[]> = new Map(); // 存储每个当事人的历史记录
  expandedPartyHistory: Set<string> = new Set(); // 跟踪展开状态
  historyLoading: Map<string, boolean> = new Map(); // 跟踪加载状态

  // 定义状态顺序
  private readonly STATUS_ORDER: CaseStatus[] = [
    CaseStatus.CREATED,
    CaseStatus.APPLYING,
    CaseStatus.ADMIN_REVIEWING,
    CaseStatus.DIRECTOR_REVIEWING,
    CaseStatus.PROCESSING_DELEGATION,
    CaseStatus.IN_PROGRESS,
    CaseStatus.APPLYING_CLOSURE,
    CaseStatus.CLOSURE_APPROVED,
    CaseStatus.ARCHIVED
  ];

  // 字段中文映射
  private readonly KEY_MAP: Record<string, string> = {
    // 基本信息
    'case_number': '案件编号',
    'case_cause': '案由',
    'lawyer': '承办律师',
    'status': '案件状态',
    'created_at': '创建时间',
    'updated_at': '更新时间',
    'is_paid': '缴费状态',
    'parties': '当事人信息',

    // 案件内容
    'phone': '联系电话',
    'opposing_party': '对方当事人',
    'accepting_authority': '受理机关',
    'contract_number': '委托合同编号',
    'court_case_number': '法院案件编号',
    'litigation_request': '诉讼请求',
    'litigation_amount': '诉讼标的额',
    'agreed_attorney_fee': '商定律师费',
    'agency_stage': '代理阶段',
    'case_summary': '案情摘要',
    'comments': '备注'
  };

  // 导出工具函数供模板使用
  getUserDisplayName = getUserDisplayName;
  getPartyTypeText = getPartyTypeText;

  // 判断用户身份：行政人员或律师
  getUserRole(user: any): string {
    if (user.profile?.has_admin_approval_permission && !user.profile?.has_director_approval_permission) {
      return '行政人员';
    } else {
      return '律师';
    }
  }

  // 添加关联当事人属性
  naturalPerson: any = null;
  legalEntity: any = null;
  clientLoading = false;
  clientError: string | null = null;

  // 添加提款申请相关属性
  hasMainLawyerPendingWithdrawal = false;
  collaboratorsPendingWithdrawal: Map<number, boolean> = new Map();
  withdrawalLoading = false;

  // 剩余可提取金额相关属性
  mainLawyerAvailableAmount = 0;
  collaboratorsAvailableAmount: Map<number, number> = new Map();

  // 标记缴费相关属性
  isMarkingPaid = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private caseService: CaseService,
    private authService: AuthService,
    private withdrawalService: WithdrawalService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private http: HttpClient
  ) {
    this.canApproveCases$ = this.authService.user$.pipe(
      map(user => {
        console.log('Current user:', user);
        // 只检查新版权限
        return !!user?.profile?.has_admin_approval_permission ||
               !!user?.profile?.has_director_approval_permission;
      }),
      retryWhen(errors =>
        errors.pipe(
          tap(err => console.log('Error getting user info, retrying...', err)),
          delay(1000),
          take(3)
        )
      )
    );
  }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadCase(+id);
    }

    // 检查当前用户是否是案件所有人，并设置当前用户
    this.authService.user$.subscribe(user => {
      this.currentUser = user; // 设置当前用户

      if (user && this.case) {
        this.isOwner = user.id === this.case.lawyer.id;

        // 如果是案件所有人，加载可用律师列表用于添加协作律师
        if (this.isOwner) {
          console.log('用户信息加载完成，当前用户是案件主办律师，加载可用律师列表');
          this.loadAvailableLawyers();
        }

        // 加载提款申请状态
        if (this.case.id) {
          this.loadWithdrawalStatus();
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // 这里添加一个用于获取案件当事人列表的方法
  get formattedParties(): any[] {
    if (!this.case || !this.case.parties)
      return [];

    return this.case.parties.map(party => {
      return {
        id: party.id,
        party_type: party.party_type,
        party_type_display: this.getPartyTypeText(party.party_type),
        natural_person: party.natural_person,
        legal_entity: party.legal_entity,
        internal_number: party.internal_number,
        remarks: party.remarks,
        is_client: party.is_client
      };
    });
  }

  // 加载案件详情时同时检查提款申请状态
  loadCase(id: number): void {
    this.isLoading = true;
    this.error = null;

    this.caseService.getCase(id).pipe(
      catchError(error => {
        console.error('加载案件失败:', error);
        this.error = '加载案件失败，请稍后再试。';
        this.isLoading = false;
        return of(null);
      })
    ).subscribe(caseData => {
      if (caseData) {
        this.case = caseData;
        this.isLoading = false;

        // 检查当前用户是否是主办律师
        this.checkUserRoles();

        // 加载协作律师
        this.loadCollaboratingLawyers(id);

        // 如果有关联当事人，加载关联当事人信息
        this.loadClientInfo(caseData);

        // 格式化当事人列表
        this.formatPartiesList(caseData);

        // 如果当前用户已经加载，立即加载提款状态和检查权限
        if (this.currentUser) {
          this.loadWithdrawalStatus();
          // 检查是否是案件主办律师，如果是则加载可用律师列表
          this.isOwner = this.currentUser.id === caseData.lawyer.id;
          if (this.isOwner) {
            console.log('当前用户是案件主办律师，加载可用律师列表');
            this.loadAvailableLawyers();
          }
        }

        // 对当事人列表进行类型验证和回填
        if (caseData.parties) {
          this.validateParties(caseData.parties);
        }
      }
    });
  }

  // 当前用户是否是主办律师
  isCurrentUserMainLawyer(): boolean {
    if (!this.case || !this.currentUser) {
      return false;
    }
    return this.case.lawyer.id === this.currentUser.id;
  }

  // 当前用户是否是协作律师
  isCurrentUserCollaborator(lawyerId: number): boolean {
    if (!this.currentUser) {
      return false;
    }
    return this.currentUser.id === lawyerId;
  }

  // 获取协作律师的提款申请状态
  getCollaboratorPendingWithdrawalStatus(lawyerId: number): boolean {
    return this.collaboratorsPendingWithdrawal.get(lawyerId) || false;
  }

  // 检查主办律师是否还有剩余可提取金额
  hasMainLawyerAvailableAmount(): boolean {
    return this.mainLawyerAvailableAmount > 0.01; // 允许小数点误差
  }

  // 获取协作律师的剩余可提取金额
  getCollaboratorAvailableAmount(lawyerId: number): number {
    return this.collaboratorsAvailableAmount.get(lawyerId) || 0;
  }

  // 检查协作律师是否还有剩余可提取金额
  hasCollaboratorAvailableAmount(lawyerId: number): boolean {
    const availableAmount = this.getCollaboratorAvailableAmount(lawyerId);
    return availableAmount > 0.01; // 允许小数点误差
  }

  // 加载提款申请状态
  loadWithdrawalStatus(): void {
    if (!this.case?.id) return;

    this.withdrawalLoading = true;

    // 获取提款申请状态
    this.withdrawalService.getCaseWithdrawalStatus(this.case.id).subscribe({
      next: (result) => {
        // 主办律师提款状态
        this.hasMainLawyerPendingWithdrawal = this.isCurrentUserMainLawyer() && result.has_pending_request;

        // 清除所有协作律师的状态
        this.collaboratorsPendingWithdrawal.clear();

        // 获取每个协作律师的提款状态
        if (this.collaborations && this.collaborations.length > 0) {
          this.collaborations.forEach(collab => {
            if (this.isCurrentUserCollaborator(collab.lawyer.id)) {
              this.withdrawalService.getCaseWithdrawalStatus(this.case!.id).subscribe({
                next: (status) => {
                  this.collaboratorsPendingWithdrawal.set(collab.lawyer.id, status.has_pending_request);
                },
                error: (error) => {
                  console.error('获取协作律师提款状态失败', error);
                  this.collaboratorsPendingWithdrawal.set(collab.lawyer.id, false);
                }
              });
            }
          });
        }
        this.withdrawalLoading = false;
      },
      error: (error) => {
        console.error('获取提款状态失败', error);
        this.withdrawalLoading = false;
        this.hasMainLawyerPendingWithdrawal = false;
      }
    });

    // 获取当前用户的剩余可提取金额
    this.withdrawalService.getCaseAvailableAmount(this.case.id).subscribe({
      next: (result) => {
        if (this.isCurrentUserMainLawyer()) {
          this.mainLawyerAvailableAmount = result.available_amount;
        }

        // 如果当前用户是协作律师，设置对应的剩余可提取金额
        if (this.collaborations && this.collaborations.length > 0) {
          this.collaborations.forEach(collab => {
            if (this.isCurrentUserCollaborator(collab.lawyer.id)) {
              this.collaboratorsAvailableAmount.set(collab.lawyer.id, result.available_amount);
            }
          });
        }
      },
      error: (error) => {
        console.error('获取剩余可提取金额失败', error);
        this.mainLawyerAvailableAmount = 0;
        this.collaboratorsAvailableAmount.clear();
      }
    });
  }

  // 打开提款对话框
  openWithdrawalDialog(amount: number): void {
    if (!this.case?.id) return;

    const dialogRef = this.dialog.open(WithdrawalDialogComponent, {
      width: '500px',
      data: {
        caseId: this.case.id,
        amount: amount
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.submitWithdrawalRequest(result);
      }
    });
  }

  // 提交提款申请
  submitWithdrawalRequest(request: WithdrawalRequestCreate): void {
    this.withdrawalService.createWithdrawal(request).subscribe({
      next: (response: any) => {
        this.snackBar.open('提款申请已提交', '关闭', {
          duration: 3000
        });
        // 更新提款状态和剩余可提取金额
        this.loadWithdrawalStatus();
      },
      error: (error: any) => {
        console.error('提款申请提交失败', error);

        // 处理验证错误
        let errorMessage = '提款申请提交失败';
        if (error.error) {
          if (error.error.amount && Array.isArray(error.error.amount)) {
            // 处理amount字段的验证错误
            errorMessage = error.error.amount[0];
          } else if (error.error.detail) {
            // 处理通用错误
            errorMessage = error.error.detail;
          } else if (error.error.non_field_errors && Array.isArray(error.error.non_field_errors)) {
            // 处理非字段错误
            errorMessage = error.error.non_field_errors[0];
          } else if (typeof error.error === 'string') {
            errorMessage = error.error;
          }
        }

        this.snackBar.open(errorMessage, '关闭', {
          duration: 5000
        });
      }
    });
  }

  // 检查当前用户角色
  checkUserRoles(): void {
    if (!this.case) return;

    // 使用user$而不是currentUser
    this.authService.user$.pipe(take(1)).subscribe(user => {
      if (user && this.case) {
        // 检查是否是主办律师
        this.isOwner = user.id === this.case.lawyer.id;

        // 检查是否是协作律师
        this.isCollaborator = this.case.collaborating_lawyers?.some(
          lawyer => lawyer.id === user.id
        ) || false;
      }
    });
  }

  // 获取案件当事人列表
  getPartiesList(): any[] {
    return this.case?.parties || [];
  }

  // 格式化当事人列表
  formatPartiesList(caseData: Case): void {
    if (!caseData.parties) return;

    caseData.parties.forEach(party => {
      party.party_type_display = this.getPartyTypeText(party.party_type);
    });
  }

  // 验证当事人类型并回填
  validateParties(parties: any[]): void {
    parties.forEach(party => {
      if (party.natural_person) {
        party.party_type = PartyType.PLAINTIFF;
      } else if (party.legal_entity) {
        party.party_type = PartyType.DEFENDANT;
      }
    });
  }

  // 加载案件关联的当事人（自然人或法人）
  loadClientInfo(caseData: Case): void {
    if (!caseData) return;

    this.clientLoading = true;
    this.naturalPerson = null;
    this.legalEntity = null;

    // 使用管道构建请求
    let request$: Observable<any> = of(null);

    // 检查是否有自然人ID
    if (caseData.natural_person_id) {
      request$ = this.http.get<any>(`${environment.apiUrl}/natural-persons/${caseData.natural_person_id}/`).pipe(
        tap(person => {
          this.naturalPerson = person;
          console.log('加载自然人成功:', person);
        }),
        catchError(error => {
          console.error('加载自然人信息出错:', error);
          this.clientError = '无法加载相关自然人信息';
          return of(null);
        })
      );
    }
    // 检查是否有法人ID
    else if (caseData.legal_entity_id) {
      request$ = this.http.get<any>(`${environment.apiUrl}/legal-entities/${caseData.legal_entity_id}/`).pipe(
        tap(entity => {
          this.legalEntity = entity;
          console.log('加载法人实体成功:', entity);
        }),
        catchError(error => {
          console.error('加载法人实体信息出错:', error);
          this.clientError = '无法加载相关法人实体信息';
          return of(null);
        })
      );
    }

    // 执行请求并完成加载状态
    request$.subscribe({
      complete: () => {
        this.clientLoading = false;
      }
    });
  }

  // 获取当事人类型的显示文本
  getClientType(): string {
    if (this.naturalPerson) {
      return '自然人';
    } else if (this.legalEntity) {
      return '法人单位';
    }
    return '未知';
  }

  // 获取当事人名称
  getClientName(): string {
    if (this.naturalPerson) {
      return this.naturalPerson.name;
    } else if (this.legalEntity) {
      return this.legalEntity.name;
    }
    return '未知';
  }

  // 获取当事人详细信息
  getClientInfo(): string[] {
    const info: string[] = [];

    if (this.naturalPerson) {
      if (this.naturalPerson.id_number) {
        info.push(`身份证号: ${this.naturalPerson.id_number}`);
      }
      if (this.naturalPerson.phone_number) {
        info.push(`手机号码: ${this.naturalPerson.phone_number}`);
      }
    } else if (this.legalEntity) {
      if (this.legalEntity.representative_name) {
        info.push(`代表人: ${this.legalEntity.representative_name}`);
      }
      if (this.legalEntity.credit_code) {
        info.push(`统一社会信用代码: ${this.legalEntity.credit_code}`);
      }
    }

    return info;
  }

  approveCase(action: string): void {
    if (!this.case || !this.approvalComment.trim()) {
      this.snackBar.open('请填写审批意见', '关闭', {
        duration: 3000,
      });
      return;
    }

    this.isLoading = true;

    // 根据当前案件状态确定审批类型
    const approvalType = this.case.status === CaseStatus.ADMIN_REVIEWING ? 'admin' : 'director';

    this.caseService.approveCase(this.case.id, action, this.approvalComment, approvalType).subscribe({
      next: (response) => {
        let message = '';
        switch (action) {
          case 'approve':
            message = '审批通过！';
            break;
          case 'reject':
            message = '审批不通过！';
            break;
          case 'revise':
            message = '已要求修改！';
            break;
        }
        this.snackBar.open(message, '关闭', {
          duration: 3000,
        });
        this.approvalComment = ''; // 清空评论
        this.loadCase(this.case!.id); // 重新加载案件信息
      },
      error: (error) => {
        this.snackBar.open('审批操作失败', '关闭', {
          duration: 3000,
        });
        this.isLoading = false;
        console.error('审批操作出错:', error);
      }
    });
  }

  showApprovalSection(): boolean {
    if (!this.case) return false;

    // 根据案件状态和用户权限决定是否显示审批部分
    if (this.case.status === CaseStatus.ADMIN_REVIEWING) {
      return this.authService.canAdminApproveCases();
    } else if (this.case.status === CaseStatus.DIRECTOR_REVIEWING ||
               this.case.status === CaseStatus.APPLYING_CLOSURE) {
      return this.authService.canDirectorApproveCases();
    }

    return false;
  }

  getContentKeys(): string[] {
    if (!this.case?.content) return [];
    return Object.keys(this.case.content);
  }

  getContentValue(key: string): string {
    if (!this.case || !this.case.content) {
      return '';
    }
    return this.case.content[key] || '';
  }

  // 获取中文key
  private getChineseKey(key: string): string {
    return this.KEY_MAP[key] || key;
  }

  // 获取英文key
  private getEnglishKey(chineseKey: string): string {
    const entry = Object.entries(this.KEY_MAP).find(([_, value]) => value === chineseKey);
    return entry ? entry[0] : chineseKey;
  }

  formatKey(key: string): string {
    return this.getChineseKey(key);
  }

  isPreFormatted(value: any): boolean {
    return typeof value === 'string' &&
      (value.includes('\n') || value.includes('  '));
  }

  getStatusText(status: CaseStatus | null): string {
    if (!status) return '';

    const statusMap: Record<CaseStatus, string> = {
      [CaseStatus.CREATED]: '创建',
      [CaseStatus.APPLYING]: '申请中',
      [CaseStatus.ADMIN_REVIEWING]: '行政审批中',
      [CaseStatus.DIRECTOR_REVIEWING]: '主任审批中',
      [CaseStatus.PROCESSING_DELEGATION]: '办理委托手续中',
      [CaseStatus.IN_PROGRESS]: '办案中',
      [CaseStatus.APPLYING_CLOSURE]: '申请结案中',
      [CaseStatus.CLOSURE_APPROVED]: '已通过主任审批结案',
      [CaseStatus.ARCHIVED]: '已归档'
    };
    return statusMap[status] || status;
  }

  // 获取下一个状态
  getNextStatus(currentStatus: CaseStatus): CaseStatus | null {
    const currentIndex = this.STATUS_ORDER.indexOf(currentStatus);
    if (currentIndex === -1 || currentIndex === this.STATUS_ORDER.length - 1) {
      return null;
    }
    return this.STATUS_ORDER[currentIndex + 1];
  }

  // 检查是否可以进入下一个状态
  canMoveToNextStatus(): boolean {
    if (!this.case || !this.isOwner) {
      return false;
    }

    const nextStatus = this.getNextStatus(this.case.status);
    if (!nextStatus) {
      return false;
    }

    // 检查特殊状态转换条件
    if (this.case.status === CaseStatus.ADMIN_REVIEWING) {
      return false; // 需要行政审批
    }
    if (this.case.status === CaseStatus.DIRECTOR_REVIEWING) {
      return false; // 需要主任审批
    }
    if (this.case.status === CaseStatus.APPLYING_CLOSURE) {
      return false; // 需要主任审批
    }

    return true;
  }

  // 移动到下一个状态
  moveToNextStatus(): void {
    if (!this.case || !this.canMoveToNextStatus()) {
      return;
    }

    const nextStatus = this.getNextStatus(this.case.status);
    if (!nextStatus) {
      return;
    }

    // 禁止直接从行政审批中转移到主任审批中
    if (this.case.status === CaseStatus.ADMIN_REVIEWING && nextStatus === CaseStatus.DIRECTOR_REVIEWING) {
      this.snackBar.open('不能直接将状态从"行政审批中"更新为"主任审批中"，必须通过行政审批流程', '关闭', {
        duration: 5000,
      });
      return;
    }

    // 如果是从IN_PROGRESS移动到APPLYING_CLOSURE，需要检查当事人身份证
    if (this.case.status === CaseStatus.IN_PROGRESS && nextStatus === CaseStatus.APPLYING_CLOSURE) {
      const idCardCheckResult = this.checkPartyIdCards();
      if (!idCardCheckResult.valid) {
        this.snackBar.open(`申请结案前请完善当事人身份证信息: ${idCardCheckResult.message}`, '关闭', {
          duration: 5000,
        });
        return;
      }
    }

    this.isLoading = true;
    this.caseService.updateStatus(this.case.id, nextStatus).subscribe({
      next: (updatedCase) => {
        this.case = updatedCase;
        this.snackBar.open('案件状态已更新', '关闭', {
          duration: 3000,
        });
        this.isLoading = false;
      },
      error: (error) => {
        this.snackBar.open(error.error?.error || '更新案件状态失败', '关闭', {
          duration: 3000,
        });
        this.isLoading = false;
        console.error('更新案件状态出错:', error);
      }
    });
  }

  // 检查所有自然人当事人是否都有身份证号
  checkPartyIdCards(): { valid: boolean; message: string } {
    if (!this.case || !this.case.parties) {
      return { valid: false, message: '没有当事人信息' };
    }

    // 筛选出所有自然人当事人
    const naturalPersonParties = this.case.parties.filter(party => party.natural_person !== null);

    if (naturalPersonParties.length === 0) {
      // 如果没有自然人当事人，则验证通过
      return { valid: true, message: '' };
    }

    // 检查每个自然人当事人是否有身份证号
    const missingIdCardParties: string[] = [];

    for (const party of naturalPersonParties) {
      // 确保natural_person存在且有id_number属性
      if (!party.natural_person?.id_number) {
        const partyName = party.natural_person?.name || '未知姓名';
        const partyType = this.getPartyTypeText(party.party_type);
        missingIdCardParties.push(`${partyType} ${partyName}`);
      }
    }

    if (missingIdCardParties.length > 0) {
      return {
        valid: false,
        message: `以下当事人缺少身份证号: ${missingIdCardParties.join(', ')}`
      };
    }

    return { valid: true, message: '' };
  }

  // 获取自定义属性
  getCustomProperties(): { name: string; value: string }[] {
    if (!this.case || !this.case.content) {
      return [];
    }

    // 预定义的标准属性名列表
    const standardProperties = [
      '案件类型', '委托人', '联系电话', '对方当事人', '受理机关',
      '委托合同编号', '法院案件编号', '诉讼请求', '诉讼标的额',
      '商定律师费', '代理阶段', '案情摘要', '备注'
    ];

    // 过滤出自定义属性
    return Object.entries(this.case.content)
      .filter(([key]) => !standardProperties.includes(key))
      .map(([name, value]) => ({ name, value: String(value) }));
  }

  // 检查是否有自定义属性
  hasCustomProperties(): boolean {
    return this.getCustomProperties().length > 0;
  }

  // 计算律师费分配
  calculateFeeDistribution(fee: string | undefined): { firmAmount: string, lawyerAmount: string } | null {
    if (!fee) return null;

    // 移除可能的非数字字符（如"¥"或"元"等）
    const numericFee = parseFloat(fee.replace(/[^\d.-]/g, ''));

    if (isNaN(numericFee) || numericFee <= 0) return null;

    const firmPercentage = 0.12; // 12%
    const firmAmount = numericFee * firmPercentage;
    const lawyerAmount = numericFee - firmAmount;

    // 格式化为保留两位小数的金额
    return {
      firmAmount: firmAmount.toFixed(2),
      lawyerAmount: lawyerAmount.toFixed(2)
    };
  }

  // 检查是否有律师费金额
  hasFeeAmount(): boolean {
    if (!this.case?.content) return false;
    const fee = this.case.content['商定律师费'];
    if (!fee) return false;

    // 检查是否有有效的数字
    const numericFee = parseFloat(fee.replace(/[^\d.-]/g, ''));
    return !isNaN(numericFee) && numericFee > 0;
  }

  // 加载协作律师详细信息
  loadCollaboratingLawyers(caseId: number): void {
    this.collaboratorsLoading = true;
    this.collaboratorsError = null;

    this.caseService.getCaseLawyers(caseId).subscribe({
      next: (collabs) => {
        this.collaborations = collabs;
        this.collaboratorsLoading = false;
      },
      error: (error) => {
        console.error('加载协作律师信息出错:', error);
        this.collaboratorsError = '无法加载协作律师信息';
        this.collaboratorsLoading = false;
      }
    });
  }

  // 加载所有可用律师
  loadAvailableLawyers(): void {
    console.log('开始加载可用律师列表...');
    this.authService.getAllUsers().subscribe({
      next: (users) => {
        console.log('获取到用户列表:', users);
        if (this.case && this.case.lawyer) {
          // 过滤掉案件创建人和已经是协作律师的用户
          this.availableLawyers = users.filter(user =>
            user.id !== this.case?.lawyer.id &&
            !this.case?.collaborating_lawyers?.some(lawyer => lawyer.id === user.id)
          );
          console.log('过滤后的可用律师列表:', this.availableLawyers);
        } else {
          this.availableLawyers = users;
          console.log('未过滤的用户列表:', this.availableLawyers);
        }
      },
      error: (error) => {
        console.error('加载可用律师列表出错:', error);
        this.snackBar.open('无法加载可用律师列表', '关闭', { duration: 3000 });
      }
    });
  }

  // 打开添加协作律师对话框
  openAddLawyerDialog(): void {
    const dialogRef = this.dialog.open(AddLawyerDialogComponent, {
      width: '400px'
    });

    // 设置可用律师列表
    const instance = dialogRef.componentInstance as AddLawyerDialogComponent;
    instance.availableLawyers = this.availableLawyers;

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.lawyerId && this.case) {
        this.addCollaborator(result.lawyerId, result.feeShareRatio, result.remarks);
      }
    });
  }

  // 添加协作律师
  addCollaborator(lawyerId: number, feeShareRatio: number, remarks?: string): void {
    if (!this.case) return;

    this.caseService.addCollaboratingLawyer(this.case.id, lawyerId, feeShareRatio, remarks).subscribe({
      next: (updatedCase) => {
        this.case = updatedCase;
        this.loadCollaboratingLawyers(updatedCase.id);
        this.snackBar.open('成功添加协作律师', '关闭', { duration: 3000 });

        // 更新可用律师列表
        this.loadAvailableLawyers();
      },
      error: (error) => {
        console.error('添加协作律师出错:', error);
        let errorMsg = '添加协作律师失败';
        if (error?.error?.error) {
          errorMsg = `添加失败: ${error.error.error}`;
        }
        this.snackBar.open(errorMsg, '关闭', { duration: 5000 });
      }
    });
  }

  // 移除协作律师
  removeCollaborator(lawyerId: number): void {
    if (!this.case) return;

    if (confirm('确定要移除该协作律师吗？')) {
      this.caseService.removeCollaboratingLawyer(this.case.id, lawyerId).subscribe({
        next: (updatedCase) => {
          this.case = updatedCase;
          this.loadCollaboratingLawyers(updatedCase.id);
          this.snackBar.open('成功移除协作律师', '关闭', { duration: 3000 });

          // 更新可用律师列表
          this.loadAvailableLawyers();
        },
        error: (error) => {
          console.error('移除协作律师出错:', error);
          this.snackBar.open('移除协作律师失败', '关闭', { duration: 3000 });
        }
      });
    }
  }

  // 打开添加当事人对话框
  openAddPartyDialog(): void {
    // 获取当前案件的类型
    const caseType = this.case?.content?.['案件类型'] || '民事案件';

    // 使用添加当事人对话框组件
    const dialogRef = this.dialog.open(AddPartyDialogComponent, {
      width: '600px',
      maxHeight: '80vh',
      disableClose: false,
      autoFocus: true,
      data: { caseType: caseType }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && this.case?.id) {
        // 添加当事人到案件
        this.addPartyToCase({
          entityType: result.entityType,
          entityId: result.entityId,
          party_type: result.party_type,
          is_client: result.is_client,
          internal_number: result.internal_number,
          remarks: result.remarks,
          name: result.name
        });
      }
    });
  }

  // 打开编辑当事人对话框
  openEditPartyDialog(party: any): void {
    if (!this.case || !party) return;

    const dialogRef = this.dialog.open(EditPartyRelationDialogComponent, {
      width: '600px',
      maxHeight: '80vh',
      disableClose: false,
      autoFocus: true,
      data: {
        caseType: this.case?.content?.['案件类型'] || '民事案件',
        partyData: party
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.updatePartyInCase(result);
      }
    });
  }

  // 添加当事人到案件
  addPartyToCase(partyData: any): void {
    if (!this.case) return;

    const caseId = this.case.id;

    // 构建API请求数据
    const data = {
      case: caseId,
      party_type: partyData.party_type,
      natural_person: partyData.entityType === 'natural' ? partyData.entityId : null,
      legal_entity: partyData.entityType === 'legal' ? partyData.entityId : null,
      is_client: partyData.is_client || false,
      internal_number: partyData.internal_number || '',
      remarks: partyData.remarks || ''
    };

    // 发送添加当事人请求
    this.http.post(`${environment.apiUrl}/case-parties/`, data).subscribe({
      next: (response) => {
        this.snackBar.open('当事人添加成功', '关闭', { duration: 3000 });
        // 重新加载案件信息以获取更新后的当事人列表
        this.loadCase(caseId);

        // 为新添加的当事人加载历史记录
        if (partyData.name) {
          const encodedName = encodeURIComponent(partyData.name);
          // 创建一个模拟的当事人对象，以便在加载后立即获取历史记录
          const newParty = {
            id: (response as any).id,
            natural_person: partyData.entityType === 'natural' ? { id: partyData.entityId, name: partyData.name } : null,
            legal_entity: partyData.entityType === 'legal' ? { id: partyData.entityId, name: partyData.name } : null,
          };
          const partyId = this.getPartyIdentifier(newParty);
          this.historyLoading.set(partyId, true);

          this.http.get<any[]>(`${environment.apiUrl}/legacy-cases/search_party/?q=${encodedName}`).subscribe({
            next: (results) => {
              if (results.length > 0) {
                this.partyHistoryRecords.set(partyId, results);
              }
              this.historyLoading.set(partyId, false);
            },
            error: (error) => {
              console.error(`获取新添加当事人 ${partyData.name} 的历史记录出错:`, error);
              this.historyLoading.set(partyId, false);
            }
          });
        }
      },
      error: (error) => {
        this.snackBar.open('当事人添加失败', '关闭', { duration: 3000 });
        console.error('添加当事人出错:', error);
      }
    });
  }

  // 更新案件中的当事人
  updatePartyInCase(partyData: any): void {
    if (!this.case || !partyData.id) return;

    // 查找原始当事人信息，获取natural_person和legal_entity关联
    const originalParty = this.formattedParties.find(p => p.id === partyData.id);
    if (!originalParty) {
      this.snackBar.open('无法找到当事人信息', '关闭', { duration: 3000 });
      return;
    }

    // 准备更新当事人的数据，保留natural_person或legal_entity关联
    const data: Record<string, any> = {
      party_type: partyData.party_type,
      is_client: partyData.is_client,
      internal_number: partyData.internal_number || '',
      remarks: partyData.remarks || '',
      // 保留原来的实体关联信息
      natural_person: originalParty.natural_person?.id || null,
      legal_entity: originalParty.legal_entity?.id || null
    };

    // 发送请求更新当事人
    this.http.patch(`${environment.apiUrl}/case-parties/${partyData.id}/`, data).subscribe({
      next: () => {
        this.snackBar.open('更新当事人成功', '关闭', { duration: 3000 });
        // 重新加载案件信息以更新当事人列表
        if (this.case) {
          this.loadCase(this.case.id);
        }
      },
      error: (error) => {
        console.error('更新当事人失败:', error);
        let errorMessage = '更新当事人失败';
        if (error.error?.non_field_errors && error.error.non_field_errors.length > 0) {
          errorMessage += ': ' + error.error.non_field_errors.join(', ');
        } else if (error.error?.detail) {
          errorMessage += ': ' + error.error.detail;
        }
        this.snackBar.open(errorMessage, '关闭', { duration: 5000 });
      }
    });
  }

  // 删除当事人
  deleteParty(party: any): void {
    if (!this.case?.id || !party?.id) return;

    const partyId = this.getPartyIdentifier(party);

    if (confirm('确定删除此当事人吗？')) {
      this.http.delete(`${environment.apiUrl}/case-parties/${party.id}/`).subscribe({
        next: () => {
          this.snackBar.open('当事人删除成功', '关闭', { duration: 3000 });
          // 删除该当事人的历史记录缓存
          this.partyHistoryRecords.delete(partyId);
          this.expandedPartyHistory.delete(partyId);
          this.historyLoading.delete(partyId);
          // 重新加载案件信息以更新当事人列表
          this.loadCase(this.case!.id);
        },
        error: (error) => {
          this.snackBar.open('当事人删除失败', '关闭', { duration: 3000 });
          console.error('删除当事人出错:', error);
        }
      });
    }
  }

  // 检查是否可以编辑敏感案件状态
  canEditSensitiveStatus(): boolean {
    if (!this.case || !this.isOwner) {
      return false;
    }

    // 主任审批过后不能再编辑敏感案件状态
    const passedDirectorReview = this.STATUS_ORDER.indexOf(this.case.status) >
                                 this.STATUS_ORDER.indexOf(CaseStatus.DIRECTOR_REVIEWING);

    return !passedDirectorReview;
  }

  // 切换案件敏感状态
  toggleSensitiveStatus(event: any): void {
    if (!this.case || !this.canEditSensitiveStatus()) {
      return;
    }

    const newValue = event.checked;

    this.isLoading = true;
    this.caseService.updateCase(this.case.id, { is_sensitive: newValue }).subscribe({
      next: (updatedCase: Case) => {
        this.case = updatedCase;
        this.snackBar.open(
          newValue ? '已标记为重大敏感案件' : '已取消重大敏感案件标记',
          '关闭',
          { duration: 3000 }
        );
        this.isLoading = false;
      },
      error: (error: any) => {
        this.snackBar.open('更新案件状态失败', '关闭', { duration: 3000 });
        this.isLoading = false;
        console.error('更新案件敏感状态出错:', error);
      }
    });
  }

  // 检查是否可以编辑风险代理状态
  canEditRiskAgencyStatus(): boolean {
    if (!this.case || !this.isOwner) {
      return false;
    }

    // 主任审批过后不能再编辑风险代理状态
    const passedDirectorReview = this.STATUS_ORDER.indexOf(this.case.status) >
                                 this.STATUS_ORDER.indexOf(CaseStatus.DIRECTOR_REVIEWING);

    return !passedDirectorReview;
  }

  // 切换案件风险代理状态
  toggleRiskAgencyStatus(event: any): void {
    if (!this.case || !this.canEditRiskAgencyStatus()) {
      return;
    }

    const newValue = event.checked;

    this.isLoading = true;
    this.caseService.updateCase(this.case.id, { is_risk_agency: newValue }).subscribe({
      next: (updatedCase: Case) => {
        this.case = updatedCase;
        this.snackBar.open(
          newValue ? '已标记为风险代理案件' : '已取消风险代理案件标记',
          '关闭',
          { duration: 3000 }
        );
        this.isLoading = false;
      },
      error: (error: any) => {
        this.snackBar.open('更新案件状态失败', '关闭', { duration: 3000 });
        this.isLoading = false;
        console.error('更新案件风险代理状态出错:', error);
      }
    });
  }

  // 获取当事人唯一标识符（用于历史记录映射）
  getPartyIdentifier(party: any): string {
    if (party.natural_person) {
      return `natural_${party.natural_person.id}_${party.natural_person.name}`;
    } else if (party.legal_entity) {
      return `legal_${party.legal_entity.id}_${party.legal_entity.name}`;
    }
    return `unknown_${party.id}`;
  }

  // 获取当事人名称
  getPartyName(party: any): string {
    return party.natural_person?.name || party.legal_entity?.name || '未知';
  }

  // 预加载所有当事人的历史记录数据
  preloadPartyHistoryData(): void {
    if (!this.case || !this.case.parties) return;

    this.case.parties.forEach(party => {
      const partyName = this.getPartyName(party);
      if (partyName && partyName !== '未知') {
        // 设置加载状态
        const partyId = this.getPartyIdentifier(party);
        this.historyLoading.set(partyId, true);

        // 使用encodeURIComponent确保中文正确编码
        const encodedName = encodeURIComponent(partyName);
        this.http.get<any[]>(`${environment.apiUrl}/legacy-cases/search_party/?q=${encodedName}`).subscribe({
          next: (results) => {
            if (results.length > 0) {
              this.partyHistoryRecords.set(partyId, results);
            }
            this.historyLoading.set(partyId, false);
          },
          error: (error) => {
            console.error(`获取当事人 ${partyName} 的历史记录出错:`, error);
            this.historyLoading.set(partyId, false);
          }
        });
      }
    });
  }

  // 切换当事人历史记录的折叠状态
  togglePartyHistory(partyId: string): void {
    if (this.expandedPartyHistory.has(partyId)) {
      this.expandedPartyHistory.delete(partyId);
    } else {
      this.expandedPartyHistory.add(partyId);
    }
  }

  // 检查当事人历史记录是否展开
  isPartyHistoryExpanded(partyId: string): boolean {
    return this.expandedPartyHistory.has(partyId);
  }

  // 获取当事人历史记录数量
  getPartyHistoryCount(partyId: string): number {
    return this.partyHistoryRecords.get(partyId)?.length || 0;
  }

  // 获取当事人历史记录
  getPartyHistory(partyId: string): any[] {
    return this.partyHistoryRecords.get(partyId) || [];
  }

  // 检查是否正在加载历史记录
  isHistoryLoading(partyId: string): boolean {
    return this.historyLoading.get(partyId) || false;
  }

  // 计算承办律师的分成比例
  calculateMainLawyerFeeShare(): number {
    if (!this.collaborations || this.collaborations.length === 0) {
      return 1.0; // 如果没有协作律师，承办律师获得100%
    }

    // 计算所有协作律师的分成比例总和
    const totalCollaboratorShare = this.collaborations.reduce((sum, collab) => {
      // 确保fee_share_ratio是数字类型
      const ratio = typeof collab.fee_share_ratio === 'string'
        ? parseFloat(collab.fee_share_ratio)
        : (collab.fee_share_ratio || 0);
      return sum + ratio;
    }, 0);

    // 承办律师的分成比例 = 1 - 协作律师分成总和
    const mainLawyerShare = 1.0 - totalCollaboratorShare;

    // 确保分成比例不会小于0
    return Math.max(0, mainLawyerShare);
  }

  // 获取承办律师分成比例的百分比显示
  getMainLawyerFeeSharePercentage(): string {
    const share = this.calculateMainLawyerFeeShare();
    return (share * 100).toFixed(2);
  }

  // 计算所有协作律师的总分成比例
  getTotalCollaboratorFeeShare(): number {
    if (!this.collaborations || this.collaborations.length === 0) {
      return 0;
    }

    return this.collaborations.reduce((sum, collab) => {
      // 确保fee_share_ratio是数字类型
      const ratio = typeof collab.fee_share_ratio === 'string'
        ? parseFloat(collab.fee_share_ratio)
        : (collab.fee_share_ratio || 0);
      return sum + ratio;
    }, 0);
  }

  // 获取协作律师总分成比例的百分比显示
  getTotalCollaboratorFeeSharePercentage(): string {
    const totalShare = this.getTotalCollaboratorFeeShare();
    return (totalShare * 100).toFixed(2);
  }

  // 检查分成比例是否合理（总和不超过100%）
  isFeeShareValid(): boolean {
    const totalCollaboratorShare = this.getTotalCollaboratorFeeShare();
    return totalCollaboratorShare <= 1.0;
  }

  // 获取可分配的律师费总额
  getTotalLawyerFee(): number {
    if (this.case?.agreed_lawyer_fee) {
      // 使用数据库中存储的律师费乘以88%（律师所得部分）
      return this.case.agreed_lawyer_fee * 0.88;
    } else if (this.case?.content && this.case.content['商定律师费']) {
      // 使用content中存储的律师费
      const feeDistribution = this.calculateFeeDistribution(this.case.content['商定律师费']);
      if (feeDistribution) {
        return parseFloat(feeDistribution.lawyerAmount);
      }
    }
    return 0; // 如果没有设置律师费，则返回0
  }

  // 计算主办律师获得的具体金额
  getMainLawyerFeeAmount(): number {
    const totalFee = this.getTotalLawyerFee();
    const shareRatio = this.calculateMainLawyerFeeShare();
    return totalFee * shareRatio;
  }

  // 计算特定协作律师获得的具体金额
  getCollaboratorFeeAmount(feeShareRatio: number): number {
    const totalFee = this.getTotalLawyerFee();
    return totalFee * feeShareRatio;
  }

  // 计算所有协作律师获得的总金额
  getTotalCollaboratorFeeAmount(): number {
    const totalFee = this.getTotalLawyerFee();
    const shareRatio = this.getTotalCollaboratorFeeShare();
    return totalFee * shareRatio;
  }

  // 编辑费用分成比例
  editFeeShareRatio(collaboration: CaseLawyerCollaboration): void {
    const dialogRef = this.dialog.open(EditCollaborationDialogComponent, {
      width: '400px',
      data: {
        feeShareRatio: collaboration.fee_share_ratio,
        lawyerName: getUserDisplayName(collaboration.lawyer),
        remarks: collaboration.remarks
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && result.feeShareRatio !== undefined) {
        this.updateCollaborationDetails(collaboration.id, result.feeShareRatio, result.remarks);
      }
    });
  }

  // 更新协作律师详情
  updateCollaborationDetails(collaborationId: number, feeShareRatio: number, remarks: string): void {
    this.caseService.updateCaseLawyerCollaboration(collaborationId, { fee_share_ratio: feeShareRatio, remarks: remarks }).subscribe({
      next: () => {
        this.snackBar.open('协作详情更新成功', '关闭', { duration: 3000 });
        // 重新加载协作律师信息
        if (this.case) {
          this.loadCollaboratingLawyers(this.case.id);
        }
      },
      error: (error: any) => {
        console.error('更新协作详情失败:', error);
        this.snackBar.open('更新协作详情失败', '关闭', { duration: 3000 });
      }
    });
  }

  // 加载协作律师提款状态
  loadCollaboratorWithdrawalStatus(): void {
    if (!this.case?.id || !this.collaborations || this.collaborations.length === 0) {
      return;
    }

    // 清除所有协作律师的状态
    this.collaboratorsPendingWithdrawal.clear();

    // 获取每个协作律师的提款状态
    this.collaborations.forEach(collab => {
      if (this.isCurrentUserCollaborator(collab.lawyer.id)) {
        this.withdrawalService.getCaseWithdrawalStatus(this.case!.id).subscribe({
          next: (status: any) => {
            this.collaboratorsPendingWithdrawal.set(collab.lawyer.id, status.has_pending_request);
          },
          error: (error: any) => {
            console.error('获取协作律师提款状态失败', error);
            this.collaboratorsPendingWithdrawal.set(collab.lawyer.id, false);
          }
        });
      }
    });
  }

  /**
   * 判断当前用户是否可以标记案件为已缴费
   * 条件：1. 是行政人员 2. 案件未缴费 3. 有商定律师费
   */
  canMarkAsPaid(): boolean {
    if (!this.case || !this.currentUser) {
      return false;
    }

    // 必须是行政人员
    const isAdmin = this.currentUser.profile?.has_case_admin_approval_permission;
    if (!isAdmin) {
      return false;
    }

    // 案件必须未缴费
    if (this.case.is_paid) {
      return false;
    }

    // 必须有商定律师费
    if (!this.case.agreed_lawyer_fee || this.case.agreed_lawyer_fee <= 0) {
      return false;
    }

    return true;
  }

  /**
   * 计算剩余未缴款金额
   */
  calculateRemainingAmount(): number {
    if (!this.case?.agreed_lawyer_fee) {
      return 0;
    }

    // 从 FinanceService 获取案件收入记录总额
    let totalExistingIncome = 0;

    // 这里我们需要调用财务服务来获取实际的收入记录
    // 由于这是一个同步方法，我们先返回商定律师费作为默认值
    // 实际的计算将在 markAsPaid 方法中进行
    return this.case.agreed_lawyer_fee;
  }

  /**
   * 编辑商定律师费
   */
  editAgreedLawyerFee(): void {
    if (!this.case) {
      return;
    }

    const currentFee = this.case.agreed_lawyer_fee ||
                      (this.case.content && this.case.content['商定律师费'] ?
                       parseFloat(this.case.content['商定律师费']) : 0);

    const newFeeStr = prompt(`请输入新的商定律师费金额：`, currentFee.toString());

    if (newFeeStr === null) {
      // 用户取消了操作
      return;
    }

    const newFee = parseFloat(newFeeStr);

    if (isNaN(newFee) || newFee < 0) {
      this.snackBar.open('请输入有效的金额', '关闭', {
        duration: 3000,
        panelClass: ['error-snackbar']
      });
      return;
    }

    // 更新案件信息
    this.caseService.updateCase(this.case.id, { agreed_lawyer_fee: newFee }).subscribe({
      next: (updatedCase: Case) => {
        this.case = updatedCase;
        this.snackBar.open('商定律师费已更新', '关闭', {
          duration: 3000,
          panelClass: ['success-snackbar']
        });

        // 付款状态会在后端自动检查和更新
      },
      error: (error: any) => {
        console.error('更新商定律师费失败:', error);
        this.snackBar.open('更新失败', '关闭', {
          duration: 3000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  /**
   * 标记案件为已缴费
   */
  markAsPaid(): void {
    if (!this.case || this.isMarkingPaid) {
      return;
    }

    // 首先获取案件的财务收入记录来计算剩余金额
    this.isMarkingPaid = true;

    // 先调用财务记录接口获取已有收入
    this.http.get(`/api/finance/records/?case=${this.case.id}&account_type=case_income`).subscribe({
      next: (financeData: any) => {
        let totalExistingIncome = 0;

        if (financeData.results && financeData.results.length > 0) {
          totalExistingIncome = financeData.results.reduce((sum: number, record: any) => {
            return sum + (record.amount || 0);
          }, 0);
        }

        const remainingAmount = (this.case!.agreed_lawyer_fee || 0) - totalExistingIncome;

        let confirmMessage: string;
        if (remainingAmount > 0) {
          confirmMessage = `确定要将案件"${this.case!.case_cause}"标记为已缴费吗？\n` +
                          `商定律师费：¥${this.case!.agreed_lawyer_fee || 0}\n` +
                          `已收金额：¥${totalExistingIncome}\n` +
                          `这将创建剩余未缴款 ¥${remainingAmount} 的财务记录。`;
        } else if (remainingAmount === 0) {
          confirmMessage = `确定要将案件"${this.case!.case_cause}"标记为已缴费吗？\n` +
                          `该案件费用已全部到账，无需创建额外财务记录。`;
        } else {
          confirmMessage = `确定要将案件"${this.case!.case_cause}"标记为已缴费吗？\n` +
                          `注意：已收费用超出商定金额 ¥${Math.abs(remainingAmount)}`;
        }

        this.isMarkingPaid = false;

        // 显示确认对话框
        const confirmed = confirm(confirmMessage);

        if (!confirmed) {
          return;
        }

        // 执行标记为已缴费
        this.performMarkAsPaid();
      },
      error: (error: any) => {
        console.error('获取财务记录失败:', error);
        this.isMarkingPaid = false;

        // 如果获取财务记录失败，仍然显示简化的确认对话框
        const confirmed = confirm(
          `确定要将案件"${this.case!.case_cause}"标记为已缴费吗？\n` +
          `这将根据已有收入情况创建相应的财务记录。`
        );

        if (confirmed) {
          this.performMarkAsPaid();
        }
      }
    });
  }

  /**
   * 执行标记为已缴费的操作
   */
  private performMarkAsPaid(): void {
    if (!this.case || this.isMarkingPaid) {
      return;
    }

    this.isMarkingPaid = true;

    this.caseService.markAsPaid(this.case.id).subscribe({
      next: (response: any) => {
        let successMessage = response.message || '案件已标记为已缴费';

        this.snackBar.open(successMessage, '关闭', {
          duration: 5000,
          panelClass: ['success-snackbar']
        });

        // 更新案件数据
        if (response.case) {
          this.case = response.case;
        }

        this.isMarkingPaid = false;
      },
      error: (error: any) => {
        console.error('标记缴费失败:', error);
        let errorMessage = '标记缴费失败';

        if (error.error?.error) {
          errorMessage = error.error.error;
        } else if (error.message) {
          errorMessage = error.message;
        }

        this.snackBar.open(errorMessage, '关闭', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });

        this.isMarkingPaid = false;
      }
    });
  }

  // 检查是否可以删除案件
  canDeleteCase(): boolean {
    if (!this.case || !this.isOwner) {
      return false;
    }
    
    // 只有在主任审批前可以删除（创建、申请中、行政审批中）
    return this.case.status === CaseStatus.CREATED || 
           this.case.status === CaseStatus.APPLYING || 
           this.case.status === CaseStatus.ADMIN_REVIEWING;
  }

  // 删除案件
  deleteCase(): void {
    if (!this.case || !this.canDeleteCase()) {
      return;
    }

    const confirmMessage = `确定要删除案件"${this.case.case_cause}"吗？此操作不可撤销！`;
    
    if (confirm(confirmMessage)) {
      this.isLoading = true;
      this.caseService.deleteCase(this.case.id).subscribe({
        next: () => {
          this.snackBar.open('案件删除成功', '关闭', {
            duration: 3000,
          });
          // 删除成功后返回案件列表
          this.router.navigate(['/cases']);
        },
        error: (error) => {
          this.isLoading = false;
          const errorMessage = error.error?.error || '删除案件失败';
          this.snackBar.open(errorMessage, '关闭', {
            duration: 3000,
          });
          console.error('删除案件出错:', error);
        }
      });
    }
  }
}
