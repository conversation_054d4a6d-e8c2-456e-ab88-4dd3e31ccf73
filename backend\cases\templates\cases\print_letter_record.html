<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ letter_type_display }}登记表 - {{ year }}年</title>
    <style>
        @page {
            size: A4;
            margin: 20mm 15mm;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            
            .no-print {
                display: none;
            }
            
            /* 打印时审批人列留空 */
            .print-empty {
                visibility: hidden;
            }
        }
        
        body {
            font-family: "SimSun", "宋体", serif;
            font-size: 12px;
            line-height: 1.2;
            margin: 0;
            padding: 15px;
            background-color: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0 0 5px 0;
        }
        
        .header h2 {
            font-size: 14px;
            font-weight: bold;
            margin: 0 0 10px 0;
        }
        
        .header .year {
            font-size: 14px;
            margin-bottom: 15px;
        }
        
        .print-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
            margin-bottom: 20px;
            font-size: 12px;
        }
        
        .print-table th,
        .print-table td {
            border: 1px solid #000;
            padding: 6px 4px;
            text-align: center;
            vertical-align: middle;
            word-wrap: break-word;
            font-size: 12px;
        }
        
        .print-table th {
            background-color: white;
            font-weight: bold;
            height: 30px;
        }
        
        .print-table td {
            height: 25px;
        }
        
        /* 列宽设置 */
        .col-sequence { width: 5%; }
        .col-number { width: 12%; }
        .col-date { width: 10%; }
        .col-case { width: 10%; }
        .col-client { width: 15%; }
        .col-quantity { width: 6%; }
        .col-unit { width: 12%; }
        .col-lawyer { width: 10%; }
        .col-approver { width: 10%; }
        .col-remarks { width: 10%; }
        
        /* 突出显示当前打印的记录 */
        .current-record {
            background-color: #ffffcc !important;
        }
        
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 12px;
        }
        
        .print-controls {
            margin: 0 0 20px 0;
            text-align: center;
        }
        
        .print-controls button {
            padding: 8px 16px;
            margin: 0 8px;
            font-size: 14px;
            cursor: pointer;
            border-radius: 4px;
            border: 1px solid;
        }
        
        .btn-print {
            background-color: #5c85ff;
            color: white;
            border-color: #5c85ff;
        }
        
        .btn-print:hover {
            background-color: #4a73e8;
        }
        
        .btn-close {
            background-color: #f44336;
            color: white;
            border-color: #f44336;
        }
        
        .btn-close:hover {
            background-color: #d32f2f;
        }
    </style>
</head>
<body>
    {% if error %}
    <div class="error-message" style="color: red; text-align: center; font-size: 16px; margin: 50px 0;">
        <h2>错误</h2>
        <p>{{ error }}</p>
    </div>
    {% else %}
    <div class="header">
        <h1>广东承境律师事务所</h1>
        <h2>{{ year }}年度广东承境律师事务所{{ letter_type_display }}登记表（{{ letter_type }}）</h2>
        <div class="year">{{ year }}年度</div>
    </div>
    
    <table class="print-table">
        <thead>
            <tr>
                <th class="col-sequence">序号</th>
                <th class="col-number">函数编号</th>
                <th class="col-date">日期</th>
                <th class="col-case">对应案件号</th>
                <th class="col-client">委托人/当事人</th>
                <th class="col-quantity">数量</th>
                <th class="col-unit">致函单位</th>
                <th class="col-lawyer">使用律师</th>
                <th class="col-approver">审批人</th>
                <th class="col-remarks">备注</th>
            </tr>
        </thead>
        <tbody>
            {% for record in records %}
            <tr {% if record.id == current_record.id %}class="current-record"{% endif %}>
                <td class="col-sequence">{{ forloop.counter }}</td>
                <td class="col-number">{% if record.letter_number %}（{{ record.year }}）{{ record.letter_type }}{{ record.letter_number }}号{% else %}-{% endif %}</td>
                <td class="col-date">{{ record.date|date:"Y-m-d" }}</td>
                <td class="col-case">{{ record.case_number|default:"-" }}</td>
                <td class="col-client">{{ record.client_name|default:"-" }}</td>
                <td class="col-quantity">{{ record.quantity|default:"1" }}</td>
                <td class="col-unit">{{ record.recipient_unit|default:"-" }}</td>
                <td class="col-lawyer">{{ record.lawyer_name|default:"-" }}</td>
                <td class="col-approver"></td>
                <td class="col-remarks">{{ record.remarks|default:"-" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10">暂无记录</td>
            </tr>
            {% endfor %}
            
            {% comment %}
            填充空行到12行
            {% endcomment %}
            {% for i in empty_rows %}
                <tr>
                    <td class="col-sequence">&nbsp;</td>
                    <td class="col-number">&nbsp;</td>
                    <td class="col-date">&nbsp;</td>
                    <td class="col-case">&nbsp;</td>
                    <td class="col-client">&nbsp;</td>
                    <td class="col-quantity">&nbsp;</td>
                    <td class="col-unit">&nbsp;</td>
                    <td class="col-lawyer">&nbsp;</td>
                    <td class="col-approver"><span class="print-empty">&nbsp;</span></td>
                    <td class="col-remarks">&nbsp;</td>
                </tr>
            {% endfor %}
        </tbody>
    </table>
    
    <div class="print-controls no-print">
        <button class="btn-print" onclick="window.print()">打印</button>
        <button class="btn-close" onclick="window.close()">关闭</button>
    </div>
    
    <div class="footer">
        <!-- <p>打印时间：{{ "now"|date:"Y年n月j日 H:i" }}</p> -->
    </div>
    {% endif %}
    
    <script>
        // 自动打印（可选）
        // window.onload = function() {
        //     window.print();
        // };
    </script>
</body>
</html> 