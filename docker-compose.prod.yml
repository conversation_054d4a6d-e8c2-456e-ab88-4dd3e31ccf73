services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/mediafiles
      - ./fonts:/app/fonts
    environment:
      - DJANGO_SETTINGS_MODULE=law_firm.settings_prod
    env_file:
      - ./backend/.env.prod
    depends_on:
      - db

  frontend: # with nginx service
    build:
      context: .
      dockerfile: ./frontend/Dockerfile.prod
    environment:
      - DJANGO_SETTINGS_MODULE=law_firm.settings_prod
    ports:
      - "4200:80"
    depends_on:
      - backend

  db:
    image: postgres:15
    volumes:
      - postgres_data:/var/lib/postgresql/data
    env_file:
      - ./backend/.env.prod

volumes:
  postgres_data:
  static_volume:
  media_volume: