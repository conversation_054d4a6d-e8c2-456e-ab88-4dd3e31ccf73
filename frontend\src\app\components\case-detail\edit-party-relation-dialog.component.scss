.dialog-content {
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-bottom: 16px;
}

.party-info-section, .relation-edit-section {
  margin-bottom: 24px;
}

.party-info-card {
  padding: 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f5f5f5;

  .party-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .party-name {
      font-weight: 500;
      font-size: 16px;
    }

    .party-type-badge {
      margin-left: 8px;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 0.8em;

      &.natural {
        background-color: #2196f3;
        color: white;
      }

      &.legal {
        background-color: #ff9800;
        color: white;
      }
    }
  }

  .party-details p {
    margin: 5px 0;
  }
}

.client-checkbox {
  display: block;
  margin: 15px 0;
} 