import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RouterModule } from '@angular/router';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { Observable } from 'rxjs';

import { AuthService } from '../../services/auth.service';
import { getUserDisplayName } from '../../utils/user.utils';

@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrls: ['./user-profile.component.scss'],
  standalone: true,
  imports: [
    CommonModule, 
    MatCardModule,
    MatDividerModule,
    MatProgressSpinnerModule,
    RouterModule,
    MatIconModule,
    MatButtonModule,
    MatChipsModule
  ]
})
export class UserProfileComponent implements OnInit {
  isLoading = true;
  error: string | null = null;
  user$: Observable<any>;

  // 导出工具函数供模板使用
  getUserDisplayName = getUserDisplayName;

  constructor(public authService: AuthService) {
    this.user$ = this.authService.user$;
  }

  ngOnInit(): void {
    this.loadUserProfile();
  }

  loadUserProfile(): void {
    this.isLoading = true;
    this.authService.getCurrentUser().subscribe({
      next: () => {
        this.isLoading = false;
      },
      error: (error) => {
        this.error = '加载用户资料失败';
        this.isLoading = false;
        console.error('加载用户资料出错:', error);
      }
    });
  }
  
  // 获取用户角色权限列表
  getUserRoles(user: any): string[] {
    const roles: string[] = ['律师'];
    
    if (user.profile?.has_admin_approval_permission) {
      roles.push('行政审批权限');
    }
    
    if (user.profile?.has_director_approval_permission) {
      roles.push('主任审批权限');
    }
    
    return roles;
  }
  
  // 判断用户是否具有任何审批权限
  hasAnyApprovalPermission(user: any): boolean {
    return user.profile?.has_admin_approval_permission ||
           user.profile?.has_director_approval_permission;
  }
  
  // 判断用户身份：行政人员或律师
  getUserRole(user: any): string {
    if (user.profile?.has_admin_approval_permission && !user.profile?.has_director_approval_permission) {
      return '行政人员';
    } else {
      return '律师';
    }
  }
  
  // 判断用户是否仅具有行政审批权限
  isAdminOnly(user: any): boolean {
    return user.profile?.has_admin_approval_permission && !user.profile?.has_director_approval_permission;
  }
} 