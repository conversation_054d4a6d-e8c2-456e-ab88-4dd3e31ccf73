import { Injectable } from '@angular/core';

export interface CourtFeeResult {
  totalFee: number;
  details: string[];
}

@Injectable({
  providedIn: 'root'
})
export class CourtFeeService {

  constructor() { }

  /**
   * 计算民事财产案件受理费
   * @param amount 争议金额（元）
   * @returns 计算结果
   */
  calculatePropertyCaseFee(amount: number): CourtFeeResult {
    const result: CourtFeeResult = {
      totalFee: 0,
      details: []
    };

    // 民事财产案件受理费计算
    if (amount <= 10000) {
      // 不超过1万元的，每件交纳50元
      result.totalFee = 50;
      result.details.push(`不超过1万元的部分: 固定收费50元`);
    } else {
      // 基础费用
      result.totalFee = 50;
      result.details.push(`不超过1万元的部分: 固定收费50元`);
      
      // 超过1万元至10万元的部分，按照2.5％交纳
      if (amount > 10000) {
        const range = Math.min(amount, 100000) - 10000;
        if (range > 0) {
          const fee = range * 0.025;
          result.totalFee += fee;
          result.details.push(`超过1万元至10万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (2.5%)`);
        }
      }
      
      // 超过10万元至20万元的部分，按照2％交纳
      if (amount > 100000) {
        const range = Math.min(amount, 200000) - 100000;
        if (range > 0) {
          const fee = range * 0.02;
          result.totalFee += fee;
          result.details.push(`超过10万元至20万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (2%)`);
        }
      }
      
      // 超过20万元至50万元的部分，按照1.5％交纳
      if (amount > 200000) {
        const range = Math.min(amount, 500000) - 200000;
        if (range > 0) {
          const fee = range * 0.015;
          result.totalFee += fee;
          result.details.push(`超过20万元至50万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (1.5%)`);
        }
      }
      
      // 超过50万元至100万元的部分，按照1％交纳
      if (amount > 500000) {
        const range = Math.min(amount, 1000000) - 500000;
        if (range > 0) {
          const fee = range * 0.01;
          result.totalFee += fee;
          result.details.push(`超过50万元至100万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (1%)`);
        }
      }
      
      // 超过100万元至200万元的部分，按照0.9％交纳
      if (amount > 1000000) {
        const range = Math.min(amount, 2000000) - 1000000;
        if (range > 0) {
          const fee = range * 0.009;
          result.totalFee += fee;
          result.details.push(`超过100万元至200万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (0.9%)`);
        }
      }
      
      // 超过200万元至500万元的部分，按照0.8％交纳
      if (amount > 2000000) {
        const range = Math.min(amount, 5000000) - 2000000;
        if (range > 0) {
          const fee = range * 0.008;
          result.totalFee += fee;
          result.details.push(`超过200万元至500万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (0.8%)`);
        }
      }
      
      // 超过500万元至1000万元的部分，按照0.7％交纳
      if (amount > 5000000) {
        const range = Math.min(amount, 10000000) - 5000000;
        if (range > 0) {
          const fee = range * 0.007;
          result.totalFee += fee;
          result.details.push(`超过500万元至1000万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (0.7%)`);
        }
      }
      
      // 超过1000万元至2000万元的部分，按照0.6％交纳
      if (amount > 10000000) {
        const range = Math.min(amount, 20000000) - 10000000;
        if (range > 0) {
          const fee = range * 0.006;
          result.totalFee += fee;
          result.details.push(`超过1000万元至2000万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (0.6%)`);
        }
      }
      
      // 超过2000万元的部分，按照0.5％交纳
      if (amount > 20000000) {
        const range = amount - 20000000;
        if (range > 0) {
          const fee = range * 0.005;
          result.totalFee += fee;
          result.details.push(`超过2000万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (0.5%)`);
        }
      }
    }
    
    // 向上取整到整数
    result.totalFee = Math.ceil(result.totalFee);
    
    return result;
  }

  /**
   * 计算离婚案件受理费
   * @param hasProperty 是否涉及财产分割
   * @param propertyAmount 财产总额（元），如果有财产分割
   * @returns 计算结果
   */
  calculateDivorceFee(hasProperty: boolean, propertyAmount: number = 0): CourtFeeResult {
    const result: CourtFeeResult = {
      totalFee: 150, // 离婚案件每件交纳150元
      details: ['离婚案件基本费用: 150元']
    };

    if (hasProperty && propertyAmount > 200000) {
      // 涉及财产分割，财产总额超过20万元的部分，按照0.5%交纳
      const excessAmount = propertyAmount - 200000;
      const additionalFee = excessAmount * 0.005;
      result.totalFee += additionalFee;
      result.details.push(`财产分割超过20万元的部分(${excessAmount.toLocaleString()}元): ${additionalFee.toLocaleString()}元 (0.5%)`);
    } else if (hasProperty) {
      result.details.push('财产总额不超过20万元，不另行交纳');
    }

    // 向上取整到整数
    result.totalFee = Math.ceil(result.totalFee);
    
    return result;
  }

  /**
   * 计算人格权案件受理费
   * @param hasDamages 是否涉及损害赔偿
   * @param damagesAmount 赔偿金额（元），如果有损害赔偿
   * @returns 计算结果
   */
  calculatePersonalRightsFee(hasDamages: boolean, damagesAmount: number = 0): CourtFeeResult {
    const result: CourtFeeResult = {
      totalFee: 300, // 人格权案件每件交纳300元
      details: ['人格权案件基本费用: 300元']
    };

    if (hasDamages) {
      if (damagesAmount <= 50000) {
        result.details.push('赔偿金额不超过5万元，不另行交纳');
      } else if (damagesAmount <= 100000) {
        // 超过5万元至10万元的部分，按照1%交纳
        const excessAmount = damagesAmount - 50000;
        const additionalFee = excessAmount * 0.01;
        result.totalFee += additionalFee;
        result.details.push(`赔偿金额超过5万元至10万元的部分(${excessAmount.toLocaleString()}元): ${additionalFee.toLocaleString()}元 (1%)`);
      } else {
        // 超过5万元至10万元的部分，按照1%交纳
        const firstRange = 50000;
        const firstRangeFee = firstRange * 0.01;
        result.totalFee += firstRangeFee;
        result.details.push(`赔偿金额超过5万元至10万元的部分(50,000元): ${firstRangeFee.toLocaleString()}元 (1%)`);
        
        // 超过10万元的部分，按照0.5%交纳
        const secondRange = damagesAmount - 100000;
        if (secondRange > 0) {
          const secondRangeFee = secondRange * 0.005;
          result.totalFee += secondRangeFee;
          result.details.push(`赔偿金额超过10万元的部分(${secondRange.toLocaleString()}元): ${secondRangeFee.toLocaleString()}元 (0.5%)`);
        }
      }
    }

    // 向上取整到整数
    result.totalFee = Math.ceil(result.totalFee);
    
    return result;
  }

  /**
   * 计算非财产民事案件受理费
   * @returns 计算结果
   */
  calculateNonPropertyFee(): CourtFeeResult {
    return {
      totalFee: 70,
      details: ['非财产案件每件交纳70元']
    };
  }

  /**
   * 计算知识产权案件受理费
   * @param hasDispute 是否有争议金额或价额
   * @param disputeAmount 争议金额（元），如果有争议金额
   * @returns 计算结果
   */
  calculateIntellectualPropertyFee(hasDispute: boolean, disputeAmount: number = 0): CourtFeeResult {
    if (!hasDispute) {
      return {
        totalFee: 750,
        details: ['没有争议金额或价额的知识产权民事案件，每件交纳750元']
      };
    } else {
      // 有争议金额或价额的，按照财产案件的标准交纳
      const result = this.calculatePropertyCaseFee(disputeAmount);
      result.details.unshift('有争议金额或价额的知识产权民事案件，按照财产案件的标准交纳');
      return result;
    }
  }

  /**
   * 计算劳动争议、人事争议案件受理费
   * @returns 计算结果
   */
  calculateLaborDisputeFee(): CourtFeeResult {
    return {
      totalFee: 10,
      details: ['劳动争议、人事争议案件每件交纳10元']
    };
  }

  /**
   * 计算申请支付令费用
   * @param amount 争议金额（元）
   * @returns 计算结果
   */
  calculatePaymentOrderFee(amount: number): CourtFeeResult {
    // 依法申请支付令的，比照财产案件受理费标准的1/3交纳
    const propertyFee = this.calculatePropertyCaseFee(amount);
    const result: CourtFeeResult = {
      totalFee: Math.ceil(propertyFee.totalFee / 3),
      details: ['申请支付令，比照财产案件受理费标准的1/3交纳']
    };
    
    for (const detail of propertyFee.details) {
      result.details.push(detail);
    }
    
    result.details.push(`最终费用(1/3比例): ${result.totalFee.toLocaleString()}元`);
    
    return result;
  }

  /**
   * 计算申请保全措施费用
   * @param amount 保全财产数额（元）
   * @returns 计算结果
   */
  calculatePreservationFee(amount: number): CourtFeeResult {
    const result: CourtFeeResult = {
      totalFee: 0,
      details: []
    };

    if (amount <= 1000) {
      result.totalFee = 30;
      result.details.push('财产数额不超过1000元或者不涉及财产数额的，每件交纳30元');
    } else {
      // 基础费用
      result.totalFee = 30;
      result.details.push('基础费用: 30元');
      
      // 超过1000元至10万元的部分，按照1%交纳
      if (amount > 1000) {
        const range = Math.min(amount, 100000) - 1000;
        if (range > 0) {
          const fee = range * 0.01;
          result.totalFee += fee;
          result.details.push(`超过1000元至10万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (1%)`);
        }
      }
      
      // 超过10万元的部分，按照0.5％交纳
      if (amount > 100000) {
        const range = amount - 100000;
        if (range > 0) {
          const fee = range * 0.005;
          result.totalFee += fee;
          result.details.push(`超过10万元的部分(${range.toLocaleString()}元): ${fee.toLocaleString()}元 (0.5%)`);
        }
      }
    }
    
    // 最多不超过5000元
    if (result.totalFee > 5000) {
      result.totalFee = 5000;
      result.details.push('申请保全措施交纳的费用最多不超过5000元');
    }
    
    // 向上取整到整数
    result.totalFee = Math.ceil(result.totalFee);
    
    return result;
  }

  /**
   * 计算申请公示催告费用
   * @returns 计算结果
   */
  calculatePublicNotificationFee(): CourtFeeResult {
    return {
      totalFee: 100,
      details: ['依法申请公示催告的，每件交纳100元']
    };
  }

  /**
   * 计算申请撤销仲裁裁决或认定仲裁协议效力费用
   * @returns 计算结果
   */
  calculateArbitrationRevocationFee(): CourtFeeResult {
    return {
      totalFee: 400,
      details: ['申请撤销仲裁裁决或者认定仲裁协议效力的，每件交纳400元']
    };
  }

  /**
   * 计算申请破产费用
   * @param bankruptcyAmount 破产财产总额（元）
   * @returns 计算结果
   */
  calculateBankruptcyFee(bankruptcyAmount: number): CourtFeeResult {
    // 破产案件依据破产财产总额计算，按照财产案件受理费标准减半交纳，但是，最高不超过30万元
    const propertyFee = this.calculatePropertyCaseFee(bankruptcyAmount);
    let fee = Math.ceil(propertyFee.totalFee / 2);
    
    const result: CourtFeeResult = {
      totalFee: fee > 300000 ? 300000 : fee,
      details: ['破产案件依据破产财产总额计算，按照财产案件受理费标准减半交纳']
    };
    
    for (const detail of propertyFee.details) {
      result.details.push(detail);
    }
    
    result.details.push(`减半后费用: ${fee.toLocaleString()}元`);
    
    if (fee > 300000) {
      result.details.push('破产案件申请费最高不超过30万元，因此最终收费为300,000元');
    }
    
    return result;
  }

  /**
   * 计算行政案件诉讼费
   * @returns 计算结果
   */
  calculateAdministrativeFee(): CourtFeeResult {
    return {
      totalFee: 50,
      details: ['行政案件诉讼费每件交纳50元']
    };
  }
} 