import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { PricingService, HourlyPricingResult } from '../../../services/pricing.service';

@Component({
  selector: 'app-hourly-pricing',
  templateUrl: './hourly-pricing.component.html',
  styleUrls: ['./hourly-pricing.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    FormsModule,
    ReactiveFormsModule,
    MatDividerModule,
    MatListModule
  ]
})
export class HourlyPricingComponent {
  hours: number = 1;
  result: HourlyPricingResult | null = null;
  
  constructor(private pricingService: PricingService) {
    this.calculateFee();
  }
  
  calculateFee(): void {
    this.result = this.pricingService.calculateHourlyFee();
    
    // 计算总价
    if (this.result && this.hours > 0) {
      this.result.hourlyRate.min *= this.hours;
      this.result.hourlyRate.max *= this.hours;
      this.result.details.push(`按${this.hours}小时计算，总价为：${this.result.hourlyRate.min.toLocaleString()}元至${this.result.hourlyRate.max.toLocaleString()}元`);
    }
  }
  
  updateHours(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.hours = value > 0 ? value : 1;
    this.calculateFee();
  }
} 