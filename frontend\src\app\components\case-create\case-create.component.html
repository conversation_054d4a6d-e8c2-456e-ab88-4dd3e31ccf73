<div class="container">
  <mat-card class="main-card">
    <mat-card-header>
      <mat-card-title>创建新案件</mat-card-title>
    </mat-card-header>

    <form [formGroup]="caseForm" (ngSubmit)="onSubmit()">
      <mat-card-content>
        <div class="form-section">
          <h3>基本信息</h3>
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>案件类型</mat-label>
            <mat-select formControlName="case_type" required (selectionChange)="onCaseTypeChanged()">
              <mat-option value="民事案件">民事案件</mat-option>
              <mat-option value="刑事案件">刑事案件</mat-option>
              <mat-option value="行政案件">行政案件</mat-option>
              <mat-option value="其他案件">其他案件</mat-option>
            </mat-select>
            <mat-error *ngIf="caseForm.get('case_type')?.hasError('required')">
              请选择案件类型
            </mat-error>
          </mat-form-field>

          <div class="checkbox-field">
            <mat-checkbox formControlName="is_sensitive">
              重大敏感案件
            </mat-checkbox>
            <span class="helper-text">标记为重大敏感案件后，主任审批后将无法修改此状态</span>
          </div>

          <div class="checkbox-field">
            <mat-checkbox formControlName="is_risk_agency">
              是否风险代理
            </mat-checkbox>
            <span class="helper-text">标记为风险代理案件后，主任审批后将无法修改此状态</span>
          </div>
        </div>

        <div class="form-section">
          <h3>当事人信息</h3>

          <!-- 当事人列表 -->
          <div class="parties-list" *ngIf="parties.length > 0">
            <div class="party-card" *ngFor="let party of parties; let i = index">
              <div class="party-header">
                <span class="party-name">{{ party.name }}</span>
                <span class="party-type-badge"
                     [ngClass]="party.entityType === 'natural' ? 'natural' : 'legal'">
                  {{ party.entityType === 'natural' ? '自然人' : '单位' }}
                </span>
                <span *ngIf="party.is_client" class="client-badge">委托人</span>
                <button mat-icon-button color="warn" (click)="removeParty(i)"
                        aria-label="移除当事人" class="delete-btn">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
              <div class="party-info">
                <p>{{ party.info }}</p>
                <p><strong>角色：</strong>{{ getPartyTypeDisplayForCase(party.party_type) }}</p>
                <p *ngIf="party.internal_number"><strong>内部编号：</strong>{{ party.internal_number }}</p>
                <p *ngIf="party.remarks"><strong>备注：</strong>{{ party.remarks }}</p>
              </div>
            </div>
          </div>

          <!-- 当事人数量提示和验证 -->
          <div class="party-validation" *ngIf="parties.length === 0">
            <mat-error>请至少添加一个当事人</mat-error>
          </div>

          <!-- 添加当事人按钮 -->
          <button mat-raised-button color="primary" type="button" (click)="openAddPartyDialog()"
                  class="add-party-button">
            <mat-icon>person_add</mat-icon> 添加当事人
          </button>
        </div>

        <div class="form-section">
          <h3>案件信息</h3>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>案由</mat-label>
            <input matInput formControlName="case_cause" placeholder="请输入案由">
            <mat-error *ngIf="caseForm.get('case_cause')?.hasError('required')">
              案由为必填项
            </mat-error>
            <mat-error *ngIf="caseForm.get('case_cause')?.hasError('maxlength')">
              案由不能超过200个字符
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>受理机关</mat-label>
            <input matInput formControlName="accepting_authority" placeholder="请输入受理机关">
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>委托合同编号</mat-label>
            <input matInput formControlName="contract_number" placeholder="请输入委托合同编号">
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>法院案件编号</mat-label>
            <input matInput formControlName="court_case_number" placeholder="请输入法院案件编号">
          </mat-form-field>

          <div class="row">
            <mat-form-field appearance="outline">
              <mat-label>诉讼请求</mat-label>
              <input matInput formControlName="litigation_request" placeholder="请输入诉讼请求">
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>诉讼标的额</mat-label>
              <input matInput formControlName="litigation_amount" placeholder="请输入诉讼标的额">
            </mat-form-field>
          </div>

          <div class="row">
            <mat-form-field appearance="outline">
              <mat-label>商定律师费</mat-label>
              <input matInput formControlName="agreed_attorney_fee" placeholder="请输入商定律师费">
            </mat-form-field>

            <mat-form-field appearance="outline">
              <mat-label>代理阶段</mat-label>
              <input matInput formControlName="agency_stage" placeholder="请输入代理阶段">
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>案情摘要</mat-label>
            <textarea matInput formControlName="case_summary" placeholder="请输入案情摘要" rows="4"></textarea>
            <mat-error *ngIf="caseForm.get('case_summary')?.hasError('required')">
              案情摘要为必填项
            </mat-error>
          </mat-form-field>

          <!-- 自定义属性部分 -->
          <div class="custom-properties-section">
            <h3>自定义属性</h3>

            <!-- 已添加的自定义属性列表 -->
            <div class="custom-properties-list" *ngIf="customProperties.length > 0">
              <div class="custom-property-item" *ngFor="let prop of customProperties; let i = index">
                <span class="property-name">{{prop.name}}</span>
                <span class="property-value">{{prop.value}}</span>
                <button mat-icon-button color="warn" (click)="removeCustomProperty(i)">
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </div>

            <!-- 添加新的自定义属性 -->
            <div class="add-property-form">
              <mat-form-field appearance="outline">
                <mat-label>属性名</mat-label>
                <input matInput formControlName="custom_property_name" placeholder="请输入属性名">
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>属性值</mat-label>
                <input matInput formControlName="custom_property_value" placeholder="请输入属性值">
              </mat-form-field>

              <button mat-raised-button color="primary" type="button" (click)="addCustomProperty()">
                <mat-icon>add</mat-icon> 添加属性
              </button>
            </div>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>备注</mat-label>
            <textarea matInput formControlName="comments" placeholder="请输入备注" rows="4"></textarea>
          </mat-form-field>
        </div>
      </mat-card-content>

      <mat-card-actions align="end">
        <button mat-button type="button" (click)="onCancel()">
          <mat-icon>close</mat-icon> 取消
        </button>
        <button mat-raised-button type="submit" color="primary" [disabled]="isSubmitting || parties.length === 0 || caseForm.invalid">
          <mat-icon>save</mat-icon> 保存
        </button>
      </mat-card-actions>
    </form>
  </mat-card>
</div>
