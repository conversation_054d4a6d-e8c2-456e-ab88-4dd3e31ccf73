.container {
  padding: 0 1rem;
  max-width: 1200px;
  margin: 2rem auto;
}

h2 {
  margin-bottom: 20px;
  color: #333;
}

.tab-content {
  margin-top: 20px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.form-row > mat-form-field {
  flex: 1;
  min-width: 200px;
}

.form-row.full-width {
  flex-direction: column;
}

.full-width {
  width: 100%;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.data-table-container {
  margin-top: 30px;
  position: relative;
  min-height: 200px;
}

table {
  width: 100%;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #888;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
}

.has-cases {
  font-weight: 500;
  color: #2196F3;
}

.highlighted-row {
  background-color: rgba(33, 150, 243, 0.1);
}

/* 操作按钮区域样式 */
.action-buttons {
  margin-bottom: 20px;
  display: flex;
  gap: 16px;
}

.action-buttons button {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Dialog全局样式 */
::ng-deep .custom-dialog-container {
  .mat-mdc-dialog-container {
    background: transparent;
    box-shadow: 0 24px 38px 3px rgba(0, 0, 0, 0.14),
                0 9px 46px 8px rgba(0, 0, 0, 0.12),
                0 11px 15px -7px rgba(0, 0, 0, 0.20);
    border-radius: 12px;
    overflow: hidden;
  }
  
  .mat-mdc-dialog-surface {
    border-radius: 12px;
    overflow: hidden;
  }
}

/* 搜索区域样式 */
.search-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  width: 100%;
  max-width: 1200px;
  position: relative;
  z-index: 10;
  margin: 2rem auto 1rem;
}

.search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.search-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.search-field {
  width: 100%;
  margin-bottom: 15px;
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
  border-radius: 4px;
}

.results-section {
  margin-bottom: 15px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header h4 {
  margin: 0;
  font-size: 1rem;
  color: #555;
}

.section-hint {
  font-size: 0.8rem;
  color: #888;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.result-card {
  padding: 12px;
  border-radius: 4px;
  background-color: #f9f9f9;
  cursor: pointer;
  transition: background-color 0.2s;
}

.result-card:hover {
  background-color: #f0f0f0;
}

.legacy-card {
  background-color: #f5f5f5;
  cursor: default;
}

.legacy-card:hover {
  background-color: #f0f0f0;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.result-name {
  font-weight: 500;
}

.result-badge {
  font-size: 0.7rem;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
}

.result-badge.natural {
  background-color: #2196F3;
}

.result-badge.legal {
  background-color: #4CAF50;
}

.result-badge.legacy {
  background-color: #9E9E9E;
}

.result-info {
  font-size: 0.85rem;
  color: #666;
}

.no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 0;
  color: #888;
}

.no-results mat-icon {
  font-size: 40px;
  height: 40px;
  width: 40px;
  margin-bottom: 10px;
}

.search-toggle {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 100;
}

/* 利益冲突检索特定样式 */
.search-description {
  margin: 10px 0;
  font-size: 0.9rem;
  color: #666;
}

.result-count {
  font-size: 0.8rem;
  color: #888;
  background-color: #e0e0e0;
  padding: 2px 8px;
  border-radius: 12px;
}

.natural-person-card {
  border-left: 4px solid #2196F3;
}

.legal-entity-card {
  border-left: 4px solid #4CAF50;
}

.legacy-case-card {
  border-left: 4px solid #FF9800;
}

.case-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 8px;
  font-size: 0.8rem;
  color: #777;
}

.case-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 创建者信息样式 */
.creator-info {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid #e0e0e0;
  font-size: 0.8rem;
  color: #777;
}

.creator-info div {
  margin-bottom: 4px;
}

.creator-info div:last-child {
  margin-bottom: 0;
}

@media (max-width: 768px) {
  .search-container {
    padding: 15px;
  }
  
  .search-results {
    max-height: 300px;
  }
  
  .case-meta {
    flex-direction: column;
    gap: 4px;
  }
} 