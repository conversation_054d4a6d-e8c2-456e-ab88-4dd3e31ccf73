<div class="create-folder-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon>create_new_folder</mat-icon>
      创建新文件夹
    </h2>
  </div>
  
  <mat-dialog-content>
    <form [formGroup]="folderForm">
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>文件夹名称</mat-label>
        <input matInput formControlName="folderName" placeholder="输入文件夹名称" autofocus>
        <mat-icon matPrefix>folder</mat-icon>
        <mat-error *ngIf="folderForm.get('folderName')?.hasError('required')">
          文件夹名称不能为空
        </mat-error>
        <mat-error *ngIf="folderForm.get('folderName')?.hasError('maxlength')">
          文件夹名称不能超过100个字符
        </mat-error>
      </mat-form-field>
    </form>
  </mat-dialog-content>
  
  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()">取消</button>
    <button 
      mat-flat-button 
      color="primary" 
      [disabled]="folderForm.invalid" 
      (click)="onSubmit()">
      创建
    </button>
  </mat-dialog-actions>
</div> 