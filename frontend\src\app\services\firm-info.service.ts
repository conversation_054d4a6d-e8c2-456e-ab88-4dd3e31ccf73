import { Injectable } from '@angular/core';
import { Title } from '@angular/platform-browser';

@Injectable({
  providedIn: 'root'
})
export class FirmInfoService {
  private _firmName: string;

  constructor(private titleService: Title) {
    // 根据域名设置律师事务所名称
    const hostname = window.location.hostname;
    this._firmName = hostname.startsWith('firm2') ? '广东承诺律师事务所雷州分所' : '广东承诺律师事务所';
    
    // 设置浏览器标题
    this.titleService.setTitle(this._firmName);
  }

  get firmName(): string {
    return this._firmName;
  }
} 