import { Injectable } from '@angular/core';

export interface PricingRange {
  min: number;
  max: number;
}

export interface CivilCasePricingResult {
  fixedFee?: PricingRange;
  percentageFee?: PricingRange;
  totalFee: PricingRange;
  details: string[];
}

export interface CriminalCasePricingResult {
  fee: PricingRange;
  details: string[];
}

export interface NonLitigationPricingResult {
  fee: PricingRange;
  details: string[];
}

export interface RiskPricingResult {
  maxPercentage: number;
  maxFee: number;
  details: string[];
}

export interface HourlyPricingResult {
  hourlyRate: PricingRange;
  details: string[];
}

@Injectable({
  providedIn: 'root'
})
export class PricingService {

  constructor() { }

  // 计算民事诉讼案件收费
  calculateCivilCaseFee(amount: number, isPropertyCase: boolean): CivilCasePricingResult {
    const result: CivilCasePricingResult = {
      totalFee: { min: 0, max: 0 },
      details: []
    };

    if (!isPropertyCase) {
      // 不涉及财产关系的案件
      result.fixedFee = { min: 2000, max: 50000 };
      result.totalFee = result.fixedFee;
      result.details.push('不涉及财产关系的案件，每件收费2000元至50000元');
    } else {
      // 涉及财产关系的案件
      const baseFee: PricingRange = { min: 3000, max: 10000 };
      result.fixedFee = { min: 2000, max: 50000 };
      result.details.push('基础服务费：3000元至10000元');
      result.details.push('可选固定收费：2000元至50000元');
      
      // 按比例计算
      let percentageFeeMin = 0;
      let percentageFeeMax = 0;
      
      // 10万元以下部分
      if (amount <= 100000) {
        percentageFeeMin += amount * 0.00;
        percentageFeeMax += amount * 0.08;
        result.details.push(`10万元以下部分(${amount.toLocaleString()}元)，收取0%-8%：${0}元-${(amount * 0.08).toLocaleString()}元`);
      } else {
        percentageFeeMin += 100000 * 0.00;
        percentageFeeMax += 100000 * 0.08;
        result.details.push(`10万元以下部分(100,000元)，收取0%-8%：${0}元-${(100000 * 0.08).toLocaleString()}元`);
      }
      
      // 10万元至50万元部分
      if (amount > 100000) {
        const range = Math.min(amount, 500000) - 100000;
        if (range > 0) {
          percentageFeeMin += range * 0.04;
          percentageFeeMax += range * 0.05;
          result.details.push(`10万元至50万元部分(${range.toLocaleString()}元)，收取4%-5%：${(range * 0.04).toLocaleString()}元-${(range * 0.05).toLocaleString()}元`);
        }
      }
      
      // 50万元至100万元部分
      if (amount > 500000) {
        const range = Math.min(amount, 1000000) - 500000;
        if (range > 0) {
          percentageFeeMin += range * 0.03;
          percentageFeeMax += range * 0.04;
          result.details.push(`50万元至100万元部分(${range.toLocaleString()}元)，收取3%-4%：${(range * 0.03).toLocaleString()}元-${(range * 0.04).toLocaleString()}元`);
        }
      }
      
      // 100万元至500万元部分
      if (amount > 1000000) {
        const range = Math.min(amount, 5000000) - 1000000;
        if (range > 0) {
          percentageFeeMin += range * 0.02;
          percentageFeeMax += range * 0.03;
          result.details.push(`100万元至500万元部分(${range.toLocaleString()}元)，收取2%-3%：${(range * 0.02).toLocaleString()}元-${(range * 0.03).toLocaleString()}元`);
        }
      }
      
      // 500万元至1000万元部分
      if (amount > 5000000) {
        const range = Math.min(amount, 10000000) - 5000000;
        if (range > 0) {
          percentageFeeMin += range * 0.015;
          percentageFeeMax += range * 0.02;
          result.details.push(`500万元至1000万元部分(${range.toLocaleString()}元)，收取1.5%-2%：${(range * 0.015).toLocaleString()}元-${(range * 0.02).toLocaleString()}元`);
        }
      }
      
      // 1000万元至2000万元部分
      if (amount > 10000000) {
        const range = Math.min(amount, 20000000) - 10000000;
        if (range > 0) {
          percentageFeeMin += range * 0.01;
          percentageFeeMax += range * 0.015;
          result.details.push(`1000万元至2000万元部分(${range.toLocaleString()}元)，收取1%-1.5%：${(range * 0.01).toLocaleString()}元-${(range * 0.015).toLocaleString()}元`);
        }
      }
      
      // 2000万元至5000万元部分
      if (amount > 20000000) {
        const range = Math.min(amount, 50000000) - 20000000;
        if (range > 0) {
          percentageFeeMin += range * 0.005;
          percentageFeeMax += range * 0.01;
          result.details.push(`2000万元至5000万元部分(${range.toLocaleString()}元)，收取0.5%-1%：${(range * 0.005).toLocaleString()}元-${(range * 0.01).toLocaleString()}元`);
        }
      }
      
      // 5000万元以上部分
      if (amount > 50000000) {
        const range = amount - 50000000;
        percentageFeeMin += range * 0.003;
        percentageFeeMax += range * 0.005;
        result.details.push(`5000万元以上部分(${range.toLocaleString()}元)，收取0.3%-0.5%：${(range * 0.003).toLocaleString()}元-${(range * 0.005).toLocaleString()}元`);
      }
      
      result.percentageFee = {
        min: percentageFeeMin,
        max: percentageFeeMax
      };

      // 总费用 = 基础费用 + 按比例收费
      result.totalFee = {
        min: baseFee.min + percentageFeeMin,
        max: baseFee.max + percentageFeeMax
      };
      
      result.details.push(`总费用(基础费用+按比例收费)：${result.totalFee.min.toLocaleString()}元-${result.totalFee.max.toLocaleString()}元`);
    }
    
    return result;
  }

  // 计算刑事诉讼案件收费
  calculateCriminalCaseFee(stage: string): CriminalCasePricingResult {
    const result: CriminalCasePricingResult = {
      fee: { min: 0, max: 0 },
      details: []
    };
    
    switch (stage) {
      case 'investigation':
        result.fee = { min: 6000, max: 200000 };
        result.details.push('刑事侦查阶段，收费标准为6000元至200000元/件');
        break;
      case 'prosecution':
        result.fee = { min: 6000, max: 300000 };
        result.details.push('审查起诉阶段，收费标准为6000元至300000元/件');
        break;
      case 'firstTrial':
        result.fee = { min: 10000, max: 500000 };
        result.details.push('一审阶段，收费标准为10000元至500000元/件');
        break;
      case 'deathPenalty':
        result.fee = { min: 20000, max: 500000 };
        result.details.push('死刑复核阶段，收费标准为20000元至500000元/件');
        break;
      case 'other':
        result.fee = { min: 2000, max: 50000 };
        result.details.push('其他刑事诉讼活动(减刑、假释、保外就医等)，收费标准为2000元至50000元/件');
        break;
      case 'secondTrial':
        result.fee = { min: 10000, max: 500000 };
        result.details.push('二审阶段，参照一审收费标准，为10000元至500000元/件');
        break;
      default:
        result.fee = { min: 6000, max: 300000 };
        result.details.push('未指定阶段，按照平均水平估算为6000元至300000元/件');
    }
    
    return result;
  }

  // 计算行政诉讼案件收费
  calculateAdministrativeCaseFee(amount: number, involveProperty: boolean): CivilCasePricingResult {
    // 行政诉讼案件收费与民事诉讼案件类似
    return this.calculateCivilCaseFee(amount, involveProperty);
  }

  // 计算非诉讼案件收费
  calculateNonLitigationFee(amount: number, serviceType: string): NonLitigationPricingResult {
    const result: NonLitigationPricingResult = {
      fee: { min: 0, max: 0 },
      details: []
    };
    
    // 根据服务类型计算费用
    if (serviceType === 'financial' && amount > 0) {
      // 涉及财产关系的非诉讼服务
      let feeMin = 0;
      let feeMax = 0;
      
      // 100万元以下部分
      if (amount <= 1000000) {
        feeMin += amount * 0.03;
        feeMax += amount * 0.04;
        result.details.push(`100万元以下部分(${amount.toLocaleString()}元)，收取3%-4%：${(amount * 0.03).toLocaleString()}元-${(amount * 0.04).toLocaleString()}元`);
      } else {
        feeMin += 1000000 * 0.03;
        feeMax += 1000000 * 0.04;
        result.details.push(`100万元以下部分(1,000,000元)，收取3%-4%：${(1000000 * 0.03).toLocaleString()}元-${(1000000 * 0.04).toLocaleString()}元`);
      }
      
      // 100万元至1000万元部分
      if (amount > 1000000) {
        const range = Math.min(amount, 10000000) - 1000000;
        if (range > 0) {
          feeMin += range * 0.02;
          feeMax += range * 0.03;
          result.details.push(`100万元至1000万元部分(${range.toLocaleString()}元)，收取2%-3%：${(range * 0.02).toLocaleString()}元-${(range * 0.03).toLocaleString()}元`);
        }
      }
      
      // 1000万元至5000万元部分
      if (amount > 10000000) {
        const range = Math.min(amount, 50000000) - 10000000;
        if (range > 0) {
          feeMin += range * 0.01;
          feeMax += range * 0.02;
          result.details.push(`1000万元至5000万元部分(${range.toLocaleString()}元)，收取1%-2%：${(range * 0.01).toLocaleString()}元-${(range * 0.02).toLocaleString()}元`);
        }
      }
      
      // 5000万元至1亿元部分
      if (amount > 50000000) {
        const range = Math.min(amount, 100000000) - 50000000;
        if (range > 0) {
          feeMin += range * 0.005;
          feeMax += range * 0.01;
          result.details.push(`5000万元至1亿元部分(${range.toLocaleString()}元)，收取0.5%-1%：${(range * 0.005).toLocaleString()}元-${(range * 0.01).toLocaleString()}元`);
        }
      }
      
      // 1亿元以上部分
      if (amount > 100000000) {
        const range = amount - 100000000;
        feeMin += range * 0.003;
        feeMax += range * 0.005;
        result.details.push(`1亿元以上部分(${range.toLocaleString()}元)，收取0.3%-0.5%：${(range * 0.003).toLocaleString()}元-${(range * 0.005).toLocaleString()}元`);
      }
      
      result.fee = { min: feeMin, max: feeMax };
    } else {
      // 不涉及财产关系的非诉讼服务，根据服务类型计算
      switch (serviceType) {
        case 'yearlyConsult':
          result.fee = { min: 10000, max: 1000000 };
          result.details.push('担任常年法律顾问，收费标准为10000元至1000000元/年');
          break;
        case 'specialConsult':
          result.fee = { min: 5000, max: 500000 };
          result.details.push('担任专项法律顾问，收费标准为5000元至500000元/件');
          break;
        case 'legalConsult':
          result.fee = { min: 200, max: 50000 };
          result.details.push('法律咨询服务，收费标准为200元至50000元/件');
          break;
        case 'legalAnalysis':
          result.fee = { min: 5000, max: 200000 };
          result.details.push('法律论证服务，收费标准为5000元至200000元/件');
          break;
        case 'legalTraining':
          result.fee = { min: 2000, max: 50000 };
          result.details.push('法律培训服务，收费标准为2000元至50000元/件');
          break;
        case 'legalWitness':
          result.fee = { min: 2000, max: 100000 };
          result.details.push('律师见证服务，收费标准为2000元至100000元/件');
          break;
        case 'legalOpinion':
          result.fee = { min: 500, max: 500000 };
          result.details.push('出具律师函、法律意见书，收费标准为500元至500000元/件');
          break;
        case 'legalDocument':
          result.fee = { min: 1000, max: 100000 };
          result.details.push('起草、审查、修改合同或文件，收费标准为1000元至100000元/件');
          break;
        case 'legalProceeding':
          result.fee = { min: 500, max: 20000 };
          result.details.push('代书、代办公证等事项，收费标准为500元至20000元/件');
          break;
        case 'legalNegotiation':
          result.fee = { min: 3000, max: 50000 };
          result.details.push('参与谈判、调解等事务，收费标准为3000元至50000元/件');
          break;
        default:
          result.fee = { min: 3000, max: 50000 };
          result.details.push('其他非诉讼法律服务，收费标准为3000元至50000元/件');
      }
    }
    
    return result;
  }

  // 计算风险代理收费
  calculateRiskPricingFee(amount: number): RiskPricingResult {
    const result: RiskPricingResult = {
      maxPercentage: 0,
      maxFee: 0,
      details: []
    };
    
    let totalMaxPercentage = 0;
    let totalMaxFee = 0;
    
    // 标的额不足100万元的部分
    if (amount <= 1000000) {
      totalMaxPercentage = 18;
      totalMaxFee = amount * 0.18;
      result.details.push(`标的额不足100万元的部分(${amount.toLocaleString()}元)，最高不得超过18%：${totalMaxFee.toLocaleString()}元`);
    } else {
      totalMaxFee += 1000000 * 0.18;
      result.details.push(`标的额不足100万元的部分(1,000,000元)，最高不得超过18%：${(1000000 * 0.18).toLocaleString()}元`);
    }
    
    // 标的额100万元以上不足500万元的部分
    if (amount > 1000000) {
      const range = Math.min(amount, 5000000) - 1000000;
      if (range > 0) {
        totalMaxFee += range * 0.15;
        result.details.push(`标的额100万元以上不足500万元的部分(${range.toLocaleString()}元)，最高不得超过15%：${(range * 0.15).toLocaleString()}元`);
      }
    }
    
    // 标的额500万元以上不足1000万元的部分
    if (amount > 5000000) {
      const range = Math.min(amount, 10000000) - 5000000;
      if (range > 0) {
        totalMaxFee += range * 0.12;
        result.details.push(`标的额500万元以上不足1000万元的部分(${range.toLocaleString()}元)，最高不得超过12%：${(range * 0.12).toLocaleString()}元`);
      }
    }
    
    // 标的额1000万元以上不足5000万元的部分
    if (amount > 10000000) {
      const range = Math.min(amount, 50000000) - 10000000;
      if (range > 0) {
        totalMaxFee += range * 0.09;
        result.details.push(`标的额1000万元以上不足5000万元的部分(${range.toLocaleString()}元)，最高不得超过9%：${(range * 0.09).toLocaleString()}元`);
      }
    }
    
    // 标的额5000万元以上的部分
    if (amount > 50000000) {
      const range = amount - 50000000;
      totalMaxFee += range * 0.06;
      result.details.push(`标的额5000万元以上的部分(${range.toLocaleString()}元)，最高不得超过6%：${(range * 0.06).toLocaleString()}元`);
    }
    
    // 计算总比例
    totalMaxPercentage = (totalMaxFee / amount) * 100;
    
    result.maxPercentage = parseFloat(totalMaxPercentage.toFixed(2));
    result.maxFee = totalMaxFee;
    result.details.push(`总计最高不得超过金额：${totalMaxFee.toLocaleString()}元，占标的额的${result.maxPercentage}%`);
    
    return result;
  }

  // 计算计时收费
  calculateHourlyFee(): HourlyPricingResult {
    const result: HourlyPricingResult = {
      hourlyRate: { min: 400, max: 6000 },
      details: []
    };
    
    result.details.push('本所计时收费的单位时间为30分钟');
    result.details.push('第一档收费标准：200元至3000元/30分钟');
    result.details.push('按每小时计算：400元至6000元/小时');
    
    return result;
  }
} 