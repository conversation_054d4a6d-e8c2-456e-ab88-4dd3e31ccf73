from django.contrib import admin
from django.core.exceptions import ValidationError
from .models import (
    Case, CaseApproval, CaseNumber,
    NaturalPerson, LegalEntity, CaseParty,
    LegacyCase, SealUsageRecord, LetterRecord
)
from django import forms

class CaseApprovalAdminForm(forms.ModelForm):
    status_when_approved = forms.ChoiceField(choices=Case.STATUS_CHOICES)
    
    class Meta:
        model = CaseApproval
        fields = '__all__'

class CaseApprovalInline(admin.TabularInline):
    model = CaseApproval
    form = CaseApprovalAdminForm
    extra = 0
    readonly_fields = ('created_at',)
    fields = ('approver', 'action', 'status_when_approved', 'comment', 'created_at')
    ordering = ('-created_at',)

# 添加当事人表单验证
class CasePartyAdminForm(forms.ModelForm):
    class Meta:
        model = CaseParty
        fields = '__all__'
    
    def clean(self):
        cleaned_data = super().clean()
        natural_person = cleaned_data.get('natural_person')
        legal_entity = cleaned_data.get('legal_entity')
        
        # 检查是否同时选择了自然人和法人
        if natural_person and legal_entity:
            raise ValidationError("请只选择一种当事人类型（自然人或法人单位）")
        
        # 检查是否没有选择任何当事人
        if not natural_person and not legal_entity:
            raise ValidationError("请至少选择一种当事人（自然人或法人单位）")
        
        return cleaned_data

# 添加当事人内联管理
class CasePartyInline(admin.TabularInline):
    model = CaseParty
    form = CasePartyAdminForm
    extra = 1
    autocomplete_fields = ['natural_person', 'legal_entity']
    fields = ('party_type', 'natural_person', 'legal_entity', 'internal_number', 'remarks')
    verbose_name = '案件当事人'
    verbose_name_plural = '案件当事人列表'

@admin.register(NaturalPerson)
class NaturalPersonAdmin(admin.ModelAdmin):
    list_display = ('name', 'id_number', 'phone_number', 'creator', 'created_at')
    search_fields = ('name', 'id_number', 'phone_number', 'creator__username')
    list_filter = ('creator', 'created_at',)
    readonly_fields = ('creator', 'created_at', 'updated_at')
    
    def save_model(self, request, obj, form, change):
        """在保存时设置创建人"""
        if not change:  # 只在创建时设置创建人
            obj.creator = request.user
        super().save_model(request, obj, form, change)

@admin.register(LegalEntity)
class LegalEntityAdmin(admin.ModelAdmin):
    list_display = ('name', 'representative_name', 'representative_id_number', 'creator', 'created_at')
    search_fields = ('name', 'representative_name', 'representative_id_number', 'creator__username')
    list_filter = ('creator', 'created_at',)
    readonly_fields = ('creator', 'created_at', 'updated_at')
    
    def save_model(self, request, obj, form, change):
        """在保存时设置创建人"""
        if not change:  # 只在创建时设置创建人
            obj.creator = request.user
        super().save_model(request, obj, form, change)

@admin.register(CaseParty)
class CasePartyAdmin(admin.ModelAdmin):
    form = CasePartyAdminForm
    list_display = ('case', 'get_party_name', 'party_type', 'internal_number', 'created_at')
    search_fields = ('case__case_cause', 'natural_person__name', 'legal_entity__name', 'internal_number')
    list_filter = ('party_type', 'created_at')
    autocomplete_fields = ['case', 'natural_person', 'legal_entity']
    readonly_fields = ['created_at']
    fieldsets = (
        ('基本信息', {
            'fields': ('case', 'party_type', 'natural_person', 'legal_entity', 'internal_number')
        }),
        ('其他信息', {
            'fields': ('remarks', 'created_at')
        }),
    )

    def get_party_name(self, obj):
        if obj.natural_person:
            return f"{obj.natural_person.name} (自然人)"
        elif obj.legal_entity:
            return f"{obj.legal_entity.name} (单位)"
        return "未知当事人"
    get_party_name.short_description = '当事人'

@admin.register(Case)
class CaseAdmin(admin.ModelAdmin):
    list_display = ('case_number', 'case_cause', 'lawyer', 'status', 'created_at')
    list_filter = ('status', 'lawyer', 'created_at')
    search_fields = ('case_number', 'case_cause', 'lawyer__username')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)
    inlines = [CasePartyInline, CaseApprovalInline]
    fieldsets = (
        ('基本信息', {
            'fields': ('case_number', 'case_cause', 'lawyer', 'status', 'is_paid', 'is_sensitive')
        }),
        ('案件内容', {
            'fields': ('content',),
            'classes': ('collapse',),
        }),
    )
    
    def save_formset(self, request, form, formset, change):
        """保存内联表单集时的自定义行为"""
        instances = formset.save(commit=False)
        
        # 处理新增和修改的实例
        for instance in instances:
            # 如果是CaseParty，执行特定逻辑
            if isinstance(instance, CaseParty):
                # 保存前设置特定字段或执行其他操作
                pass
            
            instance.save()
        
        # 处理删除的实例
        for obj in formset.deleted_objects:
            obj.delete()
        
        formset.save_m2m()

@admin.register(CaseApproval)
class CaseApprovalAdmin(admin.ModelAdmin):
    list_display = ('case', 'approver', 'action', 'created_at')
    list_filter = ('action', 'created_at')
    search_fields = ('case__case_cause', 'approver__username', 'comment')

@admin.register(CaseNumber)
class CaseNumberAdmin(admin.ModelAdmin):
    list_display = ('year', 'type_code', 'start_number', 'last_used_number')
    list_filter = ('year', 'type_code')
    search_fields = ('type_code',)
    ordering = ('-year', 'type_code')

@admin.register(LegacyCase)
class LegacyCaseAdmin(admin.ModelAdmin):
    list_display = ('contract_number', 'case_cause', 'client', 'lawyer', 'case_type', 'filing_date', 'closing_date', 'archive_date')
    list_filter = ('case_type', 'lawyer', 'approver', 'filing_date', 'closing_date', 'archive_date', 'is_conflict', 'is_sensitive')
    search_fields = ('contract_number', 'case_cause', 'client', 'opposing_party', 'criminal', 'court', 'lawyer__username', 'approver__username')
    date_hierarchy = 'filing_date'
    ordering = ('-filing_date',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('contract_number', 'case_cause', 'client', 'opposing_party', 'criminal', 'court', 'trial_level', 'lawyer', 'approver', 'case_type')
        }),
        ('费用信息', {
            'fields': ('fee_amount', 'is_fee_compliant', 'invoice_number')
        }),
        ('案件标记', {
            'fields': ('is_conflict', 'is_sensitive')
        }),
        ('日期信息', {
            'fields': ('filing_date', 'closing_date', 'archive_date')
        }),
        ('其他信息', {
            'fields': ('remarks', 'created_at', 'updated_at')
        }),
    )

@admin.register(SealUsageRecord)
class SealUsageRecordAdmin(admin.ModelAdmin):
    """公章使用登记表管理"""
    list_display = ('year', 'sequence_number', 'date', 'matter', 'document_name', 'seal_type', 'user_name', 'approver', 'operator_name')
    list_filter = ('year', 'seal_type', 'date', 'approver')
    search_fields = ('matter', 'document_name', 'recipient_unit', 'user_name', 'operator_name')
    date_hierarchy = 'date'
    ordering = ('-year', '-sequence_number')
    readonly_fields = ('year', 'sequence_number', 'created_at', 'updated_at', 'last_printed_at')
    autocomplete_fields = ('approver',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('year', 'sequence_number', 'date', 'matter')
        }),
        ('盖章信息', {
            'fields': ('document_name', 'quantity', 'seal_type', 'recipient_unit')
        }),
        ('人员信息', {
            'fields': ('user_name', 'approver', 'operator_name')
        }),
        ('其他信息', {
            'fields': ('remarks', 'created_at', 'updated_at', 'last_printed_at')
        }),
    )
    
    def get_readonly_fields(self, request, obj=None):
        """根据对象状态设置只读字段"""
        readonly_fields = list(self.readonly_fields)
        
        # 如果记录已存在且已有序号，不允许修改年份和日期
        if obj and obj.sequence_number:
            readonly_fields.extend(['date'])
        
        return readonly_fields 

@admin.register(LetterRecord)
class LetterRecordAdmin(admin.ModelAdmin):
    """函件登记表管理"""
    list_display = ('year', 'sequence_number', 'letter_type', 'date', 'client_name', 'case_number', 'recipient_unit', 'lawyer_name', 'approver')
    list_filter = ('year', 'letter_type', 'date', 'approver')
    search_fields = ('client_name', 'case_number', 'recipient_unit', 'lawyer_name')
    date_hierarchy = 'date'
    ordering = ('-year', '-sequence_number')
    readonly_fields = ('year', 'sequence_number', 'created_at', 'updated_at', 'last_printed_at')
    autocomplete_fields = ('approver',)
    
    fieldsets = (
        ('基本信息', {
            'fields': ('year', 'sequence_number', 'date', 'letter_type', 'case_number', 'client_name')
        }),
        ('函件信息', {
            'fields': ('quantity', 'recipient_unit', 'lawyer_name')
        }),
        ('审批信息', {
            'fields': ('approver',)
        }),
        ('其他信息', {
            'fields': ('remarks', 'created_at', 'updated_at', 'last_printed_at')
        }),
    )
    
    def get_readonly_fields(self, request, obj=None):
        """根据对象状态设置只读字段"""
        readonly_fields = list(self.readonly_fields)
        
        # 如果记录已存在且已有序号，不允许修改年份、日期和函件类型
        if obj and obj.sequence_number:
            readonly_fields.extend(['date', 'letter_type'])
        
        return readonly_fields 