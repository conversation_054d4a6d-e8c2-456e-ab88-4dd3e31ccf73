import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTabsModule } from '@angular/material/tabs';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatDividerModule } from '@angular/material/divider';
import { MatListModule } from '@angular/material/list';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PricingService, CivilCasePricingResult, CriminalCasePricingResult, HourlyPricingResult, NonLitigationPricingResult, RiskPricingResult } from '../../services/pricing.service';

interface CaseStage {
  value: string;
  viewValue: string;
}

interface ServiceType {
  value: string;
  viewValue: string;
}

@Component({
  selector: 'app-pricing',
  templateUrl: './pricing.component.html',
  styleUrls: ['./pricing.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,
    MatInputModule,
    MatFormFieldModule,
    MatSelectModule,
    MatRadioModule,
    MatDividerModule,
    MatListModule,
    FormsModule,
    ReactiveFormsModule
  ]
})
export class PricingComponent {
  // 计时收费相关
  hours: number = 1;
  hourlyResult: HourlyPricingResult | null = null;
  
  // 民事诉讼相关
  civilAmount: number = 100000;
  isPropertyCase: boolean = true;
  civilResult: CivilCasePricingResult | null = null;
  
  // 刑事诉讼相关
  criminalStage: string = 'firstTrial';
  criminalResult: CriminalCasePricingResult | null = null;
  stages: CaseStage[] = [
    { value: 'investigation', viewValue: '刑事侦查阶段' },
    { value: 'prosecution', viewValue: '审查起诉阶段' },
    { value: 'firstTrial', viewValue: '一审阶段' },
    { value: 'secondTrial', viewValue: '二审阶段' },
    { value: 'deathPenalty', viewValue: '死刑复核阶段' },
    { value: 'other', viewValue: '其他刑事诉讼活动(减刑、假释等)' },
  ];
  
  // 行政诉讼相关
  adminAmount: number = 100000;
  involveProperty: boolean = true;
  adminResult: CivilCasePricingResult | null = null;
  
  // 非诉讼服务相关
  nonLitigationAmount: number = 0;
  serviceType: string = 'yearlyConsult';
  isFinancialType: boolean = false;
  nonLitigationResult: NonLitigationPricingResult | null = null;
  serviceTypes: ServiceType[] = [
    { value: 'financial', viewValue: '涉及财产关系的案件' },
    { value: 'yearlyConsult', viewValue: '担任常年法律顾问' },
    { value: 'specialConsult', viewValue: '担任专项法律顾问' },
    { value: 'legalConsult', viewValue: '法律咨询服务' },
    { value: 'legalAnalysis', viewValue: '法律论证服务' },
    { value: 'legalTraining', viewValue: '法律培训服务' },
    { value: 'legalWitness', viewValue: '律师见证服务' },
    { value: 'legalOpinion', viewValue: '出具律师函、法律意见书等' },
    { value: 'legalDocument', viewValue: '起草、审查、修改合同或文件' },
    { value: 'legalProceeding', viewValue: '代书、代办公证等事项' },
    { value: 'legalNegotiation', viewValue: '参与谈判、调解等事务' },
  ];
  
  // 风险代理相关
  riskAmount: number = 500000;
  riskResult: RiskPricingResult | null = null;
  
  constructor(private pricingService: PricingService) {
    this.calculateAllFees();
  }
  
  calculateAllFees() {
    this.calculateHourlyFee();
    this.calculateCivilFee();
    this.calculateCriminalFee();
    this.calculateAdminFee();
    this.calculateNonLitigationFee();
    this.calculateRiskFee();
  }
  
  // 计时收费计算
  calculateHourlyFee(): void {
    this.hourlyResult = this.pricingService.calculateHourlyFee();
    
    if (this.hourlyResult && this.hours > 0) {
      this.hourlyResult.hourlyRate.min *= this.hours;
      this.hourlyResult.hourlyRate.max *= this.hours;
      this.hourlyResult.details.push(`按${this.hours}小时计算，总价为：${this.hourlyResult.hourlyRate.min.toLocaleString()}元至${this.hourlyResult.hourlyRate.max.toLocaleString()}元`);
    }
  }
  
  updateHours(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.hours = value > 0 ? value : 1;
    this.calculateHourlyFee();
  }
  
  // 民事诉讼收费计算
  calculateCivilFee(): void {
    this.civilResult = this.pricingService.calculateCivilCaseFee(this.civilAmount, this.isPropertyCase);
  }
  
  updateCivilAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.civilAmount = value > 0 ? value : 0;
    this.calculateCivilFee();
  }
  
  updateCivilCaseType(isPropertyCase: boolean): void {
    this.isPropertyCase = isPropertyCase;
    this.calculateCivilFee();
  }
  
  // 刑事诉讼收费计算
  calculateCriminalFee(): void {
    this.criminalResult = this.pricingService.calculateCriminalCaseFee(this.criminalStage);
  }
  
  updateCriminalStage(stage: string): void {
    this.criminalStage = stage;
    this.calculateCriminalFee();
  }
  
  // 行政诉讼收费计算
  calculateAdminFee(): void {
    this.adminResult = this.pricingService.calculateAdministrativeCaseFee(this.adminAmount, this.involveProperty);
  }
  
  updateAdminAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.adminAmount = value > 0 ? value : 0;
    this.calculateAdminFee();
  }
  
  updateAdminCaseType(involveProperty: boolean): void {
    this.involveProperty = involveProperty;
    this.calculateAdminFee();
  }
  
  // 非诉讼收费计算
  calculateNonLitigationFee(): void {
    this.nonLitigationResult = this.pricingService.calculateNonLitigationFee(this.nonLitigationAmount, this.serviceType);
  }
  
  updateNonLitigationAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.nonLitigationAmount = value > 0 ? value : 0;
    this.calculateNonLitigationFee();
  }
  
  updateServiceType(serviceType: string): void {
    this.serviceType = serviceType;
    this.isFinancialType = serviceType === 'financial';
    this.calculateNonLitigationFee();
  }
  
  // 风险代理收费计算
  calculateRiskFee(): void {
    this.riskResult = this.pricingService.calculateRiskPricingFee(this.riskAmount);
  }
  
  updateRiskAmount(event: Event): void {
    const value = +(event.target as HTMLInputElement).value;
    this.riskAmount = value > 0 ? value : 1;
    this.calculateRiskFee();
  }
  
  onTabChange(index: number): void {
    // 可以在这里做一些在切换标签页时的操作
  }
} 