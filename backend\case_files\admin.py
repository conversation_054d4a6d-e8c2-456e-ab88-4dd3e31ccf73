from django.contrib import admin
from .models import CaseFileFolder, CaseFile

@admin.register(CaseFileFolder)
class CaseFileFolderAdmin(admin.ModelAdmin):
    list_display = ('id', 'name', 'case', 'parent', 'path', 'created_at')
    list_filter = ('case',)
    search_fields = ('name', 'path', 'case__case_number')
    raw_id_fields = ('case', 'parent')

@admin.register(CaseFile)
class CaseFileAdmin(admin.ModelAdmin):
    list_display = ('id', 'original_filename', 'case', 'folder', 'file_type', 'uploaded_by', 'uploaded_at', 'order')
    list_filter = ('case', 'file_type', 'uploaded_by')
    search_fields = ('original_filename', 'case__case_number')
    raw_id_fields = ('case', 'folder', 'uploaded_by')
    list_editable = ('order',)
