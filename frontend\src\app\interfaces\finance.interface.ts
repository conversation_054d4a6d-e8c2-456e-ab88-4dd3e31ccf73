import { User, Case } from './case.interface';

export interface FinanceRecord {
  id: number;
  transaction_date: string;
  account_type: string;
  account_type_display: string;
  amount: number;
  display_amount: number;
  purpose: string;
  lawyer: User;
  case?: Case;
  remarks?: string;
  created_at: string;
  created_by: User;
  is_income: boolean;
  is_expense: boolean;
}

export interface FinanceRecordCreate {
  transaction_date: string;
  account_type: string;
  display_amount: number;
  purpose: string;
  lawyer_id: number;
  case_id?: number;
  remarks?: string;
}

export interface FinanceAccountType {
  value: string;
  label: string;
  type: 'income' | 'expense';
}

export const FINANCE_ACCOUNT_TYPES: FinanceAccountType[] = [
  // 入账类型
  { value: 'case_income', label: '案件收入', type: 'income' },
  { value: 'lawyer_payment', label: '律师缴费', type: 'income' },
  
  // 出账类型
  { value: 'lawyer_withdrawal', label: '律师提款', type: 'expense' },
  { value: 'lawyer_task_expense', label: '律师应缴费用', type: 'expense' },
  { value: 'office_expense', label: '办公费用', type: 'expense' },
  { value: 'case_refund', label: '案件退款', type: 'expense' },
  { value: 'other_expense', label: '其他支出', type: 'expense' },
]; 