<!-- 移除了顶部搜索区域，搜索功能移到"利益冲突检索"标签页 -->

<div class="container">
  <h2>当事人管理</h2>

  <mat-tab-group>
    <!-- 自然人标签页 -->
    <mat-tab label="自然人">
      <div class="tab-content">
        <!-- 新建自然人按钮 -->
        <div class="action-buttons">
          <button mat-raised-button color="primary" (click)="openCreateNaturalPersonDialog()">
            <mat-icon>person_add</mat-icon>
            新建自然人
          </button>
        </div>

        <div class="data-table-container">
          <div *ngIf="naturalPersonsLoading" class="loading-spinner">
            <mat-spinner diameter="40"></mat-spinner>
          </div>

          <table mat-table [dataSource]="filteredNaturalPersons" *ngIf="!naturalPersonsLoading && filteredNaturalPersons.length > 0">
            <!-- ID 列 -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>ID</th>
              <td mat-cell *matCellDef="let person">{{ person.id }}</td>
            </ng-container>

            <!-- 姓名列 -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>姓名</th>
              <td mat-cell *matCellDef="let person">{{ person.name }}</td>
            </ng-container>

            <!-- 身份证号码列 -->
            <ng-container matColumnDef="id_number">
              <th mat-header-cell *matHeaderCellDef>身份证号码</th>
              <td mat-cell *matCellDef="let person">{{ person.id_number || '-' }}</td>
            </ng-container>

            <!-- 手机号码列 -->
            <ng-container matColumnDef="phone_number">
              <th mat-header-cell *matHeaderCellDef>手机号码</th>
              <td mat-cell *matCellDef="let person">{{ person.phone_number || '-' }}</td>
            </ng-container>

            <!-- 备注列 -->
            <ng-container matColumnDef="remarks">
              <th mat-header-cell *matHeaderCellDef>备注</th>
              <td mat-cell *matCellDef="let person">{{ person.remarks || '-' }}</td>
            </ng-container>

            <!-- 创建时间列 -->
            <ng-container matColumnDef="created_at">
              <th mat-header-cell *matHeaderCellDef>创建时间</th>
              <td mat-cell *matCellDef="let person">{{ formatDate(person.created_at) }}</td>
            </ng-container>

            <!-- 关联案件数量列 -->
            <ng-container matColumnDef="case_count">
              <th mat-header-cell *matHeaderCellDef>关联案件数</th>
              <td mat-cell *matCellDef="let person" [ngClass]="{'has-cases': person.case_count > 0}">
                {{ person.case_count || 0 }}
              </td>
            </ng-container>

            <!-- 操作列 -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>操作</th>
              <td mat-cell *matCellDef="let person">
                <button mat-icon-button color="primary" (click)="editNaturalPerson(person)" matTooltip="编辑">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteNaturalPerson(person)"
                        [disabled]="person.case_count > 0"
                        [matTooltip]="person.case_count > 0 ? '不允许删除有关联案件的当事人' : '删除'">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="naturalPersonColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: naturalPersonColumns;"></tr>
          </table>

          <div *ngIf="!naturalPersonsLoading && filteredNaturalPersons.length === 0" class="no-data">
            没有自然人数据
          </div>
        </div>
      </div>
    </mat-tab>

    <!-- 单位标签页 -->
    <mat-tab label="单位">
      <div class="tab-content">
        <!-- 新建单位按钮 -->
        <div class="action-buttons">
          <button mat-raised-button color="primary" (click)="openCreateLegalEntityDialog()">
            <mat-icon>business</mat-icon>
            新建单位
          </button>
        </div>

        <div class="data-table-container">
          <div *ngIf="legalEntitiesLoading" class="loading-spinner">
            <mat-spinner diameter="40"></mat-spinner>
          </div>

          <table mat-table [dataSource]="filteredLegalEntities" *ngIf="!legalEntitiesLoading && filteredLegalEntities.length > 0">
            <!-- ID 列 -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef>ID</th>
              <td mat-cell *matCellDef="let entity">{{ entity.id }}</td>
            </ng-container>

            <!-- 单位名称列 -->
            <ng-container matColumnDef="name">
              <th mat-header-cell *matHeaderCellDef>单位名称</th>
              <td mat-cell *matCellDef="let entity">{{ entity.name }}</td>
            </ng-container>

            <!-- 法人代表姓名列 -->
            <ng-container matColumnDef="representative_name">
              <th mat-header-cell *matHeaderCellDef>法人代表姓名</th>
              <td mat-cell *matCellDef="let entity">{{ entity.representative_name }}</td>
            </ng-container>

            <!-- 法人代表身份证号码列 -->
            <ng-container matColumnDef="representative_id_number">
              <th mat-header-cell *matHeaderCellDef>法人代表身份证号码</th>
              <td mat-cell *matCellDef="let entity">{{ entity.representative_id_number || '-' }}</td>
            </ng-container>

            <!-- 法人代表手机号码列 -->
            <ng-container matColumnDef="representative_phone_number">
              <th mat-header-cell *matHeaderCellDef>法人代表手机号码</th>
              <td mat-cell *matCellDef="let entity">{{ entity.representative_phone_number || '-' }}</td>
            </ng-container>

            <!-- 统一社会信用代码列 -->
            <ng-container matColumnDef="credit_code">
              <th mat-header-cell *matHeaderCellDef>统一社会信用代码</th>
              <td mat-cell *matCellDef="let entity">{{ entity.credit_code || '-' }}</td>
            </ng-container>

            <!-- 备注列 -->
            <ng-container matColumnDef="remarks">
              <th mat-header-cell *matHeaderCellDef>备注</th>
              <td mat-cell *matCellDef="let entity">{{ entity.remarks || '-' }}</td>
            </ng-container>

            <!-- 创建时间列 -->
            <ng-container matColumnDef="created_at">
              <th mat-header-cell *matHeaderCellDef>创建时间</th>
              <td mat-cell *matCellDef="let entity">{{ formatDate(entity.created_at) }}</td>
            </ng-container>

            <!-- 关联案件数量列 -->
            <ng-container matColumnDef="case_count">
              <th mat-header-cell *matHeaderCellDef>关联案件数</th>
              <td mat-cell *matCellDef="let entity" [ngClass]="{'has-cases': entity.case_count > 0}">
                {{ entity.case_count || 0 }}
              </td>
            </ng-container>

            <!-- 操作列 -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>操作</th>
              <td mat-cell *matCellDef="let entity">
                <button mat-icon-button color="primary" (click)="editLegalEntity(entity)" matTooltip="编辑">
                  <mat-icon>edit</mat-icon>
                </button>
                <button mat-icon-button color="warn" (click)="deleteLegalEntity(entity)"
                        [disabled]="entity.case_count > 0"
                        [matTooltip]="entity.case_count > 0 ? '不允许删除有关联案件的当事人' : '删除'">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="legalEntityColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: legalEntityColumns;"></tr>
          </table>

          <div *ngIf="!legalEntitiesLoading && filteredLegalEntities.length === 0" class="no-data">
            没有单位数据
          </div>
        </div>
      </div>
    </mat-tab>

    <!-- 利益冲突检索标签页 -->
    <mat-tab label="利益冲突检索">
      <div class="tab-content">
        <div class="search-container">
          <div class="search-header">
            <h3>利益冲突检索</h3>
            <p class="search-description">搜索自然人、企业法人和历史案件，以排查潜在的利益冲突</p>
          </div>

          <mat-form-field appearance="outline" class="search-field">
            <mat-label>输入关键词搜索</mat-label>
            <input matInput
                   [(ngModel)]="conflictSearchTerm"
                   (compositionstart)="onCompositionStart()"
                   (compositionend)="onCompositionEnd()"
                   (input)="onConflictSearchInput()"
                   placeholder="姓名/单位名/案由/合同编号">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <!-- 搜索中状态 -->
          <div *ngIf="isConflictSearchLoading" class="loading-spinner">
            <mat-spinner diameter="30"></mat-spinner>
            <p>正在搜索...</p>
          </div>

          <!-- 搜索结果区域 -->
          <div class="search-results" *ngIf="!isConflictSearchLoading && conflictSearchTerm.length > 0">
            <!-- 自然人结果 -->
            <div class="results-section" *ngIf="naturalPersonResults.length > 0">
              <div class="section-header">
                <h4>自然人</h4>
                <span class="result-count">{{ naturalPersonResults.length }} 条结果</span>
              </div>
              <div class="results-list">
                @for (person of naturalPersonResults; track person.id) {
                <div class="result-card natural-person-card">
                  <div class="result-header">
                    <span class="result-name">{{ person.name }}</span>
                    <span class="result-badge natural">自然人</span>
                  </div>
                  <div class="result-info">
                    <div *ngIf="person.additional_info.id_number">身份证号：{{ person.additional_info.id_number }}</div>
                    <div *ngIf="person.additional_info.phone_number">手机号：{{ person.additional_info.phone_number }}</div>
                    <div class="creator-info">
                      <div *ngIf="person.additional_info.creator">创建人：{{ person.additional_info.creator }}</div>
                      <div *ngIf="person.additional_info.created_at">创建时间：{{ person.additional_info.created_at | date:'yyyy-MM-dd HH:mm' }}</div>
                      <div *ngIf="person.additional_info.db_display_name" class="db-source">数据来源：{{ person.additional_info.db_display_name }}</div>
                    </div>
                  </div>
                </div>
                }
              </div>
            </div>

            <!-- 企业法人结果 -->
            <div class="results-section" *ngIf="legalEntityResults.length > 0">
              <div class="section-header">
                <h4>企业法人</h4>
                <span class="result-count">{{ legalEntityResults.length }} 条结果</span>
              </div>
              <div class="results-list">
                @for (entity of legalEntityResults; track entity.id) {
                <div class="result-card legal-entity-card">
                  <div class="result-header">
                    <span class="result-name">{{ entity.name }}</span>
                    <span class="result-badge legal">企业法人</span>
                  </div>
                  <div class="result-info">
                    <div *ngIf="entity.additional_info.representative_name">法人代表：{{ entity.additional_info.representative_name }}</div>
                    <div *ngIf="entity.additional_info.credit_code">信用代码：{{ entity.additional_info.credit_code }}</div>
                    <div class="creator-info">
                      <div *ngIf="entity.additional_info.creator">创建人：{{ entity.additional_info.creator }}</div>
                      <div *ngIf="entity.additional_info.created_at">创建时间：{{ entity.additional_info.created_at | date:'yyyy-MM-dd HH:mm' }}</div>
                      <div *ngIf="entity.additional_info.db_display_name" class="db-source">数据来源：{{ entity.additional_info.db_display_name }}</div>
                    </div>
                  </div>
                </div>
                }
              </div>
            </div>

            <!-- 历史案件结果 -->
            <div class="results-section" *ngIf="legacyCaseResults.length > 0">
              <div class="section-header">
                <h4>历史案件</h4>
                <span class="result-count">{{ legacyCaseResults.length }} 条结果</span>
              </div>
              <div class="results-list">
                @for (legacyCase of legacyCaseResults; track legacyCase.id) {
                <div class="result-card legacy-case-card">
                  <div class="result-header">
                    <span class="result-name">{{ legacyCase.additional_info.contract_number }}</span>
                    <span class="result-badge legacy">历史案件</span>
                  </div>
                  <div class="result-info">
                    <div *ngIf="legacyCase.additional_info.case_cause">案由：{{ legacyCase.additional_info.case_cause }}</div>
                    <div *ngIf="legacyCase.additional_info.client">委托人：{{ legacyCase.additional_info.client }}</div>
                    <div *ngIf="legacyCase.additional_info.opposing_party">对方当事人：{{ legacyCase.additional_info.opposing_party }}</div>
                    <div *ngIf="legacyCase.additional_info.criminal">犯罪嫌疑人：{{ legacyCase.additional_info.criminal }}</div>
                    <div class="case-meta">
                      <span>类型：{{ legacyCase.additional_info.case_type }}</span>
                      <span *ngIf="legacyCase.additional_info.lawyer">承办律师：{{ legacyCase.additional_info.lawyer }}</span>
                      <span *ngIf="legacyCase.additional_info.filing_date">立案日期：{{ legacyCase.additional_info.filing_date | date:'yyyy-MM-dd' }}</span>
                      <span *ngIf="legacyCase.additional_info.db_display_name" class="db-source">数据来源：{{ legacyCase.additional_info.db_display_name }}</span>
                    </div>
                  </div>
                </div>
                }
              </div>
            </div>

            <!-- 无搜索结果提示 -->
            <div *ngIf="naturalPersonResults.length === 0 && legalEntityResults.length === 0 && legacyCaseResults.length === 0" class="no-results">
              <mat-icon>search_off</mat-icon>
              <p>未找到匹配的结果，请尝试其他关键词</p>
            </div>
          </div>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>
