# Generated by Django 5.0.3 on 2025-05-24 20:11

from django.db import migrations


def add_finance_permission_to_admin_group(apps, schema_editor):
    """将财务管理权限添加到行政人员组"""
    Group = apps.get_model('auth', 'Group')
    Permission = apps.get_model('auth', 'Permission')
    ContentType = apps.get_model('contenttypes', 'ContentType')
    
    # 使用指定的数据库
    db_alias = schema_editor.connection.alias
    
    try:
        # 获取或创建行政人员组
        admin_group, created = Group.objects.using(db_alias).get_or_create(name='行政人员')
        if created:
            print(f"创建了新的用户组: {admin_group.name}")
        else:
            print(f"找到现有用户组: {admin_group.name}")
        
        # 获取财务管理权限
        finance_permission = None
        try:
            # 首先尝试通过content_type获取
            finance_content_type, created = ContentType.objects.using(db_alias).get_or_create(
                app_label='finance',
                model='financerecord',
                defaults={'model': 'financerecord'}
            )
            if created:
                print(f"创建了ContentType: {finance_content_type}")
                
            finance_permission, created = Permission.objects.using(db_alias).get_or_create(
                codename='can_manage_finance_records',
                defaults={
                    'name': '财务管理权限',
                    'content_type': finance_content_type,
                }
            )
            if created:
                print(f"创建了权限: {finance_permission.name}")
                
        except Exception as e:
            print(f"通过ContentType获取财务权限失败: {e}")
            # 如果通过content_type找不到，尝试直接查找
            finance_permission = Permission.objects.using(db_alias).filter(
                codename='can_manage_finance_records'
            ).first()
            
        if not finance_permission:
            print("警告: 未找到财务管理权限，可能需要先运行finance应用的迁移")
            print("提示: 请确保运行了 'python manage.py migrate finance' 命令")
            return
    
        # 使用through模型手动创建组权限关联
        GroupPermission = admin_group.permissions.through
        relation, created = GroupPermission.objects.using(db_alias).get_or_create(
            group=admin_group,
            permission=finance_permission
        )
        if created:
            print(f"成功将权限 '{finance_permission.name}' 添加到组 '{admin_group.name}'")
        else:
            print(f"权限 '{finance_permission.name}' 已经存在于组 '{admin_group.name}' 中")
        
    except Exception as e:
        print(f"添加财务权限到行政组时出错: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")


def remove_finance_permission_from_admin_group(apps, schema_editor):
    """从行政人员组中移除财务管理权限（回滚操作）"""
    Group = apps.get_model('auth', 'Group')
    Permission = apps.get_model('auth', 'Permission')
    ContentType = apps.get_model('contenttypes', 'ContentType')
    
    # 使用指定的数据库
    db_alias = schema_editor.connection.alias
    
    try:
        # 获取行政人员组
        admin_group = Group.objects.using(db_alias).filter(name='行政人员').first()
        if not admin_group:
            print("未找到行政人员组，无需回滚")
            return
        
        # 获取财务管理权限
        finance_permission = None
        try:
            finance_content_type = ContentType.objects.using(db_alias).get(app_label='finance', model='financerecord')
            finance_permission = Permission.objects.using(db_alias).get(
                codename='can_manage_finance_records',
                content_type=finance_content_type
            )
        except (ContentType.DoesNotExist, Permission.DoesNotExist):
            finance_permission = Permission.objects.using(db_alias).filter(
                codename='can_manage_finance_records'
            ).first()
            
        if not finance_permission:
            print("未找到财务管理权限，无需回滚")
            return
        
                    # 从组中移除权限
            GroupPermission = admin_group.permissions.through
            deleted_count, _ = GroupPermission.objects.using(db_alias).filter(
                group=admin_group,
                permission=finance_permission
            ).delete()
            if deleted_count > 0:
                print(f"成功从组 '{admin_group.name}' 中移除权限 '{finance_permission.name}'")
            else:
                print(f"权限 '{finance_permission.name}' 不在组 '{admin_group.name}' 中")
            
    except Exception as e:
        print(f"从行政组移除财务权限时出错: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0003_create_groups_with_permissions'),
        ('finance', '0002_financerecord'),  # 确保财务权限已创建
    ]

    operations = [
        migrations.RunPython(
            add_finance_permission_to_admin_group,
            remove_finance_permission_from_admin_group,
            hints={'verbosity': 2}
        ),
    ]
