<div class="home-container">
  <div class="welcome-section">
    <h1>欢迎来到广东承诺律师事务所管理系统</h1>
    <p class="subtitle">专业服务，值得信赖</p>
  </div>

  <div class="cards-container">
    <mat-card class="info-card">
      <mat-card-header>
        <mat-icon mat-card-avatar>gavel</mat-icon>
        <mat-card-title>案件管理</mat-card-title>
        <mat-card-subtitle>高效处理法律事务</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p>
          通过我们的案件管理系统，您可以轻松查看所有案件的进展状态、重要文件和关键日期。
          律师团队协作更高效，客户沟通更顺畅。
        </p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" [routerLink]="['/cases']" *ngIf="authService.isLoggedIn()">
          浏览案件
        </button>
        <button mat-raised-button color="primary" [routerLink]="['/login']" *ngIf="!authService.isLoggedIn()">
          登录系统
        </button>
      </mat-card-actions>
    </mat-card>

    <mat-card class="info-card">
      <mat-card-header>
        <mat-icon mat-card-avatar>description</mat-icon>
        <mat-card-title>文档模板</mat-card-title>
        <mat-card-subtitle>智能文档生成</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p>
          使用我们的文档模板系统，只需上传Word或Excel模板文件，填写相应信息，
          即可快速生成个性化法律文书，大幅提升工作效率。
        </p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="accent" [routerLink]="['/templates']" *ngIf="authService.isLoggedIn()">
          管理模板
        </button>
        <button mat-raised-button color="accent" *ngIf="!authService.isLoggedIn()" disabled>
          体验模板功能
        </button>
      </mat-card-actions>
    </mat-card>

    <mat-card class="info-card">
      <mat-card-header>
        <mat-icon mat-card-avatar>people</mat-icon>
        <mat-card-title>团队协作</mat-card-title>
        <mat-card-subtitle>专业律师团队</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p>
          我们的团队由经验丰富的律师组成，专注于各个法律领域，
          为客户提供全面的法律服务。通过系统内部协作，我们能够更好地满足客户需求。
        </p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="warn" [routerLink]="['/users/me']" *ngIf="authService.isLoggedIn()">
          个人资料
        </button>
        <button mat-raised-button color="warn" *ngIf="!authService.isLoggedIn()" disabled>
          查看团队
        </button>
      </mat-card-actions>
    </mat-card>

    <mat-card class="info-card">
      <mat-card-header>
        <mat-icon mat-card-avatar>calculate</mat-icon>
        <mat-card-title>收费计算</mat-card-title>
        <mat-card-subtitle>透明合理的收费标准</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p>
          我们提供各类案件的收费计算器，包括民事诉讼、刑事诉讼、行政诉讼、非诉讼服务和风险代理，
          让您清楚了解各项法律服务的收费标准。
        </p>
      </mat-card-content>
      <mat-card-actions>
        <button mat-raised-button color="primary" [routerLink]="['/pricing']" *ngIf="authService.isLoggedIn()">
          查看收费标准
        </button>
        <button mat-raised-button color="primary" *ngIf="!authService.isLoggedIn()" disabled>
          收费计算
        </button>
      </mat-card-actions>
    </mat-card>
  </div>
</div> 