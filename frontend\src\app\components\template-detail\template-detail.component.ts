import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDividerModule } from '@angular/material/divider';
import { MatTabsModule } from '@angular/material/tabs';
import { MatListModule } from '@angular/material/list';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { TemplateService, TemplatePackage, TemplateFile } from '../../services/template.service';

@Component({
  selector: 'app-template-detail',
  templateUrl: './template-detail.component.html',
  styleUrls: ['./template-detail.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatInputModule,
    MatFormFieldModule,
    MatDividerModule,
    MatTabsModule,
    MatListModule,
    MatExpansionModule,
    MatSnackBarModule
  ]
})
export class TemplateDetailComponent implements OnInit {
  template: TemplatePackage | null = null;
  isLoading = true;
  error: string | null = null;
  
  uploadForm: FormGroup;
  fillForm: FormGroup;
  replacementsForm: FormGroup;
  
  fileToUpload: File | null = null;
  generatingFiles = false;
  activeTab: string = 'files'; // 默认显示文件标签页

  constructor(
    private route: ActivatedRoute,
    private templateService: TemplateService,
    private fb: FormBuilder,
    private snackBar: MatSnackBar
  ) {
    this.uploadForm = this.fb.group({
      file: ['']
    });
    
    this.fillForm = this.fb.group({
      replacements: this.fb.array([])
    });
    
    this.replacementsForm = this.fb.group({});
  }

  ngOnInit(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id) {
      this.loadTemplate(+id);
    }
  }

  loadTemplate(id: number): void {
    this.isLoading = true;
    this.templateService.getTemplate(id).subscribe({
      next: (template) => {
        this.template = template;
        this.isLoading = false;
        this.initReplacementsForm();
      },
      error: (error) => {
        this.error = '加载模板详情失败';
        this.isLoading = false;
        console.error('加载模板详情出错:', error);
      }
    });
  }

  get replacements(): FormArray {
    return this.fillForm.get('replacements') as FormArray;
  }

  initReplacementsForm(): void {
    // 创建一些默认的替换字段
    const defaultFields = ['姓名', '性别', '年龄', '地址', '电话', '日期'];
    
    const formGroup: any = {};
    defaultFields.forEach(field => {
      formGroup[field] = [''];
    });
    
    this.replacementsForm = this.fb.group(formGroup);
  }

  onFileSelected(event: Event): void {
    const element = event.target as HTMLInputElement;
    if (element.files && element.files.length > 0) {
      this.fileToUpload = element.files[0];
    }
  }

  uploadFile(): void {
    if (!this.fileToUpload || !this.template) {
      return;
    }

    this.templateService.uploadFile(this.template.id, this.fileToUpload).subscribe({
      next: (file) => {
        this.snackBar.open('文件上传成功', '关闭', { duration: 3000 });
        // 重新加载模板信息以显示新上传的文件
        this.loadTemplate(this.template!.id);
        // 重置文件上传表单
        this.uploadForm.reset();
        this.fileToUpload = null;
      },
      error: (error) => {
        this.snackBar.open('文件上传失败', '关闭', { duration: 3000 });
        console.error('文件上传出错:', error);
      }
    });
  }

  generateFiles(): void {
    if (!this.template) return;
    
    this.generatingFiles = true;
    
    // 获取表单中的替换值
    // 注意：
    // 1. 对于存在的模板变量，即使值为空也会进行替换（替换为空字符串）
    // 2. 对于不存在的模板变量，后端会保留原文不做替换
    const replacements: Record<string, string> = {};
    
    Object.keys(this.replacementsForm.controls).forEach(key => {
      const value = this.replacementsForm.get(key)?.value;
      // 确保即使值为空也进行替换，null 或 undefined 转换为空字符串
      replacements[key] = value ?? '';
    });
    
    this.templateService.generateFiles(this.template.id, replacements).subscribe({
      next: (blob) => {
        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.style.display = 'none';
        a.href = url;
        a.download = `${this.template!.name}_filled.zip`;
        a.click();
        window.URL.revokeObjectURL(url);
        
        this.snackBar.open('文件生成成功，开始下载', '关闭', { duration: 3000 });
        this.generatingFiles = false;
      },
      error: (error) => {
        this.snackBar.open('文件生成失败', '关闭', { duration: 3000 });
        console.error('文件生成出错:', error);
        this.generatingFiles = false;
      }
    });
  }

  getFileIcon(fileType: string): string {
    switch (fileType) {
      case 'docx':
        return 'description';
      case 'xlsx':
        return 'table_chart';
      default:
        return 'insert_drive_file';
    }
  }

  deleteFile(file: TemplateFile): void {
    if (!this.template) return;
    
    if (confirm(`确定要删除文件 "${file.original_filename}" 吗？`)) {
      this.templateService.deleteFile(this.template.id, file.id).subscribe({
        next: () => {
          this.snackBar.open('文件删除成功', '关闭', { duration: 3000 });
          // 重新加载模板信息以更新文件列表
          this.loadTemplate(this.template!.id);
        },
        error: (error) => {
          this.snackBar.open('文件删除失败', '关闭', { duration: 3000 });
          console.error('文件删除出错:', error);
        }
      });
    }
  }
} 