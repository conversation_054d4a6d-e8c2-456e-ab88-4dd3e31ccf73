<div class="withdrawal-list">
  <div class="header">
    <h2>提款申请管理</h2>
    <button class="refresh-btn" (click)="loadWithdrawals()">刷新</button>
  </div>

  <div *ngIf="loading" class="loading">
    加载中...
  </div>

  <div *ngIf="error" class="error">
    {{ error }}
  </div>

  <div *ngIf="!loading && !error && withdrawals.length === 0" class="empty-state">
    <div class="empty-icon">📋</div>
    <p>暂无提款申请记录</p>
  </div>

  <div *ngIf="!loading && !error && withdrawals.length > 0">
    <table class="withdrawal-table">
      <thead>
        <tr>
          <th>案件信息</th>
          <th>申请人</th>
          <th>申请金额</th>
          <th>应扣税费</th>
          <th>实际金额</th>
          <th>状态</th>
          <th>申请时间</th>
          <th>审批人</th>
          <th>其他应扣项目</th>
          <th>备注说明</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let withdrawal of withdrawals">
          <td>
            <div class="case-info">
              <div class="case-number">{{ withdrawal.case.case_number }}</div>
              <div class="case-cause">{{ withdrawal.case.case_cause }}</div>
            </div>
          </td>
          <td>{{ getUserDisplayName(withdrawal.requester) }}</td>
          <td class="amount">¥{{ withdrawal.amount.toFixed(2) }}</td>
          <td class="deduction-tax">¥{{ withdrawal.deduction_tax.toFixed(2) }}</td>
          <td class="final-amount">
            <strong>¥{{ withdrawal.final_amount.toFixed(2) }}</strong>
          </td>
          <td>
            <span class="status" [ngClass]="getStatusClass(withdrawal.status)">
              {{ withdrawal.status_display }}
            </span>
          </td>
          <td>{{ formatDate(withdrawal.created_at) }}</td>
          <td>{{ withdrawal.approver ? getUserDisplayName(withdrawal.approver) : '-' }}</td>
          <td class="other-deductions">{{ withdrawal.other_deductions || '-' }}</td>
          <td class="remarks">{{ withdrawal.remarks || '-' }}</td>
          <td class="actions">
            <button 
              *ngIf="withdrawal.can_approve" 
              class="btn btn-approve"
              (click)="approveWithdrawal(withdrawal)">
              批准
            </button>
            <button 
              *ngIf="withdrawal.can_approve" 
              class="btn btn-reject"
              (click)="rejectWithdrawal(withdrawal)">
              拒绝
            </button>
            <button 
              *ngIf="withdrawal.status === 'APPROVED'" 
              class="btn btn-print"
              (click)="printWithdrawal(withdrawal)">
              打印
            </button>
            <button 
              *ngIf="withdrawal.can_delete" 
              class="btn btn-delete"
              (click)="deleteWithdrawal(withdrawal)">
              删除
            </button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
