FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV DJANGO_SETTINGS_MODULE law_firm.settings_prod

# debian chinese mirror
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    dos2unix \
    poppler-utils \
    libreoffice-writer \
    default-jre \
    python3-dev \
    libpq-dev \
    zlib1g-dev \
    libjpeg-dev \
    libfreetype6-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*


# 安装Python依赖
COPY backend/requirements.txt backend/requirements.prod.txt .
RUN pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip install --no-cache-dir -r requirements.prod.txt

# 日志目录
RUN mkdir -p /var/log/django

# 创建非root用户
RUN useradd -m appuser && chown -R appuser:appuser /app /var/log/django
USER appuser

# 复制项目文件
COPY --chown=appuser:appuser ./backend/. .

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 添加等待脚本
COPY --chown=appuser:appuser ./backend/wait-for-it.sh /wait-for-it.sh
RUN chmod +x /wait-for-it.sh

EXPOSE 8000

CMD ["/wait-for-it.sh", "--", "gunicorn", "--bind", "0.0.0.0:8000", "law_firm.wsgi:application"] 