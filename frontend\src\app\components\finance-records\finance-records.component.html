<div class="finance-records-container">
  <mat-card class="header-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>account_balance_wallet</mat-icon>
        财务收支管理
      </mat-card-title>
      <mat-card-subtitle>管理律师事务所的财务收支记录</mat-card-subtitle>
    </mat-card-header>
    
    <mat-card-actions>
      <button mat-raised-button color="primary" 
              *ngIf="hasFinancePermission"
              (click)="openCreateDialog()">
        <mat-icon>add</mat-icon>
        添加财务记录
      </button>
      <span *ngIf="!hasFinancePermission" class="permission-notice">
        您只能查看自己相关的财务记录
      </span>
    </mat-card-actions>
  </mat-card>

  <!-- 筛选区域 -->
  <mat-card class="filter-card">
    <mat-card-content>
      <form [formGroup]="filterForm" class="filter-form">
        <mat-form-field appearance="outline">
          <mat-label>律师</mat-label>
          <mat-select formControlName="lawyer_id">
            <mat-option value="">全部律师</mat-option>
            <mat-option *ngFor="let lawyer of lawyers" [value]="lawyer.id">
              {{ getUserDisplayName(lawyer) }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>账目类型</mat-label>
          <mat-select formControlName="account_type">
            <mat-option value="">全部类型</mat-option>
            <mat-option *ngFor="let type of accountTypes" [value]="type.value">
              {{ type.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>开始日期</mat-label>
          <input matInput [matDatepicker]="startPicker" formControlName="start_date">
          <mat-datepicker-toggle matSuffix [for]="startPicker"></mat-datepicker-toggle>
          <mat-datepicker #startPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>结束日期</mat-label>
          <input matInput [matDatepicker]="endPicker" formControlName="end_date">
          <mat-datepicker-toggle matSuffix [for]="endPicker"></mat-datepicker-toggle>
          <mat-datepicker #endPicker></mat-datepicker>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>关键词搜索</mat-label>
          <input matInput formControlName="keyword" placeholder="搜索用途、备注、律师姓名">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <button mat-button type="button" (click)="resetFilters()">
          <mat-icon>clear</mat-icon>
          重置筛选
        </button>
      </form>
    </mat-card-content>
  </mat-card>

  <!-- 数据表格 -->
  <mat-card class="table-card">
    <mat-card-content>
      <div *ngIf="isLoading" class="loading-container">
        <mat-spinner></mat-spinner>
      </div>

      <div *ngIf="!isLoading" class="table-container">
        <table mat-table [dataSource]="filteredRecords" class="finance-table">
          <!-- 交易日期列 -->
          <ng-container matColumnDef="transaction_date">
            <th mat-header-cell *matHeaderCellDef>交易日期</th>
            <td mat-cell *matCellDef="let record">
              {{ formatDate(record.transaction_date) }}
            </td>
          </ng-container>

          <!-- 律师列 -->
          <ng-container matColumnDef="lawyer">
            <th mat-header-cell *matHeaderCellDef>律师</th>
            <td mat-cell *matCellDef="let record">
              {{ getUserDisplayName(record.lawyer) }}
            </td>
          </ng-container>

          <!-- 案件列 -->
          <ng-container matColumnDef="case">
            <th mat-header-cell *matHeaderCellDef>关联案件</th>
            <td mat-cell *matCellDef="let record">
              <div *ngIf="record.case; else noCaseTemplate" class="case-info">
                <div class="case-number">{{ record.case.case_number }}</div>
                <div class="case-cause" [matTooltip]="record.case.case_cause">
                  {{ record.case.case_cause }}
                </div>
              </div>
              <ng-template #noCaseTemplate>
                <span class="no-case">-</span>
              </ng-template>
            </td>
          </ng-container>

          <!-- 账目类型列 -->
          <ng-container matColumnDef="account_type">
            <th mat-header-cell *matHeaderCellDef>账目类型</th>
            <td mat-cell *matCellDef="let record">
              <mat-chip [ngClass]="getAccountTypeClass(record)">
                {{ getAccountTypeDisplay(record.account_type) }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- 用途列 -->
          <ng-container matColumnDef="purpose">
            <th mat-header-cell *matHeaderCellDef>用途</th>
            <td mat-cell *matCellDef="let record" [matTooltip]="record.remarks">
              {{ record.purpose }}
            </td>
          </ng-container>

          <!-- 金额列 -->
          <ng-container matColumnDef="display_amount">
            <th mat-header-cell *matHeaderCellDef>金额</th>
            <td mat-cell *matCellDef="let record" [ngClass]="record.is_income ? 'income-amount' : 'expense-amount'">
              {{ formatAmount(record) }}
            </td>
          </ng-container>

          <!-- 创建人列 -->
          <ng-container matColumnDef="created_by">
            <th mat-header-cell *matHeaderCellDef>创建人</th>
            <td mat-cell *matCellDef="let record">
              {{ getUserDisplayName(record.created_by) }}
            </td>
          </ng-container>

          <!-- 创建时间列 -->
          <ng-container matColumnDef="created_at">
            <th mat-header-cell *matHeaderCellDef>创建时间</th>
            <td mat-cell *matCellDef="let record">
              {{ formatDate(record.created_at) }}
            </td>
          </ng-container>

          <!-- 操作列 -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>操作</th>
            <td mat-cell *matCellDef="let record">
              <button mat-icon-button 
                      *ngIf="hasFinancePermission"
                      (click)="deleteRecord(record)"
                      matTooltip="删除"
                      color="warn">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>

        <div *ngIf="filteredRecords.length === 0 && !isLoading" class="no-data">
          <mat-icon>inbox</mat-icon>
          <p>暂无财务记录</p>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div> 