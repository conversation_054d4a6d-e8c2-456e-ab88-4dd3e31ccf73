# 前端构建阶段
FROM node:20-alpine AS builder

WORKDIR /app
COPY ./frontend/package*.json ./
RUN npm config set registry https://mirrors.cloud.tencent.com/npm/
RUN npm install

COPY ./frontend/. .
RUN npm run build

# 生产阶段
FROM nginx:alpine

# 复制nginx配置
COPY ./deploy/nginx.conf /etc/nginx/conf.d/default.conf

# 复制构建产物
COPY --from=builder /app/dist/law-firm-frontend/browser /usr/share/nginx/html

# 复制后端静态文件
# COPY --from=backend_staticfile /app/staticfiles /var/www/staticfiles

EXPOSE 80