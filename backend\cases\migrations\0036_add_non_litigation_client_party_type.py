# Generated by Django 5.0.3 on 2025-06-19 17:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('cases', '0035_letterrecord_approved_at_sealusagerecord_approved_at'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='caseparty',
            name='party_type',
            field=models.CharField(choices=[('PLAINTIFF', '原告'), ('DEFENDANT', '被告'), ('THIRD_PARTY', '第三人'), ('SUSPECT', '犯罪嫌疑人'), ('SUSPECT_FAMILY', '嫌疑人家属'), ('NON_LITIGATION_CLIENT', '非诉委托人')], max_length=30, verbose_name='当事人类型'),
        ),
    ]
