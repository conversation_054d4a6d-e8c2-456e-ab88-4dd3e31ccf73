---
description: 
globs: 
alwaysApply: true
---
在开发环境下，我们有三个container来分辨运行前端，后端和数据库。
1，前端容器名字: frontend，目录名：frontend
2，后端容器名字: web，目录名：backend
3，数据库容器名字: db

开发的过程中，请默认所有container已经再运行，不要再次启动container或者前后端。

请使用docker-compose exec来执行后端的修改。

请参考 docker-compose.yml
后端请参考 docker-compose.yml

对UI做出改动之后，请使用查看前端的日志来确认修改是否成功。
docker-compose logs frontend

对后端逻辑做出改动之后，请使用查看后端的日志来确认修改是否成功。
docker-compose logs web

产生migration是，也请在container内操作，而不是直接在本地执行。

不要使用互动方式的django shell，因为Cursor不支持。