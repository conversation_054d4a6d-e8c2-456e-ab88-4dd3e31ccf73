-- Initialize additional databases for Django application
-- This script can be run multiple times safely

-- Create the second database if it doesn't exist
SELECT 'CREATE DATABASE lawcase_db2'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'lawcase_db2')\gexec

-- Grant privileges to the user for both databases
GRANT ALL PRIVILEGES ON DATABASE lawcase_db TO lawcase_user;
GRANT ALL PRIVILEGES ON DATABASE lawcase_db2 TO lawcase_user;
