import { Compo<PERSON>, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTableModule } from '@angular/material/table';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { environment } from '../../../environments/environment';
import { forkJoin, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { PartyDialogComponent } from '../shared/party-dialog/party-dialog.component';
import { CreateNaturalPersonDialogComponent } from './create-natural-person-dialog.component';
import { CreateLegalEntityDialogComponent } from './create-legal-entity-dialog.component';
import { MatTabGroup } from '@angular/material/tabs';

export interface NaturalPerson {
  id: number;
  name: string;
  id_number?: string;
  phone_number?: string;
  remarks?: string;
  created_at: string;
  updated_at: string;
  case_count?: number;
}

export interface LegalEntity {
  id: number;
  name: string;
  representative_name?: string;
  representative_id_number?: string;
  representative_phone_number?: string;
  credit_code?: string;
  remarks?: string;
  created_at: string;
  updated_at: string;
  case_count?: number;
}

export interface UniversalSearchResult {
  id: number;
  name: string;
  type: 'natural_person' | 'legal_entity' | 'legacy_case';
  type_display: string;
  additional_info: any;
}

@Component({
  selector: 'app-party-management',
  templateUrl: './party-management.component.html',
  styleUrls: ['./party-management.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatTabsModule,
    MatTableModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatDialogModule,
    MatSnackBarModule,
    MatPaginatorModule,
    MatTooltipModule
  ]
})
export class PartyManagementComponent implements OnInit, OnDestroy {
  @ViewChild(MatTabGroup) tabGroup!: MatTabGroup;

  naturalPersons: NaturalPerson[] = [];
  legalEntities: LegalEntity[] = [];

  filteredNaturalPersons: NaturalPerson[] = [];
  filteredLegalEntities: LegalEntity[] = [];

  naturalPersonsLoading = false;
  legalEntitiesLoading = false;

  naturalPersonSearchTerm = '';
  legalEntitySearchTerm = '';

  naturalPersonColumns: string[] = ['id', 'name', 'id_number', 'phone_number', 'remarks', 'created_at', 'case_count', 'actions'];
  legalEntityColumns: string[] = ['id', 'name', 'representative_name', 'representative_id_number', 'representative_phone_number', 'credit_code', 'remarks', 'created_at', 'case_count', 'actions'];

  // 利益冲突检索相关属性
  conflictSearchTerm: string = '';
  naturalPersonResults: UniversalSearchResult[] = [];
  legalEntityResults: UniversalSearchResult[] = [];
  legacyCaseResults: UniversalSearchResult[] = [];
  isConflictSearchLoading: boolean = false;

  // Properties for debouncing
  private conflictSearchDebounceTimer: any;
  private readonly debounceTimeMs = 300; // Adjust debounce time as needed (e.g., 300-500ms)
  
  // Properties for handling IME composition
  private isComposing: boolean = false;

  constructor(
    private http: HttpClient,
    private dialog: MatDialog,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadNaturalPersons();
    this.loadLegalEntities();
  }

  ngOnDestroy(): void {
    // Important: Clear the timer when the component is destroyed to prevent memory leaks.
    clearTimeout(this.conflictSearchDebounceTimer);
  }

  loadNaturalPersons() {
    this.naturalPersonsLoading = true;
    this.http.get<NaturalPerson[]>(`${environment.apiUrl}/natural-persons/`)
      .subscribe({
        next: (data) => {
          this.naturalPersons = data;
          this.loadCaseCountsForNaturalPersons();
        },
        error: (error) => {
          console.error('加载自然人列表失败', error);
          this.snackBar.open('加载自然人列表失败', '关闭', { duration: 3000 });
          this.naturalPersonsLoading = false;
        }
      });
  }

  loadLegalEntities() {
    this.legalEntitiesLoading = true;
    this.http.get<LegalEntity[]>(`${environment.apiUrl}/legal-entities/`)
      .subscribe({
        next: (data) => {
          this.legalEntities = data;
          this.loadCaseCountsForLegalEntities();
        },
        error: (error) => {
          console.error('加载单位列表失败', error);
          this.snackBar.open('加载单位列表失败', '关闭', { duration: 3000 });
          this.legalEntitiesLoading = false;
        }
      });
  }

  loadCaseCountsForNaturalPersons() {
    const requests = this.naturalPersons.map(person =>
      this.http.get<any>(`${environment.apiUrl}/case-parties/?natural_person=${person.id}`).pipe(
        map(response => ({ id: person.id, count: response.length })),
        catchError(error => {
          console.error(`获取自然人 ${person.id} 的案件数量失败`, error);
          return of({ id: person.id, count: 0 });
        })
      )
    );

    if (requests.length === 0) {
      this.filteredNaturalPersons = this.naturalPersons;
      this.naturalPersonsLoading = false;
      return;
    }

    forkJoin(requests).subscribe(results => {
      results.forEach(result => {
        const person = this.naturalPersons.find(p => p.id === result.id);
        if (person) {
          person.case_count = result.count;
        }
      });

      this.filteredNaturalPersons = [...this.naturalPersons];
      this.applyNaturalPersonFilter();
      this.naturalPersonsLoading = false;
    });
  }

  loadCaseCountsForLegalEntities() {
    const requests = this.legalEntities.map(entity =>
      this.http.get<any>(`${environment.apiUrl}/case-parties/?legal_entity=${entity.id}`).pipe(
        map(response => ({ id: entity.id, count: response.length })),
        catchError(error => {
          console.error(`获取单位 ${entity.id} 的案件数量失败`, error);
          return of({ id: entity.id, count: 0 });
        })
      )
    );

    if (requests.length === 0) {
      this.filteredLegalEntities = this.legalEntities;
      this.legalEntitiesLoading = false;
      return;
    }

    forkJoin(requests).subscribe(results => {
      results.forEach(result => {
        const entity = this.legalEntities.find(e => e.id === result.id);
        if (entity) {
          entity.case_count = result.count;
        }
      });

      this.filteredLegalEntities = [...this.legalEntities];
      this.applyLegalEntityFilter();
      this.legalEntitiesLoading = false;
    });
  }

  searchNaturalPersons() {
    this.applyNaturalPersonFilter();
  }

  searchLegalEntities() {
    this.applyLegalEntityFilter();
  }

  applyNaturalPersonFilter() {
    const searchTerm = this.naturalPersonSearchTerm.toLowerCase().trim();

    if (!searchTerm) {
      this.filteredNaturalPersons = [...this.naturalPersons];
      return;
    }

    this.filteredNaturalPersons = this.naturalPersons.filter(person =>
      (person.name && person.name.toLowerCase().includes(searchTerm)) ||
      (person.id_number && person.id_number.toLowerCase().includes(searchTerm)) ||
      (person.phone_number && person.phone_number.toLowerCase().includes(searchTerm)) ||
      (person.remarks && person.remarks.toLowerCase().includes(searchTerm))
    );
  }

  applyLegalEntityFilter() {
    const searchTerm = this.legalEntitySearchTerm.toLowerCase().trim();

    if (!searchTerm) {
      this.filteredLegalEntities = [...this.legalEntities];
      return;
    }

    this.filteredLegalEntities = this.legalEntities.filter(entity =>
      (entity.name && entity.name.toLowerCase().includes(searchTerm)) ||
      (entity.representative_name && entity.representative_name.toLowerCase().includes(searchTerm)) ||
      (entity.representative_id_number && entity.representative_id_number.toLowerCase().includes(searchTerm)) ||
      (entity.representative_phone_number && entity.representative_phone_number.toLowerCase().includes(searchTerm)) ||
      (entity.credit_code && entity.credit_code.toLowerCase().includes(searchTerm)) ||
      (entity.remarks && entity.remarks.toLowerCase().includes(searchTerm))
    );
  }

  // 打开创建自然人对话框
  openCreateNaturalPersonDialog(): void {
    const dialogRef = this.dialog.open(CreateNaturalPersonDialogComponent, {
      width: '600px',
      maxHeight: '85vh',
      disableClose: false,
      panelClass: 'custom-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // 添加到列表并刷新显示
        result.case_count = 0; // 新创建的自然人没有关联案件
        this.naturalPersons.push(result);
        this.applyNaturalPersonFilter();
      }
    });
  }

  // 打开创建单位对话框
  openCreateLegalEntityDialog(): void {
    const dialogRef = this.dialog.open(CreateLegalEntityDialogComponent, {
      width: '750px',
      maxHeight: '85vh',
      disableClose: false,
      panelClass: 'custom-dialog-container'
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // 添加到列表并刷新显示
        result.case_count = 0; // 新创建的单位没有关联案件
        this.legalEntities.push(result);
        this.applyLegalEntityFilter();
      }
    });
  }

  deleteNaturalPerson(person: NaturalPerson) {
    // 检查是否有关联案件
    if (person.case_count && person.case_count > 0) {
      this.snackBar.open('不能删除有关联案件的当事人', '关闭', { duration: 3000 });
      return;
    }

    if (confirm('确定要删除此自然人吗？此操作不可撤销。')) {
      this.http.delete(`${environment.apiUrl}/natural-persons/${person.id}/`)
        .subscribe({
          next: () => {
            this.snackBar.open('删除自然人成功', '关闭', { duration: 3000 });
            this.naturalPersons = this.naturalPersons.filter(p => p.id !== person.id);
            this.applyNaturalPersonFilter();
          },
          error: (error) => {
            console.error('删除自然人失败', error);
            this.snackBar.open('删除自然人失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 3000 });
          }
        });
    }
  }

  deleteLegalEntity(entity: LegalEntity) {
    // 检查是否有关联案件
    if (entity.case_count && entity.case_count > 0) {
      this.snackBar.open('不能删除有关联案件的当事人', '关闭', { duration: 3000 });
      return;
    }

    if (confirm('确定要删除此单位吗？此操作不可撤销。')) {
      this.http.delete(`${environment.apiUrl}/legal-entities/${entity.id}/`)
        .subscribe({
          next: () => {
            this.snackBar.open('删除单位成功', '关闭', { duration: 3000 });
            this.legalEntities = this.legalEntities.filter(e => e.id !== entity.id);
            this.applyLegalEntityFilter();
          },
          error: (error) => {
            console.error('删除单位失败', error);
            this.snackBar.open('删除单位失败: ' + (error.error?.detail || '未知错误'), '关闭', { duration: 3000 });
          }
        });
    }
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // 编辑自然人
  editNaturalPerson(person: NaturalPerson) {
    const dialogRef = this.dialog.open(PartyDialogComponent, {
      width: '800px',
      data: {
        isEdit: true,
        partyData: {
          natural_person: person
        }
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // 刷新数据
        this.loadNaturalPersons();
        this.snackBar.open('自然人更新成功', '关闭', { duration: 3000 });
      }
    });
  }

  // 编辑单位
  editLegalEntity(entity: LegalEntity) {
    const dialogRef = this.dialog.open(PartyDialogComponent, {
      width: '800px',
      data: {
        isEdit: true,
        partyData: {
          legal_entity: entity
        }
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // 刷新数据
        this.loadLegalEntities();
        this.snackBar.open('单位更新成功', '关闭', { duration: 3000 });
      }
    });
  }

  // Handle IME composition start
  public onCompositionStart(): void {
    this.isComposing = true;
  }

  // Handle IME composition end
  public onCompositionEnd(): void {
    this.isComposing = false;
    // Trigger search after composition ends
    this.onConflictSearchInput();
  }

  // This method is called by (input) from the conflict search template
  public onConflictSearchInput(): void {
    // Don't search while composing (e.g., using Chinese input method)
    if (this.isComposing) {
      return;
    }

    // Clear any existing pending timer
    clearTimeout(this.conflictSearchDebounceTimer);

    // If the search term is empty, clear results immediately
    if (!this.conflictSearchTerm || this.conflictSearchTerm.trim().length === 0) {
      this.naturalPersonResults = [];
      this.legalEntityResults = [];
      this.legacyCaseResults = [];
      this.isConflictSearchLoading = false;
      return;
    }

    // Set a new timer to execute the search after a pause in typing
    this.conflictSearchDebounceTimer = setTimeout(() => {
      this.executeConflictSearch();
    }, this.debounceTimeMs);
  }

  // This method contains the actual conflict search logic using the universal_search API
  private executeConflictSearch() {
    if (!this.conflictSearchTerm || this.conflictSearchTerm.trim().length === 0) {
      this.naturalPersonResults = [];
      this.legalEntityResults = [];
      this.legacyCaseResults = [];
      return;
    }

    this.isConflictSearchLoading = true;
    const searchTerm = this.conflictSearchTerm.trim();

    // 使用新的通用搜索API
    this.http.get<UniversalSearchResult[]>(`${environment.apiUrl}/cases/universal_search/?keyword=${encodeURIComponent(searchTerm)}`).subscribe({
      next: (results) => {
        // 按类型分组搜索结果
        this.naturalPersonResults = results.filter(r => r.type === 'natural_person');
        this.legalEntityResults = results.filter(r => r.type === 'legal_entity');
        this.legacyCaseResults = results.filter(r => r.type === 'legacy_case');
        this.isConflictSearchLoading = false;
      },
      error: (error) => {
        console.error('利益冲突检索出错：', error);
        this.naturalPersonResults = [];
        this.legalEntityResults = [];
        this.legacyCaseResults = [];
        this.isConflictSearchLoading = false;
      }
    });
  }

  // 移除旧的selectParty方法，利益冲突检索主要用于查看信息，不需要直接编辑功能
}
