<div class="pricing-calculator">
  <h1>计时收费计算器</h1>
  
  <mat-card class="calculator-card">
    <mat-card-content>
      <div class="form-row">
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>工作时长（小时）</mat-label>
          <input matInput type="number" min="1" [value]="hours" (input)="updateHours($event)">
          <mat-hint>请输入律师需要工作的小时数</mat-hint>
        </mat-form-field>
      </div>
      
      <div *ngIf="result" class="result-section">
        <h2>收费标准</h2>
        <mat-divider></mat-divider>
        
        <div class="fee-range">
          <div class="fee-item">
            <span class="fee-label">最低收费：</span>
            <span class="fee-value">{{ result.hourlyRate.min.toLocaleString() }}元</span>
          </div>
          <div class="fee-item">
            <span class="fee-label">最高收费：</span>
            <span class="fee-value">{{ result.hourlyRate.max.toLocaleString() }}元</span>
          </div>
        </div>
        
        <h3>收费明细</h3>
        <mat-list>
          <mat-list-item *ngFor="let detail of result.details">
            {{ detail }}
          </mat-list-item>
        </mat-list>
      </div>
    </mat-card-content>
  </mat-card>
</div> 