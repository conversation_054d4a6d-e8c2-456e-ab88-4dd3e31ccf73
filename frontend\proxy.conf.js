const PROXY_CONFIG = [
  {
    context: ["/api"],
    target: "http://web:8000",
    changeOrigin: true,
    secure: false,
    logLevel: "debug",
    configure(proxy) {
      proxy.on("proxyReq", (proxyReq, req, res) => {
        // 将原始的Host头传递给后端
        if (req.headers.host) {
          proxyReq.setHeader('X-Forwarded-Host', req.headers.host);
        }
        console.log(`[Proxy] ${req.method} ${req.url} -> ${proxyReq.getHeader('host')} (X-Forwarded-Host: ${req.headers.host})`);
      });
    },
  },
];

module.exports = PROXY_CONFIG;