from django.db import models
from django.contrib.auth.models import User, Group
from django.utils import timezone
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

class OfficeExpense(models.Model):
    """办公费用模型"""
    expense_date = models.DateField(verbose_name="费用发生日期")
    applicant = models.ForeignKey(User, on_delete=models.CASCADE, related_name='expenses', verbose_name="申请人")
    purpose = models.CharField(max_length=100, verbose_name="用途")
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name="金额")
    description = models.TextField(blank=True, null=True, verbose_name="说明")
    approver = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_expenses', verbose_name="审批人")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    last_printed_at = models.DateTimeField(null=True, blank=True, verbose_name="最后打印时间")
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = "办公费用"
        verbose_name_plural = "办公费用"
    
    def __str__(self):
        return f"{self.applicant.username} - {self.purpose} - {self.amount}"
    
    def can_approve(self, user):
        """判断用户是否可以审批费用"""
        # 检查用户是否有主任审批权限
        return hasattr(user, 'profile') and user.profile.has_case_director_approval_permission()
    
    def approve(self, approver):
        """审批费用"""
        if self.approver is None and self.can_approve(approver):
            self.approver = approver
            self.save()
            
            # 创建对应的财务记录
            FinanceRecord.objects.create(
                transaction_date=self.expense_date,
                account_type='office_expense',
                amount=-self.amount,  # 办公费用是支出，所以为负数
                purpose=f"办公费用 - {self.purpose}",
                lawyer=self.applicant,
                remarks=self.description,
                created_by=approver,
                office_expense=self
            )
            
            return True
        return False
    
    def print_record(self):
        """更新打印时间"""
        self.last_printed_at = timezone.now()
        self.save()


class FinanceRecord(models.Model):
    """财务收支记录模型"""
    
    # 账目类型选择
    ACCOUNT_TYPE_CHOICES = [
        # 入账类型
        ('case_income', '案件收入'),
        ('lawyer_payment', '律师缴费'),
        
        # 出账类型  
        ('lawyer_withdrawal', '律师提款'),
        ('lawyer_task_expense', '律师应缴费用'),
        ('office_expense', '办公费用'),
        ('case_refund', '案件退款'),
        ('other_expense', '其他支出'),
    ]
    
    # 入账类型列表
    INCOME_TYPES = ['case_income', 'lawyer_payment']
    
    # 出账类型列表
    EXPENSE_TYPES = ['lawyer_withdrawal', 'lawyer_task_expense', 'office_expense', 'case_refund', 'other_expense']
    
    transaction_date = models.DateField(
        default=timezone.now,
        verbose_name="费用发生时间",
        help_text="默认为当天，可以修改"
    )
    
    account_type = models.CharField(
        max_length=50,
        choices=ACCOUNT_TYPE_CHOICES,
        verbose_name="账目类型"
    )
    
    amount = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name="数额",
        help_text="入账类型存储为正数，出账类型存储为负数"
    )
    
    purpose = models.CharField(
        max_length=200,
        verbose_name="用途"
    )
    
    lawyer = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='finance_records',
        verbose_name="关联律师"
    )
    
    case = models.ForeignKey(
        'cases.Case',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='finance_records',
        verbose_name="关联案件",
        help_text="可选择关联的案件"
    )
    
    remarks = models.TextField(
        blank=True,
        null=True,
        verbose_name="备注"
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name="创建时间"
    )
    
    created_by = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='created_finance_records',
        verbose_name="创建人"
    )
    
    withdrawal_request = models.OneToOneField(
        'WithdrawalRequest',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='finance_record',
        verbose_name="关联提款申请"
    )
    
    office_expense = models.OneToOneField(
        'OfficeExpense',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='finance_record',
        verbose_name="关联办公费用"
    )
    
    class Meta:
        ordering = ['-transaction_date', '-created_at']
        verbose_name = "财务收支记录"
        verbose_name_plural = "财务收支记录"
        permissions = [
            ("can_manage_finance_records", "财务管理权限"),
        ]
        
    def __str__(self):
        case_info = f" ({self.case.case_number})" if self.case else ""
        return f"{self.lawyer.username} - {self.get_account_type_display()} - {self.amount}{case_info}"
    
    def save(self, *args, **kwargs):
        """保存时检查金额正负"""
        if self.account_type in self.INCOME_TYPES:
            # 入账类型，确保为正数
            self.amount = abs(self.amount)
        elif self.account_type in self.EXPENSE_TYPES:
            # 出账类型，确保为负数
            self.amount = -abs(self.amount)
        
        super().save(*args, **kwargs)
    
    @property
    def display_amount(self):
        """UI显示用的金额（始终为正数）"""
        return abs(self.amount)
    
    @property
    def is_income(self):
        """是否为入账类型"""
        return self.account_type in self.INCOME_TYPES
    
    @property
    def is_expense(self):
        """是否为出账类型"""
        return self.account_type in self.EXPENSE_TYPES


class WithdrawalRequest(models.Model):
    STATUS_CHOICES = (
        ('PENDING', '未审批'),
        ('APPROVED', '已批准'),
        ('REJECTED', '已拒绝'),
    )
    
    case = models.ForeignKey(
        'cases.Case',
        on_delete=models.CASCADE,
        related_name='withdrawal_requests',
        verbose_name='关联案件'
    )
    requester = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='withdrawal_requests',
        verbose_name='申请人'
    )
    amount = models.DecimalField(
        max_digits=12, 
        decimal_places=2,
        verbose_name='申请金额'
    )
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES,
        default='PENDING',
        verbose_name='状态'
    )
    approver = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_withdrawals',
        verbose_name='审批人'
    )
    deduction_tax = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        default=0,
        verbose_name='应扣税费'
    )
    other_deductions = models.TextField(
        blank=True,
        null=True,
        verbose_name='请他应扣项目'
    )
    remarks = models.TextField(
        blank=True,
        null=True,
        verbose_name='备注说明'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='申请时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')
    
    class Meta:
        verbose_name = '提款申请'
        verbose_name_plural = '提款申请'
        ordering = ['-created_at']
    
    def __str__(self):
        from users.utils import get_user_display_name
        return f"{get_user_display_name(self.requester)} - {self.case.case_number} - ¥{self.amount}"

    def approve(self, approver, deduction_tax=None, other_deductions=None, remarks=None):
        """批准提款申请并创建财务记录"""
        if self.status != 'PENDING':
            return False
            
        self.status = 'APPROVED'
        self.approver = approver
        
        if deduction_tax is not None:
            from decimal import Decimal
            self.deduction_tax = Decimal(str(deduction_tax))
        if other_deductions is not None:
            self.other_deductions = other_deductions
        if remarks is not None:
            self.remarks = remarks
            
        self.save()
        
        from users.utils import get_user_display_name
        
        FinanceRecord.objects.create(
            transaction_date=timezone.now().date(),
            account_type='lawyer_withdrawal',
            amount=-self.amount,
            purpose=f"{get_user_display_name(self.requester)}律师提款 - {self.case.case_number}",
            lawyer=self.requester,
            case=self.case,
            remarks=self.remarks,
            created_by=approver,
            withdrawal_request=self
        )
        
        return True
        
    def reject(self, approver):
        """拒绝提款申请"""
        if self.status != 'PENDING':
            return False
            
        self.status = 'REJECTED'
        self.approver = approver
        self.save()
        return True
    
    @property
    def final_amount(self):
        """实际提取金额（申请金额 - 应扣税费）"""
        from decimal import Decimal
        deduction = Decimal(str(self.deduction_tax)) if self.deduction_tax else Decimal('0')
        return self.amount - deduction


# 信号处理器 - 自动更新案件付款状态
@receiver(post_save, sender=FinanceRecord)
def update_case_payment_status_on_finance_record_save(sender, instance, **kwargs):
    """当财务记录被保存时，检查并更新相关案件的付款状态"""
    if instance.case and instance.account_type in ['case_income', 'case_refund']:
        # 只有案件收入和案件退款类型的记录才会影响案件付款状态
        instance.case.check_and_update_payment_status()


@receiver(post_delete, sender=FinanceRecord)
def update_case_payment_status_on_finance_record_delete(sender, instance, **kwargs):
    """当财务记录被删除时，检查并更新相关案件的付款状态"""
    if instance.case and instance.account_type in ['case_income', 'case_refund']:
        # 只有案件收入和案件退款类型的记录才会影响案件付款状态
        instance.case.check_and_update_payment_status()
