import { ApplicationConfig, LOCALE_ID } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { routes } from './app.routes';
import { provideAnimations } from '@angular/platform-browser/animations';
import { authInterceptor } from './interceptors/auth.interceptor';
import { registerLocaleData } from '@angular/common';
import localeZh from '@angular/common/locales/zh';
import { MatPaginatorIntl } from '@angular/material/paginator';

// 注册中文locale
registerLocaleData(localeZh);

// 自定义中文分页器国际化
export class ChinesePaginatorIntl extends MatPaginatorIntl {
  override itemsPerPageLabel = '每页条数:';
  override nextPageLabel = '下一页';
  override previousPageLabel = '上一页';
  override firstPageLabel = '首页';
  override lastPageLabel = '末页';

  override getRangeLabel = (page: number, pageSize: number, length: number) => {
    if (length === 0 || pageSize === 0) {
      return `0 / ${length}`;
    }
    const startIndex = page * pageSize;
    const endIndex = startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;
    return `${startIndex + 1} – ${endIndex} / ${length}`;
  };
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(
      withInterceptors([authInterceptor])
    ),
    provideAnimations(),
    { provide: LOCALE_ID, useValue: 'zh-CN' },
    { provide: MatPaginatorIntl, useClass: ChinesePaginatorIntl }
  ]
};
