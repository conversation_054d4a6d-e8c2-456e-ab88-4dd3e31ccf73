import { Component, OnInit, Input, Output, EventEmitter, OnD<PERSON>roy } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { NgIf, NgFor, AsyncPipe } from '@angular/common';
import { Observable, Subject, of } from 'rxjs';
import { startWith, debounceTime, distinctUntilChanged, switchMap, takeUntil } from 'rxjs/operators';
import { CaseService } from '../../services/case.service';
import { Case } from '../../interfaces/case.interface';

@Component({
  selector: 'app-case-search',
  standalone: true,
  imports: [
    NgI<PERSON>, <PERSON><PERSON><PERSON>, AsyncPipe, ReactiveFormsModule,
    MatFormFieldModule, MatInputModule, MatAutocompleteModule,
    MatIconModule, MatButtonModule
  ],
  template: `
    <mat-form-field [appearance]="appearance" [style.width]="width">
      <mat-label>{{ label }}</mat-label>
      <input
        matInput
        [matAutocomplete]="auto"
        [formControl]="searchControl"
        [placeholder]="placeholder"
        [readonly]="readonly">
      <mat-icon matSuffix>search</mat-icon>
      <button mat-icon-button matSuffix *ngIf="searchControl.value && !readonly" (click)="clearSelection()">
        <mat-icon>close</mat-icon>
      </button>
      
      <mat-autocomplete 
        #auto="matAutocomplete"
        [displayWith]="displayFn"
        (optionSelected)="onCaseSelected($event.option.value)">
        <mat-option *ngFor="let caseItem of filteredCases$ | async" [value]="caseItem">
          <div class="case-option">
            <div class="case-number">{{ caseItem.case_number }}</div>
            <div class="case-cause">{{ caseItem.case_cause }}</div>
            <div class="case-clients" *ngIf="caseItem.clients && caseItem.clients.length > 0">
              <span class="clients-label">委托人：</span>
              <span class="clients-list">
                <span *ngFor="let client of caseItem.clients; let last = last">
                  {{ client.name }}
                  <span *ngIf="client.id_number" class="id-number">（{{ client.id_number }}）</span>
                  <span class="client-type">{{ client.type }}</span>{{ !last ? '、' : '' }}
                </span>
              </span>
            </div>
          </div>
        </mat-option>
        <mat-option *ngIf="(filteredCases$ | async)?.length === 0 && searchControl.value && searchControl.value.length >= 2" disabled>
          <div class="no-results">未找到匹配的案件</div>
        </mat-option>
      </mat-autocomplete>
    </mat-form-field>
  `,
  styles: [`
    .case-option {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    
    .case-number {
      font-weight: 500;
      font-size: 14px;
    }
    
    .case-cause {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
    }
    
    .case-clients {
      font-size: 11px;
      color: rgba(0, 0, 0, 0.5);
      margin-top: 2px;
    }
    
    .clients-label {
      font-weight: 500;
    }
    
    .clients-list {
      margin-left: 4px;
    }
    
    .id-number {
      color: rgba(0, 0, 0, 0.4);
      font-size: 10px;
    }
    
    .client-type {
      color: rgba(0, 0, 0, 0.4);
      font-size: 10px;
      margin-left: 2px;
    }
    
    .no-results {
      font-style: italic;
      color: rgba(0, 0, 0, 0.6);
      text-align: center;
      padding: 8px;
    }
  `]
})
export class CaseSearchComponent implements OnInit, OnDestroy {
  @Input() label: string = '搜索案件';
  @Input() placeholder: string = '输入案件号、案由或当事人姓名进行搜索';
  @Input() appearance: 'fill' | 'outline' = 'outline';
  @Input() width: string = '100%';
  @Input() readonly: boolean = false;
  @Input() initialValue: Case | null = null;
  
  @Output() caseSelected = new EventEmitter<Case | null>();
  @Output() caseNumberSelected = new EventEmitter<string>();
  
  searchControl = new FormControl();
  filteredCases$: Observable<Case[]> = of([]);
  private destroy$ = new Subject<void>();
  
  constructor(private caseService: CaseService) {}
  
  ngOnInit(): void {
    // 设置初始值
    if (this.initialValue) {
      this.searchControl.setValue(this.initialValue);
    }
    
    // 设置搜索逻辑
    this.filteredCases$ = this.searchControl.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      distinctUntilChanged(),
      switchMap(value => {
        if (typeof value === 'string' && value.length >= 2) {
          return this.caseService.searchCases(value);
        }
        return of([]);
      }),
      takeUntil(this.destroy$)
    );
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  
  displayFn = (caseItem: Case): string => {
    return caseItem ? `${caseItem.case_number} - ${caseItem.case_cause}` : '';
  };
  
  onCaseSelected(caseItem: Case): void {
    this.caseSelected.emit(caseItem);
    this.caseNumberSelected.emit(caseItem.case_number || '');
  }
  
  clearSelection(): void {
    this.searchControl.setValue('');
    this.caseSelected.emit(null);
    this.caseNumberSelected.emit('');
  }
  
  // 公开方法，允许外部设置值
  setValue(caseItem: Case | null): void {
    this.searchControl.setValue(caseItem);
  }
  
  // 公开方法，允许外部获取当前值
  getCurrentCase(): Case | null {
    const value = this.searchControl.value;
    return (value && typeof value === 'object') ? value : null;
  }
} 