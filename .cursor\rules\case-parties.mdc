---
description: 案件当事人规则
globs: 
alwaysApply: false
---

一个民事案件、行政诉讼案件和其他案件的当事人，角色可以是原告、被告和第三人
一个刑事案件的当事人，角色可以是原告、犯罪嫌疑人、嫌疑人家属和第三人。
一个案件的所有角色，都可以有多个。

当事人可以是自然人和单位。

对于自然人，有四个属性，姓名，身份证号码，手机号码和备注。
对于单位，只有三个属性，就是名称，组织机构代码和备注，其中名称唯一确定他的身份。

自然人界面应该允许编辑身份证号码，手机号码和备注。
单位界面应该允许编辑单位名称，手机号码，身份证信息，组织机构代码和备注。

一个当事人可以跟多个案件关联，并且属于原告、被告或者第三人。

案件归档之前，需要确认所有关联的当事人都已经输入了身份证信息，如果没有，在案件详情信息里应该突出提示该用户没有关联身份证号码，并在用户尝试归档时提示用户有哪些用户没有关联身份证号码。