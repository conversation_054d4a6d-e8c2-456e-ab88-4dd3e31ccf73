from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from .views import (
    CaseViewSet, NaturalPersonViewSet, LegalEntityViewSet,
    CasePartyViewSet, CaseLawyerCollaborationViewSet,
    LegacyCaseViewSet, SealUsageRecordViewSet, PrintSealUsageTemplateView,
    PrintSealUsageRecordTemplateView, LetterRecordViewSet, PrintLetterTemplateView
)

app_name = 'api'

router = DefaultRouter()
router.register(r'cases', CaseViewSet)
router.register(r'natural-persons', NaturalPersonViewSet)
router.register(r'legal-entities', LegalEntityViewSet)
router.register(r'case-parties', CasePartyViewSet)
router.register(r'case-lawyers', CaseLawyerCollaborationViewSet)
router.register(r'legacy-cases', LegacyCaseViewSet)
router.register(r'seal-usage-records', SealUsageRecordViewSet)
router.register(r'letter-records', LetterRecordViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('templates/cases/print_seal_usage.html', PrintSealUsageTemplateView.as_view(), name='print_seal_usage'),
    path('templates/cases/print_seal_usage_record/<int:record_id>/', PrintSealUsageRecordTemplateView.as_view(), name='print_seal_usage_record'),
    path('templates/cases/print_letter.html', PrintLetterTemplateView.as_view(), name='print_letter'),
    path('templates/cases/print_letter_record/<int:record_id>/', PrintLetterTemplateView.as_view(), name='print_letter_record'),
    path('templates/cases/print_letter_record/', PrintLetterTemplateView.as_view(), name='print_letter_record_query'),
] 