from rest_framework import serializers
from django.contrib.auth.models import User
from .models import OfficeExpense, FinanceRecord, WithdrawalRequest
from cases.models import Case
from django.db import models


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name']


class CaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Case
        fields = ['id', 'case_number', 'case_cause']


class OfficeExpenseSerializer(serializers.ModelSerializer):
    applicant = UserSerializer(read_only=True)
    approver = UserSerializer(read_only=True)
    can_approve = serializers.SerializerMethodField()
    
    class Meta:
        model = OfficeExpense
        fields = '__all__'
    
    def get_can_approve(self, obj):
        request = self.context.get('request')
        if request and request.user:
            return obj.can_approve(request.user)
        return False
    
    def to_representation(self, instance):
        """自定义序列化输出，将amount转换为float"""
        data = super().to_representation(instance)
        if 'amount' in data and data['amount'] is not None:
            data['amount'] = float(data['amount'])
        return data


class OfficeExpenseCreateSerializer(serializers.ModelSerializer):
    """创建办公费用的序列化器"""
    class Meta:
        model = OfficeExpense
        fields = ['expense_date', 'purpose', 'amount', 'description']
    
    def create(self, validated_data):
        validated_data['applicant'] = self.context['request'].user
        return super().create(validated_data)


class FinanceRecordSerializer(serializers.ModelSerializer):
    lawyer = UserSerializer(read_only=True)
    case = CaseSerializer(read_only=True)
    created_by = UserSerializer(read_only=True)
    office_expense = OfficeExpenseSerializer(read_only=True)
    display_amount = serializers.ReadOnlyField()
    is_income = serializers.ReadOnlyField()
    is_expense = serializers.ReadOnlyField()
    
    class Meta:
        model = FinanceRecord
        fields = '__all__'


class FinanceRecordCreateSerializer(serializers.ModelSerializer):
    """创建财务记录的序列化器"""
    lawyer_id = serializers.IntegerField(write_only=True)
    case_id = serializers.IntegerField(write_only=True, required=False, allow_null=True)
    display_amount = serializers.DecimalField(max_digits=12, decimal_places=2, write_only=True)
    
    class Meta:
        model = FinanceRecord
        fields = [
            'transaction_date', 'account_type', 'display_amount', 'purpose',
            'lawyer_id', 'case_id', 'remarks'
        ]
    
    def create(self, validated_data):
        # 处理字段映射
        lawyer_id = validated_data.pop('lawyer_id')
        case_id = validated_data.pop('case_id', None)
        display_amount = validated_data.pop('display_amount')
        
        # 设置正确的字段值
        validated_data['lawyer_id'] = lawyer_id
        validated_data['case_id'] = case_id
        validated_data['amount'] = display_amount
        validated_data['created_by'] = self.context['request'].user
        
        return super().create(validated_data)


class WithdrawalRequestSerializer(serializers.ModelSerializer):
    requester = UserSerializer(read_only=True)
    approver = UserSerializer(read_only=True)
    case = CaseSerializer(read_only=True)
    case_id = serializers.IntegerField(write_only=True)
    can_approve = serializers.SerializerMethodField()
    can_delete = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    final_amount = serializers.SerializerMethodField()
    
    class Meta:
        model = WithdrawalRequest
        fields = [
            'id', 'case', 'case_id', 'requester', 'amount', 'status', 'status_display',
            'approver', 'deduction_tax', 'other_deductions', 'remarks', 'created_at', 'updated_at', 
            'can_approve', 'can_delete', 'final_amount'
        ]
        read_only_fields = ['requester', 'approver', 'created_at', 'updated_at']
    
    def get_final_amount(self, obj):
        """获取实际提取金额"""
        return float(obj.final_amount)
    
    def get_can_approve(self, obj):
        """检查当前用户是否可以审批此申请"""
        request = self.context.get('request')
        if not request or not request.user:
            return False
        
        # 只有行政人员可以审批，且申请状态为待审批
        return (obj.status == 'PENDING' and 
                hasattr(request.user, 'profile') and 
                request.user.profile.has_case_admin_approval_permission())
    
    def get_can_delete(self, obj):
        """检查当前用户是否可以删除此申请"""
        request = self.context.get('request')
        if not request or not request.user:
            return False
        
        # 只有申请人可以删除自己的未审批申请
        return (obj.requester == request.user and obj.status == 'PENDING')
    
    def create(self, validated_data):
        """创建提款申请时自动设置申请人"""
        validated_data['requester'] = self.context['request'].user
        return super().create(validated_data)

    def validate(self, data):
        """验证提款申请数据"""
        case_id = data.get('case_id')
        amount = data.get('amount')
        request = self.context.get('request')
        
        if not request or not request.user:
            raise serializers.ValidationError("用户认证失败")
        
        # 基本金额验证
        if amount <= 0:
            raise serializers.ValidationError({"amount": "提款金额必须大于0"})
        
        # 获取案件信息
        try:
            case = Case.objects.get(id=case_id)
        except Case.DoesNotExist:
            raise serializers.ValidationError({"case_id": "案件不存在"})
        
        # 计算律师应得的分成金额
        max_withdrawal_amount = self._calculate_max_withdrawal_amount(case, request.user)
        
        if max_withdrawal_amount <= 0:
            raise serializers.ValidationError({"amount": "该案件暂无可提取金额，请确认案件已收费且您有提款权限"})
        
        if amount > max_withdrawal_amount:
            raise serializers.ValidationError({
                "amount": f"提款金额不能超过您在该案件中的可提取金额 ¥{max_withdrawal_amount:.2f}"
            })
        
        return data
    
    def _calculate_max_withdrawal_amount(self, case, user):
        """计算律师在指定案件中的最大可提取金额"""
        from cases.models import CaseLawyerCollaboration
        from decimal import Decimal
        
        # 获取案件的律师费总额
        total_lawyer_fee = self._get_total_lawyer_fee(case)
        if total_lawyer_fee <= 0:
            return Decimal('0')
        
        # 计算律师的分成比例
        lawyer_share_ratio = self._get_lawyer_share_ratio(case, user)
        if lawyer_share_ratio <= 0:
            return Decimal('0')
        
        # 计算律师应得金额
        lawyer_entitled_amount = total_lawyer_fee * Decimal(str(lawyer_share_ratio))
        
        # 计算已提取金额
        already_withdrawn = self._get_already_withdrawn_amount(case, user)
        
        # 返回可提取金额
        return max(Decimal('0'), lawyer_entitled_amount - already_withdrawn)
    
    def _get_total_lawyer_fee(self, case):
        """获取案件的律师费总额（律师所得部分，即88%）"""
        from decimal import Decimal
        
        if case.agreed_lawyer_fee:
            # 使用数据库中存储的律师费乘以88%（律师所得部分）
            return case.agreed_lawyer_fee * Decimal('0.88')
        elif case.content and case.content.get('商定律师费'):
            # 从content中解析律师费
            fee_str = case.content['商定律师费']
            try:
                # 移除可能的非数字字符
                import re
                fee_match = re.search(r'[\d.]+', str(fee_str))
                if fee_match:
                    total_fee = Decimal(fee_match.group())
                    return total_fee * Decimal('0.88')
            except (ValueError, TypeError):
                pass
        
        return Decimal('0')
    
    def _get_lawyer_share_ratio(self, case, user):
        """获取律师在案件中的分成比例"""
        from cases.models import CaseLawyerCollaboration
        from decimal import Decimal
        
        # 如果是主办律师
        if case.lawyer.id == user.id:
            # 计算协作律师的总分成比例
            collaborator_total_ratio = CaseLawyerCollaboration.objects.filter(
                case=case
            ).aggregate(
                total=models.Sum('fee_share_ratio')
            )['total'] or Decimal('0')
            
            # 主办律师分成 = 1 - 协作律师总分成
            return max(Decimal('0'), Decimal('1') - collaborator_total_ratio)
        
        # 如果是协作律师
        try:
            collaboration = CaseLawyerCollaboration.objects.get(case=case, lawyer=user)
            return collaboration.fee_share_ratio
        except CaseLawyerCollaboration.DoesNotExist:
            return Decimal('0')
    
    def _get_already_withdrawn_amount(self, case, user):
        """获取律师在该案件中已提取的金额"""
        from decimal import Decimal
        
        # 查询已批准的提款申请
        approved_withdrawals = WithdrawalRequest.objects.filter(
            case=case,
            requester=user,
            status='APPROVED'
        ).aggregate(
            total=models.Sum('amount')
        )['total'] or Decimal('0')
        
        return approved_withdrawals

    def to_representation(self, instance):
        """自定义序列化输出，将amount和deduction_tax转换为float"""
        data = super().to_representation(instance)
        if 'amount' in data and data['amount'] is not None:
            data['amount'] = float(data['amount'])
        if 'deduction_tax' in data and data['deduction_tax'] is not None:
            data['deduction_tax'] = float(data['deduction_tax'])
        return data 