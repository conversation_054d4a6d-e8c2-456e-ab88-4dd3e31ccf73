.pricing-calculator {
  max-width: 800px;
  margin: 0 auto;
  padding: 24px;
  
  h1 {
    margin-bottom: 24px;
    color: #333;
    text-align: center;
  }
}

.calculator-card {
  margin-bottom: 24px;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.case-type-radio {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  margin-bottom: 16px;
}

.full-width {
  width: 100%;
}

.result-section {
  margin-top: 24px;
  
  h2 {
    margin-bottom: 16px;
    color: #333;
  }
  
  h3 {
    margin: 24px 0 16px;
    color: #555;
  }
}

.fee-range {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 24px 0;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.fee-item {
  display: flex;
  justify-content: space-between;
}

.total-fee {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px dashed #ccc;
  
  .fee-label, .fee-value {
    font-size: 1.1em;
    font-weight: 700;
  }
  
  .fee-value {
    color: #f44336;
  }
}

.fee-label {
  font-weight: 500;
  color: #555;
}

.fee-value {
  font-weight: 700;
  color: #3f51b5;
  font-size: 1.1em;
} 